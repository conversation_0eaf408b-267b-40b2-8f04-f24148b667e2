//By default, failures thrown by the 'before all' and 'after all' hooks will not be retried.
//Therefore, we can use 'beforeEach' to ensure the steps that should be executed before each test begins are retried by failure, as Cypress will automatically retry it.
export const retryableBefore = (beforePrepare: () => void) => {
  let shouldRetry = true;

  beforeEach(() => {
    if (!shouldRetry) return;
    shouldRetry = false;
    beforePrepare();
  });

  Cypress.on('test:after:run', (result) => {
    if (result.state === 'failed' && result.currentRetry < result.retries) {
      shouldRetry = true;
    }
  });
};
