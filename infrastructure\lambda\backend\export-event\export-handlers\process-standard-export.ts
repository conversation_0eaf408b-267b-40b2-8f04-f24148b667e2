import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { KafkaAdapter } from '../../../utils/kafka';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { NewCarOrderAuditTrailModel } from '../../../../lib/entities/new-car-order-audit-trail-model';
import {
  ExportResult,
  KafkaObj,
  KafkaObjsWrapper,
  KafkaObjTyp,
  NcoExportActionType,
  NewCarOrderKafkaObject,
} from '../../export-nco/types';
import { flatToKafkaExportNewCarOrderObj } from './utils';
import { NotificationKafkaEvent } from '../../../../lib/types/process-steering-types';

const KAFKA_TOPIC_ONE_VMS = getEnvVarWithAssert('KAFKA_TOPIC_ONE_VMS');

export async function processStandardExport(
  notification: NotificationKafkaEvent,
  auditTrails: NewCarOrderAuditTrailModel[],
  kafkaAdapter: KafkaAdapter,
  logger: KasLambdaLogger,
): Promise<ExportResult> {
  const ncoId = notification.nco_id!;
  const correlationId = notification.transaction_id;
  const defaultResult: ExportResult = {
    exportedIds: [],
    failed: false,
  };

  if (auditTrails.length === 0) {
    logger.log(LogLevel.INFO, `No unexported audit trails found for object_id=${ncoId}`);
    return defaultResult;
  }
  try {
    const kafkaExportObjs: KafkaObj<NewCarOrderKafkaObject>[] = [];

    for (const auditTrail of auditTrails) {
      logger.setObjectId(auditTrail.pk_new_car_order_id);

      if (!auditTrail.new_nco && !auditTrail.old_nco) {
        logger.log(LogLevel.ERROR, 'Invalid audit trail – missing new/old object', { data: auditTrail });
        continue;
      }

      kafkaExportObjs.push({
        id: auditTrail.pk_new_car_order_id,
        kafkaActionTyp: auditTrail.action_type,
        correlationid: auditTrail.action_correlation_id,
        importer: auditTrail.new_nco?.importer_number ?? auditTrail.old_nco?.importer_number,
        obj: flatToKafkaExportNewCarOrderObj(auditTrail.new_nco ?? auditTrail.old_nco!),
      });
    }

    if (kafkaExportObjs.length === 0) {
      logger.log(LogLevel.INFO, `No valid export objects found after processing`);
      return { exportedIds: [], failed: false };
    }

    const kafkaObjsWrapper: KafkaObjsWrapper<NewCarOrderKafkaObject> = {
      kafkaObjTyp: KafkaObjTyp.NCO,
      kafkaObjs: kafkaExportObjs.map((kafkaExportObj) => {
        const _kafkaItemOneVms = { ...kafkaExportObj };
        delete _kafkaItemOneVms.obj.configuration_expire;
        return _kafkaItemOneVms;
      }),
      kafkaActionTyp: NcoExportActionType.UPDATE, //will be overwritten by individual item action type
    };

    logger.log(LogLevel.DEBUG, `Exporting ${kafkaExportObjs.length} object(s) to ${KAFKA_TOPIC_ONE_VMS}`);

    await kafkaAdapter.pushObjsToTopic({
      topic: KAFKA_TOPIC_ONE_VMS,
      kWrapper: kafkaObjsWrapper,
      correlationid: correlationId,
    });

    logger.log(LogLevel.INFO, `Successfully exported audit trails to NCO topic.`);
    return {
      exportedIds: [...new Set(kafkaExportObjs.map((obj) => obj.id))],
      failed: false,
    };
  } catch (err) {
    logger.log(LogLevel.ERROR, 'Standard export to NCO topic failed', { data: err });
    return {
      exportedIds: [],
      failed: true,
      error: err instanceof Error ? err : new Error('Unknown NCO export error'),
    };
  }
}
