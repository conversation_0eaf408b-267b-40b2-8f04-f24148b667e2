import {
  Stack,
  aws_secretsmanager as SecretsManager,
  aws_ec2 as ec2,
  aws_rds as rds,
  aws_iam as iam,
  aws_logs as logs,
  aws_ssm as ssm,
  Duration,
  aws_ssm,
} from 'aws-cdk-lib';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Construct } from 'constructs';
import { GlobalStack } from './global-stack';
import { Constants } from '../utils/constants';
import { KasKmsKey, KasSecret, KasSnsTopic, KasStage } from '@kas-resources/constructs';
import { CfnEventSubscription, ClusterInstance, ServerlessCluster } from 'aws-cdk-lib/aws-rds';
import { KasBastionHostForward } from '@kas-resources/constructs';
import { IFunction, LayerVersion } from 'aws-cdk-lib/aws-lambda';
import {
  AwsCustomResource,
  AwsCustomResourcePolicy,
  AwsSdkCall,
  PhysicalResourceId,
} from 'aws-cdk-lib/custom-resources';
import { SetupAuroraGlobalLambda } from '../constructs/setup-aurora-global-lambda';
import { Effect, PolicyStatement, Role, ServicePrincipal } from 'aws-cdk-lib/aws-iam';
import { EmailSubscription } from 'aws-cdk-lib/aws-sns-subscriptions';
import { KasScaleAuroraMinACUConstruct } from '@kas-resources/constructs/src/lib/scale-aurora-min-acu/scale-aurora-min-acu-construct';
import { KasScheduleKafkaTriggerStateConstruct } from '@kas-resources/constructs/src/lib/schedule-kafka-trigger-state/schedule-kafka-trigger-state-construct';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';

export class AuroraDatabaseStack extends Stack {
  private readonly auroraAdminSecret: KasSecret;
  private readonly auroraReaderSecret: KasSecret;
  private readonly auroraWriterSecret: KasSecret;
  private readonly coraMdAuroraReaderSecret: KasSecret;
  private readonly coraMdAuroraWriterSecret: KasSecret;

  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    //By setting db password the following character will be excluded
    const PASSWORD_EXCLUDE_CHARACTERS = ':@/" \'';

    //Import Security Group for the RDS Database
    const auroraSecurityGroupID = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraSecurityGroupPName,
    );
    const auroraSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraSecurityGroup',
      auroraSecurityGroupID,
    );

    const auroraAccessSecurityGroupID = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    auroraSecurityGroup.addIngressRule(
      ec2.Peer.securityGroupId(auroraAccessSecurityGroup.securityGroupId),
      ec2.Port.tcp(Constants.AURORA_DB_PORT),
      'Allow DB access from aurora access sg',
    );

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    const rotateAuroraLambdaSecurityGroup = new ec2.SecurityGroup(this, 'AuroraAccessSgRotateLambda', {
      vpc: props.vpc,
      allowAllOutbound: false,
      securityGroupName: 'AuroraAccessSgRotateLambda',
    });
    rotateAuroraLambdaSecurityGroup.addEgressRule(
      ec2.Peer.securityGroupId(auroraSecurityGroup.securityGroupId),
      ec2.Port.tcp(Constants.AURORA_DB_PORT),
      'allow access to aurora cluster',
    );
    rotateAuroraLambdaSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(Constants.VPC_CIDR),
      ec2.Port.tcp(443),
      'allow access to internal VPC resources',
    );
    rotateAuroraLambdaSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6fa54006'),
      ec2.Port.tcp(443),
      'allow access to DynamoDB',
    );
    rotateAuroraLambdaSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6da54004'),
      ec2.Port.tcp(443),
      'allow access to S3',
    );
    auroraSecurityGroup.addIngressRule(
      ec2.Peer.securityGroupId(rotateAuroraLambdaSecurityGroup.securityGroupId),
      ec2.Port.tcp(Constants.AURORA_DB_PORT),
      'Allow DB access from ' + id,
    );

    //Import global logGroup KMS key
    const logGroupKmsKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    const globalSecretKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const secretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    const coraMdSecretKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalMdSecretKmsKeyArnPName,
    );
    const mdSecretKey = KasKmsKey.fromKeyArn(this, 'mdSecretKey', coraMdSecretKeyArn);

    // Aurora Admin secret
    this.auroraAdminSecret = new KasSecret(this, 'AuroraAdminSecret', {
      secretName: 'cora-aurora-admin-secret',
      description: 'AuroraDB password for admin access',
      encryptionKey: secretKey,
      generateSecretString: {
        secretStringTemplate: '{"username" : "postgresAdmin"}',
        generateStringKey: 'password',
        excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
        passwordLength: 32,
        requireEachIncludedType: true,
      },
    });

    //Parameters Group to stay Building Block compliant
    const dbClusterParameterGroup = new rds.ParameterGroup(this, 'AuroraClusterParameterGroup', {
      engine: rds.DatabaseClusterEngine.auroraPostgres({
        version: rds.AuroraPostgresEngineVersion.VER_16_3,
      }),
      description: 'cora-aurora-postgresql-parametergroup',
      parameters: {
        'pgaudit.log': 'all',
        'pgaudit.role': 'rds_pgaudit',
        shared_preload_libraries: 'pgaudit',
        timezone: 'Europe/Berlin',
        'rds.force_ssl': '1',
        log_min_messages: 'log',
        log_min_error_statement: 'log',
        log_connections: '1',
        log_disconnections: '1',
        'rds.log_retention_period': '10080',
        client_min_messages: 'warning',
        log_rotation_age: '1440',
        log_filename: 'postgresql.log.%Y-%m-%d',
        lc_messages: 'en_US.UTF-8',
        lc_monetary: 'en_US.UTF-8',
        lc_numeric: 'en_US.UTF-8',
        lc_time: 'en_US.UTF-8',
      },
    });

    const dbParameterGroup = new rds.ParameterGroup(this, 'AuroraDbParameterGroup', {
      engine: rds.DatabaseClusterEngine.auroraPostgres({
        version: rds.AuroraPostgresEngineVersion.VER_16_3,
      }),
      description: 'aurora-postgresql16',
    });

    //KMS Key to encrypt the Database
    const rdsKmsKeyId = Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_AURORA_DATABASE_NAME);
    const rdsKmsKey = new KasKmsKey(this, rdsKmsKeyId);
    rdsKmsKey.grantEncryptDecrypt(new iam.ServicePrincipal('rds.amazonaws.com'));

    //Create the Aurora Serverless DB cluster
    const dbCluster = new rds.DatabaseCluster(this, 'AuroraDbCluster', {
      engine: rds.DatabaseClusterEngine.auroraPostgres({
        version: rds.AuroraPostgresEngineVersion.VER_16_3,
      }),
      parameterGroup: dbClusterParameterGroup,
      vpc: props.vpc,
      securityGroups: [auroraSecurityGroup],
      cloudwatchLogsExports: ['postgresql'],
      cloudwatchLogsRetention: logs.RetentionDays.ONE_MONTH,
      monitoringInterval: Duration.seconds(60),
      enableDataApi: true,
      clusterIdentifier: Constants.AURORA_CLUSTER_NAME,
      copyTagsToSnapshot: true,
      credentials: rds.Credentials.fromSecret(this.auroraAdminSecret),
      defaultDatabaseName: Constants.AURORA_DB_NAME,
      deletionProtection: true,
      storageEncryptionKey: rdsKmsKey,
      serverlessV2MinCapacity: props.stage === 'prod' ? 1 : 0,
      serverlessV2MaxCapacity: 4,
      backup: {
        retention: Duration.days(30),
      },
      readers: [
        ClusterInstance.serverlessV2('reader', {
          instanceIdentifier: 'reader-1-serverless-cora-auroradb-cluster',
          allowMajorVersionUpgrade: false,
          autoMinorVersionUpgrade: true,
          enablePerformanceInsights: true,
          parameterGroup: dbParameterGroup,
          publiclyAccessible: false,
          scaleWithWriter: true,
        }),
      ],
      writer: ClusterInstance.serverlessV2('writer', {
        instanceIdentifier: 'writer-serverless-cora-auroradb-cluster',
        allowMajorVersionUpgrade: false,
        autoMinorVersionUpgrade: true,
        enablePerformanceInsights: true,
        parameterGroup: dbParameterGroup,
        publiclyAccessible: false,
      }),
    });

    //Create secret for Cora WRITER credentials
    this.auroraWriterSecret = new KasSecret(this, 'AuroraWriterSecret', {
      secretName: 'cora-aurora-writer-secret',
      description: 'AuroraDB password for writer access of CORA',
      encryptionKey: secretKey,
      generateSecretString: {
        generateStringKey: 'password',
        excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
        passwordLength: 32,
        requireEachIncludedType: true,
        secretStringTemplate: `
              {
                "username": "corawriter",
                "dbname": "${Constants.AURORA_DB_NAME}",
                "port": ${Constants.AURORA_DB_PORT},
                "engine": "postgres",
                "dbClusterIdentifier": "${Constants.AURORA_CLUSTER_NAME}",
                "host": "${dbCluster.clusterEndpoint.hostname}"
              }
            `,
      },
    });
    this.auroraWriterSecret.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.DENY,
        principals: [new iam.AnyPrincipal()],
        actions: ['*'],
        resources: [this.auroraWriterSecret.secretArn],
        conditions: { StringNotEquals: { 'aws:PrincipalAccount': [props.env.account] } },
      }),
    );

    //Create secret for Cora MD WRITER credentials
    this.coraMdAuroraWriterSecret = new KasSecret(this, 'MdAuroraWriterSecret', {
      secretName: 'cora-md-aurora-writer-secret',
      description: 'AuroraDB password for writer access of Cora MD',
      encryptionKey: mdSecretKey,
      generateSecretString: {
        generateStringKey: 'password',
        excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
        passwordLength: 32,
        requireEachIncludedType: true,
        secretStringTemplate: `
              {
                "username": "coramdwriter",
                "dbname": "${Constants.AURORA_DB_NAME}",
                "port": ${Constants.AURORA_DB_PORT},
                "engine": "postgres",
                "dbClusterIdentifier": "${Constants.AURORA_CLUSTER_NAME}",
                "host": "${dbCluster.clusterEndpoint.hostname}"
              }
            `,
      },
    });
    this.coraMdAuroraWriterSecret.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.DENY,
        principals: [new iam.AnyPrincipal()],
        actions: ['*'],
        resources: [this.coraMdAuroraWriterSecret.secretArn],
        conditions: { StringNotEquals: { 'aws:PrincipalAccount': [props.env.account] } },
      }),
    );

    //Rotate credentials of WRITERs every 30 days
    new SecretsManager.SecretRotation(this, 'AuroraWriterPasswordRotation', {
      application: SecretsManager.SecretRotationApplication.POSTGRES_ROTATION_SINGLE_USER,
      secret: this.auroraWriterSecret,
      target: dbCluster,
      vpc: props.vpc,
      automaticallyAfter: Duration.days(30),
      excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
      securityGroup: rotateAuroraLambdaSecurityGroup,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
    });

    new SecretsManager.SecretRotation(this, 'MdAuroraWriterPasswordRotation', {
      application: SecretsManager.SecretRotationApplication.POSTGRES_ROTATION_SINGLE_USER,
      secret: this.coraMdAuroraWriterSecret,
      target: dbCluster,
      vpc: props.vpc,
      automaticallyAfter: Duration.days(30),
      excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
      securityGroup: rotateAuroraLambdaSecurityGroup,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_MD_SERVICES },
    });

    //Create secret for CORA READER credentials
    this.auroraReaderSecret = new KasSecret(this, 'AuroraReaderSecret', {
      secretName: 'cora-aurora-reader-secret',
      description: 'AuroraDB password for reader access of CORA',
      encryptionKey: secretKey,
      generateSecretString: {
        generateStringKey: 'password',
        excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
        passwordLength: 32,
        requireEachIncludedType: true,
        secretStringTemplate: `
                {
                  "username": "corareader",
                  "dbname": "${Constants.AURORA_DB_NAME}",
                  "port": ${Constants.AURORA_DB_PORT},
                  "engine": "postgres",
                  "dbClusterIdentifier": "${Constants.AURORA_CLUSTER_NAME}",
                  "host": "${dbCluster.clusterEndpoint.hostname}"
                }
              `,
      },
    });
    this.auroraReaderSecret.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.DENY,
        principals: [new iam.AnyPrincipal()],
        actions: ['*'],
        resources: [this.auroraReaderSecret.secretArn],
        conditions: { StringNotEquals: { 'aws:PrincipalAccount': [props.env.account] } },
      }),
    );

    //Create secret for CORA MD READER credentials
    this.coraMdAuroraReaderSecret = new KasSecret(this, 'MdAuroraReaderSecret', {
      secretName: 'cora-md-aurora-reader-secret',
      description: 'AuroraDB password for reader access of CORA MD',
      encryptionKey: mdSecretKey,
      generateSecretString: {
        generateStringKey: 'password',
        excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
        passwordLength: 32,
        requireEachIncludedType: true,
        secretStringTemplate: `
                {
                  "username": "coramdreader",
                  "dbname": "${Constants.AURORA_DB_NAME}",
                  "port": ${Constants.AURORA_DB_PORT},
                  "engine": "postgres",
                  "dbClusterIdentifier": "${Constants.AURORA_CLUSTER_NAME}",
                  "host": "${dbCluster.clusterEndpoint.hostname}"
                }
              `,
      },
    });
    this.coraMdAuroraReaderSecret.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.DENY,
        principals: [new iam.AnyPrincipal()],
        actions: ['*'],
        resources: [this.coraMdAuroraReaderSecret.secretArn],
        conditions: { StringNotEquals: { 'aws:PrincipalAccount': [props.env.account] } },
      }),
    );

    //Rotate credentials of READERs every 30 days
    new SecretsManager.SecretRotation(this, 'AuroraReaderPasswordRotation', {
      application: SecretsManager.SecretRotationApplication.POSTGRES_ROTATION_SINGLE_USER,
      secret: this.auroraReaderSecret,
      target: dbCluster,
      vpc: props.vpc,
      automaticallyAfter: Duration.days(30),
      excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
      securityGroup: rotateAuroraLambdaSecurityGroup,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
    });

    new SecretsManager.SecretRotation(this, 'MdAuroraReaderPasswordRotation', {
      application: SecretsManager.SecretRotationApplication.POSTGRES_ROTATION_SINGLE_USER,
      secret: this.coraMdAuroraReaderSecret,
      target: dbCluster,
      vpc: props.vpc,
      automaticallyAfter: Duration.days(30),
      excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
      securityGroup: rotateAuroraLambdaSecurityGroup,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_MD_SERVICES },
    });

    //Rotate credentials of ADMIN every 30 days
    new SecretsManager.SecretRotation(this, 'AuroraAdminPasswordRotation', {
      application: SecretsManager.SecretRotationApplication.POSTGRES_ROTATION_SINGLE_USER,
      secret: this.auroraAdminSecret,
      target: dbCluster,
      vpc: props.vpc,
      automaticallyAfter: Duration.days(30),
      excludeCharacters: PASSWORD_EXCLUDE_CHARACTERS,
      securityGroup: rotateAuroraLambdaSecurityGroup,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
    });

    const serverlessCluster = ServerlessCluster.fromServerlessClusterAttributes(this, 'ServerlessAurora', {
      clusterIdentifier: dbCluster.clusterIdentifier,
      port: Constants.AURORA_DB_PORT,
      clusterEndpointAddress: dbCluster.clusterEndpoint.hostname,
      securityGroups: [auroraSecurityGroup],
    });

    // The bastion host will only be used on the 'dev' stage for convenience of the developers.
    // For all other stages the Data API by RDS will be used in order to perform migrations.
    if (props.stage.toLowerCase() === 'dev') {
      const bastionHostSecurityGroup = new ec2.SecurityGroup(this, 'AuroraAccessSgBastionHost', {
        vpc: props.vpc,
        allowAllOutbound: false,
        securityGroupName: 'AuroraAccessSgBastionHost',
      });
      bastionHostSecurityGroup.addEgressRule(
        ec2.Peer.securityGroupId(auroraSecurityGroup.securityGroupId),
        ec2.Port.tcp(Constants.AURORA_DB_PORT),
        'allow access to aurora cluster',
      );
      bastionHostSecurityGroup.addEgressRule(
        ec2.Peer.ipv4(Constants.VPC_CIDR),
        ec2.Port.tcp(443),
        'allow access to internal VPC resources',
      );
      bastionHostSecurityGroup.addEgressRule(
        ec2.Peer.prefixList('pl-6fa54006'),
        ec2.Port.tcp(443),
        'allow access to DynamoDB',
      );
      bastionHostSecurityGroup.addEgressRule(
        ec2.Peer.prefixList('pl-6da54004'),
        ec2.Port.tcp(443),
        'allow access to S3',
      );
      auroraSecurityGroup.addIngressRule(
        ec2.Peer.securityGroupId(bastionHostSecurityGroup.securityGroupId),
        ec2.Port.tcp(Constants.AURORA_DB_PORT),
        'Allow DB access from ' + id,
      );

      //Retrieve Session Manager KMS Key ARN
      const sessionManagerKmsKeyArn = ssm.StringParameter.valueForStringParameter(
        this,
        props.globalParameterNames.globalSessionManagerKmsKeyArnPname,
      );
      const sessionManagerKey = KasKmsKey.fromKeyArn(this, 'sessionManagerKey', sessionManagerKmsKeyArn);

      //Create custom role for bastion host
      const bastionHostRole = new iam.Role(this, 'CoraAuroraBastionHostRole', {
        assumedBy: new iam.ServicePrincipal('ec2.amazonaws.com'),
        managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore')],
        inlinePolicies: {
          //Add missing actions to bastion host role for writing logs to CloudWatch, for S3 and for access to KMS key
          CoraSsmAgentLogsAndKeyPolicy: new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                  'logs:CreateLogStream',
                  'logs:PutLogEvents',
                  'logs:DescribeLogGroups',
                  'logs:DescribeLogStreams',
                ],
                resources: ['*'],
              }),
              new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:PutObject'],
                resources: [
                  `arn:aws:s3:::porsche-compliance-log-${props.env.account}-${props.env.region}/session_manager/*`,
                ],
              }),
              new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:GetEncryptionConfiguration'],
                resources: [`arn:aws:s3:::porsche-compliance-log-${props.env.account}-${props.env.region}`],
              }),
              new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:GenerateDataKey'],
                resources: [sessionManagerKey.keyArn],
              }),
            ],
          }),
        },
      });

      new KasBastionHostForward(this, 'CoraAuroraBastionHost', {
        vpc: props.vpc,
        serverlessCluster,
        securityGroup: bastionHostSecurityGroup,
        name: 'CoraAuroraBastionHost',
        region: props.env.region!,
        role: bastionHostRole,
        subnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      });
    }

    //Create Custom Resource for init scripts
    const setupAuroraCR = this.setupAuroraCustomResource(
      props.vpc,
      auroraSecurityGroup,
      logGroupKey,
      logSubscriptionLambda,
      props.globalParameterNames.typeORMLambdaLayerArnPName,
      props.stage,
    );
    setupAuroraCR.node.addDependency(dbCluster);

    //create sns health monitoring topic and event subscription for some random rds events as required by BB
    const healthMonitoringTopicKms = new KasKmsKey(this, 'CoraAuroraHealthMonitoringTopicKms');
    healthMonitoringTopicKms.grantEncryptDecrypt(new iam.ServicePrincipal('sns.amazonaws.com'));
    healthMonitoringTopicKms.grantEncryptDecrypt(new iam.ServicePrincipal('events.rds.amazonaws.com'));

    const healthMonitoringTopic = new KasSnsTopic(this, 'CoraAuroraHealthMonitoringTopic', {
      displayName: 'CoraAuroraHealthMonitoringTopic',
      masterKey: healthMonitoringTopicKms,
    });
    healthMonitoringTopic.addSubscription(new EmailSubscription(Constants.MONITORING_EMAIL));

    healthMonitoringTopic.addToResourcePolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        resources: ['*'],
        principals: [new ServicePrincipal('events.rds.amazonaws.com')],
        actions: ['sns:Publish'],
        conditions: {
          ArnLike: {
            'aws:SourceArn': `arn:aws:rds:${props.env.region}:${props.env.account}:db:${Constants.AURORA_CLUSTER_NAME}*`,
          },
        },
      }),
    );

    new CfnEventSubscription(this, 'CoraAuroraEventSubscription', {
      snsTopicArn: healthMonitoringTopic.topicArn,
      subscriptionName: 'CoraAuroraEventSubscription',
      eventCategories: ['creation', 'failover'],
      sourceType: 'db-cluster',
    });

    new aws_ssm.StringParameter(this, 'aurora-admin-secret-arn-param', {
      description: 'CORA Aurora Admin Secret ARN parameter',
      parameterName: props.globalParameterNames.auroraAdminSecretArnPName,
      stringValue: this.auroraAdminSecret.secretArn,
    });

    new aws_ssm.StringParameter(this, 'cora-aurora-db-writer-secret-arn-param', {
      description: 'CORA Aurora Writer Secret ARN parameter',
      parameterName: props.globalParameterNames.coraAuroraWriterSecretArnPName,
      stringValue: this.auroraWriterSecret.secretArn,
    });

    new aws_ssm.StringParameter(this, 'cora-md-aurora-db-writer-secret-arn-param', {
      description: 'CORA-MD Aurora Writer Secret ARN parameter',
      parameterName: props.globalParameterNames.coraMdAuroraWriterSecretArnPName,
      stringValue: this.coraMdAuroraWriterSecret.secretArn,
    });

    new aws_ssm.StringParameter(this, 'cora-aurora-db-reader-secret-arn-param', {
      description: 'CORA Aurora Reader Secret ARN parameter',
      parameterName: props.globalParameterNames.coraAuroraReaderSecretArnPName,
      stringValue: this.auroraReaderSecret.secretArn,
    });

    new aws_ssm.StringParameter(this, 'aurora-cluster-arn-param', {
      description: 'CORA Aurora Serverless Cluster ARN parameter',
      parameterName: props.globalParameterNames.auroraClusterArnPName,
      stringValue: serverlessCluster.clusterArn,
    });

    new aws_ssm.StringParameter(this, 'cora-md-aurora-db-reader-secret-arn-param', {
      description: 'CORA-MD Aurora Reader Secret ARN parameter',
      parameterName: props.globalParameterNames.coraMdAuroraReaderSecretArnPName,
      stringValue: this.coraMdAuroraReaderSecret.secretArn,
    });

    if (props.stage !== 'prod') {
      new KasScaleAuroraMinACUConstruct(this, 'CoraScaleACU', {
        region: props.env.region!,
        accountId: props.env.account!,
        stage: props.stage,
        logsCustomManagedKey: logGroupKey,
        clusterIdentifier: dbCluster.clusterIdentifier,
      });

      const kccConfigResponseConsumerArn = `arn:aws:lambda:${props.env.region}:${props.env.account}:function:${Constants.APPLICATION_SHORT_NAME}-${props.stage}-${Constants.KCC_CONFIG_RESPONSE_CONSUMER}`;
      new KasScheduleKafkaTriggerStateConstruct(this, 'CoraKafkaTrigger', {
        region: props.env.region!,
        accountId: props.env.account!,
        stage: props.stage,
        logsCustomManagedKey: logGroupKey,
        kafkaConsumerArns: [kccConfigResponseConsumerArn],
      });
    }
  }

  //Setup Custom Resource to execute init AuroraDB scripts while deploying
  private setupAuroraCustomResource(
    vpc: ec2.IVpc,
    auroraSg: ec2.ISecurityGroup,
    logGroupKey: KasKmsKey,
    logSubscriptionLambda: IFunction,
    typeORMLambdaLayerArnPName: string,
    stage: KasStage,
  ): AwsCustomResource {
    const id = 'DdlGlobal';
    const auroraSetupLambdaSg = new ec2.SecurityGroup(this, 'AuroraAccessSgSetupLambda', {
      vpc,
      allowAllOutbound: false,
      securityGroupName: 'AuroraAccessSgSetupLambda',
    });
    auroraSetupLambdaSg.addEgressRule(
      ec2.Peer.securityGroupId(auroraSg.securityGroupId),
      ec2.Port.tcp(Constants.AURORA_DB_PORT),
      'allow access to aurora cluster',
    );
    auroraSetupLambdaSg.addEgressRule(
      ec2.Peer.ipv4(Constants.VPC_CIDR),
      ec2.Port.tcp(443),
      'allow access to internal VPC resources',
    );
    auroraSetupLambdaSg.addEgressRule(
      ec2.Peer.prefixList('pl-6fa54006'),
      ec2.Port.tcp(443),
      'allow access to DynamoDB',
    );
    auroraSetupLambdaSg.addEgressRule(ec2.Peer.prefixList('pl-6da54004'), ec2.Port.tcp(443), 'allow access to S3');
    auroraSg.addIngressRule(
      ec2.Peer.securityGroupId(auroraSetupLambdaSg.securityGroupId),
      ec2.Port.tcp(Constants.AURORA_DB_PORT),
      'Allow DB access from ' + id,
    );
    const functionName = `${Constants.APPLICATION_SHORT_NAME}-${stage}-setup-aurora-db`;

    //TypeORM Lambda Layer
    const typeORMLayerArn = StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeORMLayer = LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    const auroraSetupLambda = new SetupAuroraGlobalLambda(this, 'SetupAuroraGlobalLambda', {
      vpc: vpc,
      auroraAdminSecret: this.auroraAdminSecret,
      coraAuroraReaderSecret: this.auroraReaderSecret,
      coraMdAuroraReaderSecret: this.coraMdAuroraReaderSecret,
      coraAuroraWriterSecret: this.auroraWriterSecret,
      coraMdAuroraWriterSecret: this.coraMdAuroraWriterSecret,
      stage: stage,
      securityGroup: auroraSetupLambdaSg,
      customManagedKey: logGroupKey,
      typeormLayer: typeORMLayer,
      logSubscriptionLambda: logSubscriptionLambda,
      functionName,
    });

    const sdkCall: AwsSdkCall = {
      service: 'Lambda',
      action: 'invoke',
      parameters: {
        FunctionName: functionName,
      },
      physicalResourceId: PhysicalResourceId.of(
        `${functionName}-${auroraSetupLambda.currentVersion.version}-${Date.now()}`,
      ),
    };

    const customResourceFnRole = new Role(this, functionName + 'Role', {
      assumedBy: new ServicePrincipal('lambda.amazonaws.com'),
    });
    customResourceFnRole.addToPolicy(
      new PolicyStatement({
        resources: [auroraSetupLambda.functionArn],
        actions: ['lambda:InvokeFunction'],
      }),
    );
    return new AwsCustomResource(this, 'CR' + functionName, {
      policy: AwsCustomResourcePolicy.fromSdkCalls({ resources: AwsCustomResourcePolicy.ANY_RESOURCE }),
      onUpdate: sdkCall,
      timeout: Duration.minutes(5),
      role: customResourceFnRole,
    });
  }
}
