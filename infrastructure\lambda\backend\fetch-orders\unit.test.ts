import {
  BatchGetCommand,
  BatchGetCommandOutput,
  QueryCommand,
  QueryCommandOutput,
  ScanCommand,
} from '@aws-sdk/lib-dynamodb';
import { generateApiGatewayEvent, mockContext, setupMocks } from '../../utils/test-utils';
import { APIGatewayProxyResult } from 'aws-lambda';
import mdOneVmsStatusJson from '../../../test/data/masterdata-onevms-status.json';

const mocks = setupMocks();
import { handler } from '.';
import { CoraNCOQueryApiResponse } from '../../../lib/types/new-car-order-types';

beforeEach(() => {
  mocks.ddbMock!.reset();
  mocks.smMock!.reset();
  jest.resetModules();
});

const orgRelFailFetchResult: QueryCommandOutput = {
  Items: [],
  $metadata: {},
};

const orgRelFetchResult: QueryCommandOutput = {
  Items: [
    {
      dealer_number: '1234',
      importer_number: 'importer1',
      is_deactivated: false,
      is_relevant_for_order_create: true,
    },
  ],
  $metadata: {},
};

const mtvResult: BatchGetCommandOutput = {
  Responses: {
    [process.env.TABLE_NAME_MODEL_TYPE_VISIBILITY ?? '']: [
      {
        importer_number_role: 'importer1_DLR',
        model_type_my4_cnr: '98231_2024_C02',
        importer_number: 'importer1',
        cnr: 'C02',
        model_type: '98231',
        my4: 2024,
        role: 'string',
        valid_from: '2022-01-01',
      },
    ],
  },
  $metadata: {},
};

describe('Fetch Orders Lambda should return Forbidden for a user without permissions', () => {
  it('return user has no access', async () => {
    mocks
      .ddbMock!.on(QueryCommand, {
        TableName: process.env.TABLE_NAME_ORG_RELS ?? '',
      })
      .resolves(orgRelFailFetchResult);
    const res = await handler(generateApiGatewayEvent({ body: JSON.stringify({}) }), mockContext, () => {});
    console.log('res', res);

    expect(res?.statusCode).toBe(403);
  });
});

describe('Fetch Orders Lambda should return data based on access', () => {
  it('Should succeed for user with access to 1 dealer', async () => {
    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS ?? '' }).resolves(orgRelFetchResult);

    mocks
      .ddbMock!.on(ScanCommand, { TableName: process.env.TABLE_NAME_STATUS_MAPPING })
      .resolves({ Items: mdOneVmsStatusJson });

    mocks.ddbMock!.on(BatchGetCommand, {}).resolves(mtvResult);

    const res = await handler(generateApiGatewayEvent({ body: JSON.stringify({}) }), mockContext, () => {});
    console.error('res', res);

    expect(res?.statusCode).toBe(200);
    expect(res?.body.length).toBeGreaterThan(1);
    const objResult = JSON.parse((res as APIGatewayProxyResult).body) as CoraNCOQueryApiResponse;
    // No more filtering after getMany, query is filtered but not in mock
    expect(objResult.data.length).toBe(3);
  });
});
