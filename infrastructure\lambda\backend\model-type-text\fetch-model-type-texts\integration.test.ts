/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { DataSource, Repository } from 'typeorm';
import { ModelTypeTextModel } from '../../../../lib/entities/model-type-text-model';

const lambdaArn = buildLambdaArn('fetch-model-type-texts');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);

const importerOrgId1 = '9ccc116a-d204-4111-aafe-8d6e43975c3e';
const importerOrgId2 = '509c29f8-18de-4bfe-9dae-28bc1e10f151';
const dealerGroupOrgId1 = '1412e4ad-d070-4891-ad89-7fd2e6183808';
const dealerGroupOrgId2 = '6e7cce98-4f53-489b-b499-91d5caf2b2eb';
const dealerOrgId1 = '881462a9-c83a-4aad-9995-a28a54ecac07';
const dealerOrgId2 = 'ab2a496b-5bb3-470b-b962-100e8f9d36e8';
const dealerOrgIdInactive = '28ea6869-c7db-42a4-aea2-935263883a9e';

const orgRels: CoraOrgRelModel[] = [
  //importer 1 relation to itself
  {
    pk_ppn_id: importerOrgId1,
    parent_ppn_id: importerOrgId1,
    dealer_number: 'ItImp11',
    display_name: 'IntegrationTest - Importer11',
    importer_number: 'ItImp11',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //importer 2 relation to itself
  {
    pk_ppn_id: importerOrgId2,
    parent_ppn_id: importerOrgId2,
    dealer_number: 'ItImp22',
    display_name: 'IntegrationTest - Importer22',
    importer_number: 'ItImp22',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 1 relation to parent Importer11
  {
    pk_ppn_id: dealerGroupOrgId1,
    parent_ppn_id: importerOrgId1,
    display_name: 'IntegrationTest - Dealer Group1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 1 relation to itself
  {
    pk_ppn_id: dealerGroupOrgId1,
    parent_ppn_id: dealerGroupOrgId1,
    display_name: 'IntegrationTest - Dealer Group1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 2 relation to parent Importer22
  {
    pk_ppn_id: dealerGroupOrgId2,
    parent_ppn_id: importerOrgId2,
    display_name: 'IntegrationTest - Dealer Group2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 2 relation to itself
  {
    pk_ppn_id: dealerGroupOrgId2,
    parent_ppn_id: dealerGroupOrgId2,
    display_name: 'IntegrationTest - Dealer Group2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent dealer group1
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId1,
    dealer_number: 'ItDlr1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImp11',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId1,
    dealer_number: 'ItDlr1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImp11',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: 'ItDlr1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImp11',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent dealer group2
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId2,
    dealer_number: 'ItDlr2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImp22',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId2,
    dealer_number: 'ItDlr2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImp22',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: 'ItDlr2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImp22',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent dealer group1
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerGroupOrgId1,
    dealer_number: 'ItDlrInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImp11',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent importer
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: importerOrgId1,
    dealer_number: 'ItDlrInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImp11',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to itself
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerOrgIdInactive,
    dealer_number: 'ItDlrInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImp11',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

const mtvImpInPast: ModelTypeVisibilityModel = {
  //For IMP visible date in past
  importer_number: 'ItImp11',
  cnr: 'MTTCNR1',
  model_type: 'MT0001',
  my4: '2030',
  role: 'IMP',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};
const mtvImpInFuture: ModelTypeVisibilityModel = {
  //For IMP visible date in future
  importer_number: 'ItImp11',
  cnr: 'MTTCNR1',
  model_type: 'MT0002',
  my4: '2030',
  role: 'IMP',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvDlrInPast: ModelTypeVisibilityModel = {
  //For DLR visible date in past
  importer_number: 'ItImp11',
  cnr: 'MTTCNR1',
  model_type: 'MT0001',
  my4: '2030',
  role: 'DLR',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};
const mtvDlrInFuture: ModelTypeVisibilityModel = {
  //For DLR visible date in future
  importer_number: 'ItImp11',
  cnr: 'MTTCNR1',
  model_type: 'MT0002',
  my4: '2030',
  role: 'DLR',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvPagInPast: ModelTypeVisibilityModel = {
  //For PAG visible date in past
  importer_number: 'ItImp11',
  cnr: 'MTTCNR1',
  model_type: 'MT0001',
  my4: '2030',
  role: 'PAG',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};
const mtvPagInFuture: ModelTypeVisibilityModel = {
  //For PAG visible date in future
  importer_number: 'ItImp11',
  cnr: 'MTTCNR1',
  model_type: 'MT0002',
  my4: '2030',
  role: 'PAG',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvs: ModelTypeVisibilityModel[] = [
  mtvImpInPast,
  mtvImpInFuture,
  mtvDlrInPast,
  mtvDlrInFuture,
  mtvPagInPast,
  mtvPagInFuture,
];

//modeltype texts
const mttGG1En = {
  iso_language_code: 'en-DE',
  ggid: 1,
  model_year: 2030,
  model_type: 'MT0001',
  language_code: 'EN',
  model_text: 'mttGG1En',
  importer_number: null,
  cnr: null,
  modified_by: 'INTEGRATION_TESTER',
  created_by: 'INTEGRATION_TESTER',
};

const mttGG1De = {
  iso_language_code: 'de-DE',
  ggid: 1,
  model_year: 2030,
  model_type: 'MT0001',
  language_code: 'DE',
  model_text: 'mttGG1De',
  importer_number: null,
  cnr: null,
  modified_by: 'INTEGRATION_TESTER',
  created_by: 'INTEGRATION_TESTER',
};

const mttGG2En = {
  iso_language_code: 'en-DE',
  ggid: 2,
  model_year: 2030,
  model_type: 'MT0001',
  language_code: 'EN',
  model_text: 'mttGG2En',
  importer_number: '99999999',
  cnr: 'MTTCNR1',
  modified_by: 'INTEGRATION_TESTER',
  created_by: 'INTEGRATION_TESTER',
};

const mtts: ModelTypeTextModel[] = [mttGG1En, mttGG1De, mttGG2En];

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

let dataSource: DataSource;
let mtvRepo: Repository<ModelTypeVisibilityModel>;
let mttRepo: Repository<ModelTypeTextModel>;
let testMtvs: ModelTypeVisibilityModel[] = [];
let testMtts: ModelTypeTextModel[] = [];

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([ModelTypeVisibilityModel, ModelTypeTextModel]);
  mtvRepo = dataSource.getRepository(ModelTypeVisibilityModel);
  mttRepo = dataSource.getRepository(ModelTypeTextModel);
  testMtvs = await mtvRepo.save(mtvs);
  testMtts = await mttRepo.save(mtts);
  await prepareDynamodb([{ tableName: orgRelTableName, objs: orgRels }]);
});

afterAll(async () => {
  await mtvRepo.remove(testMtvs);
  await mttRepo.remove(testMtts);
  await dataSource.destroy();
  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
  ]);
});

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithVisibilityDlr = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'DLR',
    },
  ],
};

const appsWithVisibilityPag = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'PAG',
    },
  ],
};

it('should return 401 if org is missing in auth context', async () => {
  const event = createApiGwEvent({
    kasApplications: appsWithVisibilityImp,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 401 if visibility is missing in auth context', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithNoRole,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 400 if isoLanguageCode path param is missing', async () => {
  const event = createApiGwEvent({ userOrgId: importerOrgId1, kasApplications: appsWithVisibilityImp });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 400 for a dealer whos org is inactive', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgIdInactive,
    kasApplications: appsWithVisibilityDlr,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).toBeDefined();
});

it('should return 200 with empty array for a dealer that is requesting a different importer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId2,
    kasApplications: appsWithVisibilityDlr,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toEqual(0);
});

it('should return 200 with empty array for an importer that is requesting a different importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId2,
    kasApplications: appsWithVisibilityImp,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toEqual(0);
});

it('should return 200 with empty array for an importer with PAG visibility that is requesting a different importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId2,
    kasApplications: appsWithVisibilityPag,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toEqual(0);
});

it('should return 400 for a user with org ID that is not in DB', async () => {
  const event = createApiGwEvent({
    userOrgId: 'somethingWrongHere',
    kasApplications: appsWithVisibilityImp,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).toBeDefined();
});

it('should return 200 with the list of correct english MTTs for an authorized importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toEqual(1);
  expect(body[0].iso_language_code).toEqual(mttGG1En.iso_language_code);
  expect(body[0].model_year).toEqual(mttGG1En.model_year);
  expect(body[0].model_type).toEqual(mttGG1En.model_type);
  expect(body[0].language_code).toEqual(mttGG1En.language_code);
  expect(body[0].model_text).toEqual(mttGG1En.model_text);
  expect(body[0].importer_number).toEqual(mttGG1En.importer_number);
  expect(body[0].cnr).toEqual(mttGG1En.cnr);
});

it('should return 200 with the list of correct english MTTs for an authorized dealer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId1,
    kasApplications: appsWithVisibilityDlr,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toEqual(1);
  expect(body[0].iso_language_code).toEqual(mttGG1En.iso_language_code);
  expect(body[0].model_text).toEqual(mttGG1En.model_text);
});

it('should return 200 with the list of correct english MTTs for an authorized user with pag visibility', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityPag,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toEqual(1);
  expect(body[0].iso_language_code).toEqual(mttGG1En.iso_language_code);
  expect(body[0].model_text).toEqual(mttGG1En.model_text);
});

//Test for isoLanguageCode to be ignored
it('should return 200 with the list of correct english MTTs when asking for german', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    pathParameters: { isoLanguageCode: 'en-DE' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toEqual(1);
  expect(body[0].iso_language_code).toEqual(mttGG1En.iso_language_code);
  expect(body[0].model_text).toEqual(mttGG1En.model_text);
});
