import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON>pinner } from '@porsche-design-system/components-react';
import { FetchError, getErrorTextFromFetchError } from '../../errors/FetchErrors';
import { FormApiNewCarOrderConvertRequest, OrderConvertForm } from './OrderConvertForm';
import { DealConvertResultModal } from '../order-flow-modal/DealConvertResultModal';
import { ObjectValidator } from '../../../utils/object-validation';
import KccConfiguration from '../../kcc-component/KccConfiguration';
import { KccComponentInitialRequestParams } from '../../kcc-component/kccUtils';
import { selectKccConfig } from '../../../store/slices/KccConfigSlice';
import { routes } from '../../../Constants';
import {
  useConvertDealToNewCarOrderMutation,
  useGetPurchaseIntentionByIdQuery,
} from '../../../store/api/PurchaseIntentionApi';
import { useLazyGetQuotaForDealerQuery } from '../../../store/api/QuotaApi';
import { ConvertPiApiRequest } from '../../../../../infrastructure/lib/types/process-steering-types';
import { CoraNCPurchaseIntentionApiResponse } from '../../../../../infrastructure/lib/types/purchase-intention-types';
import { CoraNCOConfiguration } from '../../../../../infrastructure/lib/types/new-car-order-types';
import { useAppDispatch, useAppSelector } from '../../../app/hooks';
import { useNavigate, useParams } from 'react-router-dom';
import { displayNotification } from '../../../store/slices/NotificationSlice';
import { OrderFlowModal } from '../order-flow-modal/OrderFlowModal';

const OrderConvertMainPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { purchase_intention_id } = useParams();
  const {
    data: original_purchase_intention,
    error,
    isLoading,
  } = useGetPurchaseIntentionByIdQuery(purchase_intention_id ?? '');
  const kccConfig = useAppSelector(selectKccConfig);

  const objectValidator = new ObjectValidator<ConvertPiApiRequest>('ConvertPiApiRequest');
  const [newCarOrderToCreate, setNewCarOrderToCreate] = useState<FormApiNewCarOrderConvertRequest | undefined>(
    undefined,
  );
  const [modalError, setModalError] = useState<string | undefined>(undefined);
  const [formError, setFormError] = useState<string>();
  const [kccParams, setKccParams] = useState<KccComponentInitialRequestParams>();
  const [showKccIframe, setShowKccIframe] = useState<boolean>(false);
  const [triggerQuotaQuery, quotaResult] = useLazyGetQuotaForDealerQuery();
  const [
    convertPi,
    {
      isLoading: isConvertingPi,
      isError: isPiConvertError,
      isSuccess: isPiConvertSuccess,
      data: convertPiResult,
      error: piConvertError,
    },
  ] = useConvertDealToNewCarOrderMutation();

  function onFormChange(order: FormApiNewCarOrderConvertRequest) {
    setNewCarOrderToCreate(order);
  }

  function validateDealConvertRequest(order: Partial<ConvertPiApiRequest>): ConvertPiApiRequest | undefined {
    if (formError) {
      setModalError(formError);
      console.error('validation failed', formError);
      return undefined;
    }
    const [order_validated, validation_errors] = objectValidator.validate(order);
    if (order_validated === null) {
      setModalError(validation_errors?.[0].message);
      return undefined;
    }
    setModalError(undefined);
    return order_validated;
  }

  function onConvertToOrder() {
    if (newCarOrderToCreate) {
      const order = { ...newCarOrderToCreate };
      order.configuration_signature = kccConfig.signature_config;
      order.configuration = kccConfig.config;
      order.configuration_expire = kccConfig.config_pvms;
      order.configuration_expire_signature = kccConfig.signature_config_pvms;
      const validated_order = validateDealConvertRequest(order);
      if (validated_order) {
        convertPi(validated_order);
      }
    } else {
      dispatch(
        displayNotification({
          title: t('unknown_error'),
          msg: t('unknown_error', { area: 'order_create' }),
          state: 'error',
        }),
      );
    }
  }

  function restartKccWorkflow() {
    console.debug('reload kcc iframe');
  }

  useEffect(() => {
    if (original_purchase_intention) {
      const _convertedNewCarOrder = dealToNewCarOrderObject(original_purchase_intention);
      setKccParams({ ..._convertedNewCarOrder, process: 'CO2' });
      setNewCarOrderToCreate(_convertedNewCarOrder);
      triggerQuotaQuery({
        dealer_number: _convertedNewCarOrder.dealer_number,
        model_type: _convertedNewCarOrder.model_type,
        model_year: _convertedNewCarOrder.model_year,
        importer_number: _convertedNewCarOrder.importer_number,
      });
    }
  }, [original_purchase_intention]);

  useEffect(() => {
    if (quotaResult.isFetching || !newCarOrderToCreate) {
      return;
    }
    if (quotaResult.error) {
      dispatch(
        displayNotification({
          msg: `${t('api_err')} ${t('fetch_quotas')}: ${getErrorTextFromFetchError(quotaResult.error, t)}`,
          state: 'error',
          title: 'purchase_intention_no_quota_err_title',
          showUntil: new Date().getTime() + 60000,
        }),
      );
      navigate(routes.lists.purchaseIntentions);
      return;
    }
    if (quotaResult.data?.find((q) => q.quota_open > 0)) {
      setShowKccIframe(true);
      return;
    }
    if (isPiConvertSuccess) {
      return;
    }
    dispatch(
      displayNotification({
        msg: `${t('no_quota_err_msg')} | ${t('model_type')}: ${newCarOrderToCreate.model_type}, ${t('model_year')}: ${newCarOrderToCreate.model_year}, ${t('dealer_number')}: ${newCarOrderToCreate.dealer_number}`,
        state: 'error',
        title: 'purchase_intention_no_quota_err_title',
        showUntil: new Date().getTime() + 60000,
      }),
    );
    navigate(routes.lists.purchaseIntentions);
  }, [quotaResult]);

  if (!purchase_intention_id) {
    return <>{t('no_purchase_intention_id_in_params')}</>;
  }
  if (isLoading) {
    return <PSpinner />;
  }
  if (!original_purchase_intention) {
    return <FetchError custom_error={error} error_area="deal_convert" />;
  }

  return (
    <div>
      {isPiConvertError || isPiConvertSuccess ? (
        <DealConvertResultModal
          open={isPiConvertError || isPiConvertSuccess}
          originalPi={original_purchase_intention}
          error={piConvertError}
          result={convertPiResult}
        />
      ) : (
        <>
          {newCarOrderToCreate && (
            <OrderFlowModal
              e2eId="convert_pi_modal"
              heading={t('convert_order')}
              error={modalError}
              body={
                <OrderConvertForm
                  partialNewCarOrder={newCarOrderToCreate}
                  onFormChange={onFormChange}
                  setError={setFormError}
                />
              }
              onConfirm={onConvertToOrder}
              onBackToKcc={restartKccWorkflow}
              confirmButtonText="convert_order_button"
              mutationRequestInProgress={isConvertingPi}
            />
          )}
        </>
      )}
      {showKccIframe && kccParams && (
        <KccConfiguration
          kccInitRequestParams={kccParams}
          kccCancelDefaultRedirectUrl={`${window.location.origin}${routes.lists.purchaseIntentions}`}
        />
      )}
      {!showKccIframe && quotaResult.isFetching && <PSpinner />}
    </div>
  );
};
export default OrderConvertMainPage;

function dealToNewCarOrderObject(purchaseIntention: CoraNCPurchaseIntentionApiResponse): Omit<
  ConvertPiApiRequest,
  'configuration' | 'shipping_code'
> & {
  shipping_code?: string;
  configuration?: CoraNCOConfiguration;
} {
  return {
    cnr: purchaseIntention.cnr,
    deal_id: purchaseIntention.purchase_intention_id,
    dealer_number: purchaseIntention.dealer_number,
    importer_number: purchaseIntention.importer_number,
    importer_code: purchaseIntention.importer_code,
    model_type: purchaseIntention.model_type,
    model_year: `${purchaseIntention.model_year}`,
    order_type: purchaseIntention.order_type,
    pk_new_car_order_id: purchaseIntention.purchase_intention_id,
    quota_month: purchaseIntention.quota_month,
    shipping_code: purchaseIntention.shipping_code ?? undefined,
    business_partner_id: purchaseIntention.business_partner_id,
    configuration_expire: purchaseIntention.vehicle_configuration_pvmsnext,
    configuration: purchaseIntention.vehicle_configuration_onevms ?? undefined,
    configuration_signature: 'dummy',
    receiving_port_code: purchaseIntention.receiving_port_code,
    requested_dealer_delivery_date: purchaseIntention.requested_dealer_delivery_date || undefined, // can be empty string in pi
    dealer_name: purchaseIntention.dealer_name,
    modified_at: purchaseIntention.modified_at ?? new Date(0).toISOString(),
  };
}
