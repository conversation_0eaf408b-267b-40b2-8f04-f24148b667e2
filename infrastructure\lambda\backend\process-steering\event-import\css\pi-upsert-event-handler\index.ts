import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { DataSource } from 'typeorm';

import { Constants } from '../../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../../lib/utils/object-validation';
import { createTypeORMDataSource } from '../../../../../config/typeorm-config';
import { secretCache } from '../../../../../utils/secret-cache';
import { getEnvVarWithAssert, getMdDealerImporter, pushNotificationsToKafka } from '../../../../../utils/utils';
import {
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../../lib/types/process-steering-types';
import { createSqsEventHandlerWithInitLogger } from '../../../../../utils/sqs-event-handler';
import { PvmsOrderDataDTOTransaction } from '../../../../../../lib/types/pvms-types';
import { CoraPurchaseIntentionModel } from '../../../../../../lib/entities/purchase-intention-model';
import { KafkaAdapter } from '../../../../../utils/kafka';
import {
  CssCreateSaleEvent,
  CssSaleEvent,
  CssSaleEventType,
  CssUpdateSaleEvent,
} from '../../../../../../lib/types/css-types';
import { CoraMdDealer, CoraMdImporter } from '../../../../../../lib/types/masterdata-types';
import { CoraNCOConfiguration } from '../../../../../../lib/types/new-car-order-types';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';

type UpsertPiWithMessageId = (CssCreateSaleEvent | CssUpdateSaleEvent) & { messageId: string };
type UpsertPiWithError = UpsertPiWithMessageId & { errorMessage: string };

const auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');
const kafkaSecretArn = getEnvVarWithAssert('KAFKA_SECRET_ARN');
const KAFKA_TOPIC_NOTIFICATION = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
const KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];
const impTableName = getEnvVarWithAssert('TABLE_NAME_MD_IMPORTER');
const dlrTableName = getEnvVarWithAssert('TABLE_NAME_MD_DEALER');

const stage = getEnvVarWithAssert('STAGE');
const kasLambdaLogger = new KasLambdaLogger('css-import-upsert-pi-handler', LogLevel.TRACE);

//init secret cache
secretCache.initCache(auroraSecretArn, kafkaSecretArn);

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });

//init kafka adapter
const kafkaAdapter = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: kafkaSecretArn,
  logger: kasLambdaLogger,
});

const sqsCreateEventValidator = new ObjectValidator<CssCreateSaleEvent>('CssCreateSaleEvent');
const sqsUpdateEventValidator = new ObjectValidator<CssUpdateSaleEvent>('CssUpdateSaleEvent');

const cssSaleToCoraPurchaseIntentionDatabaseObject = (
  cssSale: CssSaleEvent,
  importer: CoraMdImporter,
  dealer: CoraMdDealer,
  orderType: string,
  configuration: CoraNCOConfiguration,
): Partial<CoraPurchaseIntentionModel> => {
  const receivingPortCode =
    dealer.standard_port_code ??
    (dealer.alternative_port_codes?.length ? dealer.alternative_port_codes[0] : undefined) ??
    (importer.port_codes?.length ? importer.port_codes[0] : undefined);
  null;

  return {
    purchase_intention_id: cssSale.saleId,
    importer_number: importer.pk_importer_number,
    importer_code: importer.code,
    dealer_number: cssSale.dealer.partnerNumber,
    model_type: cssSale.orderType,
    model_year: cssSale.modelYear,
    cnr: cssSale.cNr,
    quota_month: cssSale.quotaMonth,
    order_type: orderType,
    shipping_code: null,
    receiving_port_code: receivingPortCode,
    requested_dealer_delivery_date: cssSale.desiredDeliveryDate,
    created_at: cssSale.meta.eventTime,
    created_by: OneVmsSourceSystemKey.CSS,
    modified_by: OneVmsSourceSystemKey.CSS,
    modified_at: cssSale.meta.eventTime,
    vehicle_status_code: Constants.CSS_PURCHASE_INTENTION_STATUS_NEW,
    business_partner_id: cssSale.customer.businessPartnerId,
    seller: cssSale.salesPerson,
    vehicle_configuration_pvmsnext: null,
    vehicle_configuration_onevms: configuration,
    is_converted: false,
  };
};

const getOrderTypeFromCssSaleObject = (cssSale: CssSaleEvent): string | undefined => {
  switch (cssSale.customer.type) {
    case 'PRIVATE':
      return 'KF';
    case 'COMMERCIAL':
      return 'KY';
    case 'COMMERCIAL_RESELLER':
      return 'KY'; //TODO check if correct
    default:
      return undefined;
  }
};

const getConfigurationFromCssSaleObject = (cssSale: CssSaleEvent): CoraNCOConfiguration | undefined => {
  switch (cssSale.meta.eventType) {
    case CssSaleEventType.ConfiguredVehicleSold: //1.1
      return cssSale.configuration;
    case CssSaleEventType.ConfigurationChanged: //2.2
      return cssSale.updatedConfiguration;
    default:
      return undefined;
  }
};

const cssEventToNotification = (
  cssEvent: Partial<UpsertPiWithMessageId>,
  status: NotificationStatus,
  details?: unknown,
): NotificationKafkaEvent => {
  return {
    transaction_id: cssEvent.transaction_id ?? 'unknown',
    sub_transaction_id: cssEvent.transaction_id ?? 'unknown',
    event_type: OneVmsEventKey.CSS_IMPORT,
    action_by: OneVmsSourceSystemKey.CSS,
    action_at: new Date().toISOString(),
    source_system: OneVmsSourceSystemKey.CSS,
    status: status,
    details,
  };
};

const upsertPiFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };
  const successfulEvents: UpsertPiWithMessageId[] = [];
  const expectedFailedEvents: UpsertPiWithError[] = [];
  const unexpectedFailedEvents: UpsertPiWithError[] = [];
  const unParseableEvents: Partial<UpsertPiWithError>[] = [];

  const upsertPiEvents = event.Records.map((record) => {
    try {
      // Parse body from event object
      const upsertPiEvent = JSON.parse(record.body) as CssCreateSaleEvent | CssUpdateSaleEvent | undefined;
      if (!upsertPiEvent) {
        throw new Error('Parsed body is undefined or null');
      }

      let eventValidator: ObjectValidator<CssSaleEvent> | null = null;
      switch (upsertPiEvent.meta.eventType) {
        case CssSaleEventType.ConfiguredVehicleSold: //1.1
          eventValidator = sqsCreateEventValidator;
          break;
        case CssSaleEventType.ConfigurationChanged: //2.2
          eventValidator = sqsUpdateEventValidator;
          break;
      }
      if (!eventValidator) {
        throw new Error('Could not determine event type');
      }

      const [body_validated, validation_errors] = eventValidator.validate(upsertPiEvent);
      if (body_validated === null) {
        throw new Error(JSON.stringify(validation_errors));
      }

      return { ...upsertPiEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<PvmsOrderDataDTOTransaction>),
        messageId: record.messageId,
        errorMessage: message,
      });
      return undefined;
    }
  }).filter(Boolean) as UpsertPiWithMessageId[];

  let piDataSource: DataSource;
  const allImporters: Record<string, CoraMdImporter> = {};
  const allDealers: Record<string, CoraMdDealer> = {};

  try {
    piDataSource = await createTypeORMDataSource(logger, auroraSecretArn, stage, [CoraPurchaseIntentionModel]);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await pushNotificationsToKafka(
      upsertPiEvents.map((cssEvent) => cssEventToNotification(cssEvent, NotificationStatus.EVENT_HANDLER_NIO)),
      KAFKA_TOPIC_NOTIFICATION,
      kafkaAdapter,
      kasLambdaLogger,
    );

    // Return fail for all events so that they are retried later
    return {
      batchItemFailures: upsertPiEvents.map((upsertPiEvent) => ({
        itemIdentifier: upsertPiEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  //Process all events one by one
  for (const upsertPiEvent of upsertPiEvents) {
    logger.setObjectId(upsertPiEvent.transaction_id);
    const dealerNumber = upsertPiEvent.dealer.partnerNumber;
    const importerNumber = upsertPiEvent.dealer.importerPartnerNumber;

    //get Cora MD Importer and dealer for all event and cache them
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!allImporters[importerNumber] || !allDealers[dealerNumber]) {
      const { dealer, importer } = await getMdDealerImporter(
        {
          dynamoDb,
          dlrTableName,
          impTableName,
          importerNumber,
          dealerNumber,
        },
        logger,
      );
      if (!dealer || !importer) {
        const message = 'Dealer or Importer for PI could not be determined, skipping';
        logger.log(LogLevel.ERROR, message, { data: upsertPiEvent });
        expectedFailedEvents.push({
          ...upsertPiEvent,
          errorMessage: message,
        });
        continue;
      }
      allImporters[importerNumber] = importer;
      allDealers[dealerNumber] = dealer;
    }

    //Get and check ordertype
    const orderType = getOrderTypeFromCssSaleObject(upsertPiEvent);
    if (!orderType) {
      const message = 'Ordertype of Sale event could not be determined, skipping';
      logger.log(LogLevel.ERROR, message, { data: upsertPiEvent });
      expectedFailedEvents.push({ ...upsertPiEvent, errorMessage: message });
      continue;
    }

    //Get and check configuration
    const configuration = getConfigurationFromCssSaleObject(upsertPiEvent);
    if (!configuration) {
      const message = 'Configuration of Sale event could not be determined, skipping';
      logger.log(LogLevel.ERROR, message, { data: upsertPiEvent });
      expectedFailedEvents.push({
        ...upsertPiEvent,
        errorMessage: message,
      });
      continue;
    }

    //convert to Cora PI Object
    const coraPi = cssSaleToCoraPurchaseIntentionDatabaseObject(
      upsertPiEvent,
      allImporters[importerNumber],
      allDealers[dealerNumber],
      orderType,
      configuration,
    );

    try {
      await piDataSource.transaction(async (transactionalEntityManager) => {
        // Check if PI exists and is converted
        const existingPi = await transactionalEntityManager.findOneBy(CoraPurchaseIntentionModel, {
          purchase_intention_id: coraPi.purchase_intention_id,
        });

        if (existingPi?.is_converted) {
          const message = 'PI already exists and is converted, skipping update';
          logger.log(LogLevel.WARN, message, {
            data: { pi_id: coraPi.purchase_intention_id },
          });
          expectedFailedEvents.push({
            ...upsertPiEvent,
            errorMessage: message,
          });
          return;
        }

        //only overwrite config and modified fields if PI already exists
        const piForUpsert = existingPi
          ? {
              ...existingPi,
              vehicle_configuration_onevms: coraPi.vehicle_configuration_onevms,
              modified_at: coraPi.modified_at,
              modified_by: coraPi.modified_by,
            }
          : coraPi;

        // Perform upsert if PI doesn't exist or isn't converted
        await transactionalEntityManager.save(CoraPurchaseIntentionModel, piForUpsert);

        logger.log(
          LogLevel.INFO,
          `Successfully ${existingPi ? 'updated' : 'inserted'} PI with id "${coraPi.purchase_intention_id}" into rds`,
        );

        successfulEvents.push(upsertPiEvent);
      });
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'PI was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...upsertPiEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during update';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...upsertPiEvent, errorMessage: message });
      //Return as fail so that event will be retried later
      sqsBatchResponse.batchItemFailures.push({
        itemIdentifier: upsertPiEvent.messageId,
        errorMessage: message,
      });
      continue;
    }
  }

  const notificationEvents = [
    ...successfulEvents.map((cssEvent) => cssEventToNotification(cssEvent, NotificationStatus.EVENT_HANDLER_IO)),
    ...expectedFailedEvents.map((cssEvent) =>
      cssEventToNotification(cssEvent, NotificationStatus.EVENT_HANDLER_NIO, cssEvent.errorMessage),
    ),
    ...unParseableEvents.map((cssEvent) =>
      cssEventToNotification(cssEvent, NotificationStatus.EVENT_HANDLER_NIO, cssEvent.errorMessage),
    ),
    ...unexpectedFailedEvents.map((cssEvent) =>
      cssEventToNotification(cssEvent, NotificationStatus.EVENT_HANDLER_NIO, cssEvent.errorMessage),
    ),
  ];

  try {
    await pushNotificationsToKafka(notificationEvents, KAFKA_TOPIC_NOTIFICATION, kafkaAdapter, kasLambdaLogger);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications', { data: e });
  }

  //Return fails so that retriable events are tried again
  return sqsBatchResponse;
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(kasLambdaLogger)(event, context, upsertPiFunc);
