import { OneVmsSourceSystemKey } from '../../../lib/types/process-steering-types';

export interface CoraNCOConfigOrderedOptions {
  [k: string]: unknown;
  option_id: string;
  option_type?: string;
  option_validity?: {
    [k: string]: unknown;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    valid_until?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    valid_from?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    serial_to?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    serial_from?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    offer_period_start?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    offer_period_end?: string;
    /**
     * @description Matrial lead time, in number of days
     * @pattern ^(\d+|)$
     */
    material_lead_time?: string; //(Anzahl in Tagen)
    /**
     * @description When an option was added to the order
     * @format date-time
     */
    added_to_order_timestamp?: string;
    referenced_package?: string;
    referenced_package_type?: string;
    referenced_package_sort_order?: number;
    option_subtype?: string;
    option_subtype_value?: string;
  };
}

export interface CoraNCOConfiguration {
  ordered_options: CoraNCOConfigOrderedOptions[];
  technical_options?: unknown;
}

export interface KccNCOConfigReponseKafkaValue {
  kas: {
    model_info: {
      model_type: string;
      model_year: number;
      country_code: string;
    };
    configuration: CoraNCOConfiguration;
  };
}

export interface KccNCOConfigWindowMessagePayload extends KccNCOConfigReponseKafkaValue {
  pvms: PvmsNCOConfigurationExpire;
}

export interface KccNCOConfigReponseKafkaObject {
  key: string;
  value: KccNCOConfigReponseKafkaValue;
  timestamp: number;
}

export type PvmsNCOConfigurationExpire = unknown;
interface CoraNCODatabaseObjectBase {
  pk_new_car_order_id: string;
  importer_number: string;
  /**
   * @minLength 2
   */
  importer_code: string;
  dealer_number: string;
  /**
   * @minLength 6
   */
  model_type: string;
  model_year: string;
  /**
   * @pattern ^C\d\d$
   */
  cnr: string;
  /**
   * @pattern ^\d\d\d\d-\d\d$
   */
  quota_month: string;
  order_type: string;
  shipping_code: string;
  receiving_port_code?: string;
  requested_dealer_delivery_date?: string;
  last_known_correlation_id?: string;
}

export interface CoraNCODatabaseObject extends CoraNCODatabaseObjectBase {
  /**
   * @format date-time
   */
  created_at: string;
  created_by: string;
  /**
   * @format date-time
   */
  modified_at?: string;
  modified_by?: string;
  order_status_onevms_code: string;
  order_status_onevms_error_code: string;
  /**
   * @format date-time
   */
  order_status_onevms_timestamp_last_change: string;
  cancellation_reason?: string;
  business_partner_id?: string;
  deal_id?: string;
  configuration: CoraNCOConfiguration;
  configuration_expire: PvmsNCOConfigurationExpire;
  changed_by_system: OneVmsSourceSystemKey; //set to "pvms" to break event loop
}

export type CoraNCODatabaseObjectWithoutCoraConfig = Omit<CoraNCODatabaseObject, 'configuration'>;

export interface CoraGetNCOApiResponse extends CoraNCODatabaseObject {
  dealer_name?: string;
}

// Api Request Objects
export type CoraNewCarDealConvertApiRequest = CoraNCODatabaseObjectBase &
  Required<Pick<CoraNCODatabaseObject, 'deal_id'>> &
  Pick<CoraNCODatabaseObject, 'configuration' | 'business_partner_id' | 'configuration_expire'> & {
    configuration_signature: string;
    configuration_expire_signature?: string;
    dealer_name?: string;
  };

export type CoraNCOEditApiRequest = CoraNCODatabaseObjectBase &
  Pick<
    CoraNCODatabaseObject,
    'configuration' | 'business_partner_id' | 'configuration_expire' | 'modified_at' | 'modified_by'
  > & {
    configuration_signature: string;
    configuration_expire_signature?: string;
    dealer_name?: string;
  };

export type CoraNCOCreateApiRequest = Omit<CoraNCODatabaseObjectBase, 'pk_new_car_order_id'> &
  Pick<CoraNCODatabaseObject, 'configuration' | 'configuration_expire'> & {
    configuration_signature: string;
    configuration_expire_signature?: string;
    dealer_name?: string;
  };

export interface CoraNCOCancelApiRequest {
  /**
   * @minItems 1
   */
  ncoids: string[];
  cancellation_reason: string;
  last_known_correlation_id: string;
}

export const unchangeableKeysCoraNCODatabaseObject = [
  'pk_new_car_order_id',
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'importer_code',
  'created_at',
  'created_by',
  'order_status_onevms_code',
  'order_status_onevms_error_code',
  'order_status_onevms_timestamp_last_change',
] as const;
export type UnchangeableKeysCoraNCODatabaseObject = Pick<
  CoraNCODatabaseObject,
  (typeof unchangeableKeysCoraNCODatabaseObject)[number]
>;

export const unchangeableKeysCoraNCPurchaseIntentionToNCO = [
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'importer_code',
] as const;
export type UnchangeableKeysCoraNCPurchaseIntentionToNCO = Pick<
  CoraNCODatabaseObject,
  (typeof unchangeableKeysCoraNCPurchaseIntentionToNCO)[number]
>;

export const frontendRequiredQueryParamsNCOCreate = [
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'cnr',
  'importer_code',
  'quota_month',
] as const;
export type FrontendNCOCreateQueryParams = Pick<
  CoraNCODatabaseObject,
  (typeof frontendRequiredQueryParamsNCOCreate)[number]
>;
