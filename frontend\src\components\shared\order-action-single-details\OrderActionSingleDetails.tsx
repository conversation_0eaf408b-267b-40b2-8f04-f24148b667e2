import { CarOrderInList } from '../../../store/types';
import { PDivider, PText } from '@porsche-design-system/components-react';
import { matchMttForOrderOrPurchaseIntention } from '../../../utils/utils';
import React from 'react';
import './OrderActionSingleDetails.css';
import { useTranslation } from 'react-i18next';
import { ModelTypeTextModel } from '../../../../../infrastructure/lib/entities/model-type-text-model';

export interface OrderActionSingleDetailsProps {
  order: CarOrderInList;
  modelTypeTexts?: ModelTypeTextModel[];
}

const OrderActionSingleDetails: React.FC<OrderActionSingleDetailsProps> = (props: OrderActionSingleDetailsProps) => {
  const { t } = useTranslation();
  return (
    <div>
      <div className="display-details-single-container">
        <div className="field-col">
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('pk_new_car_order_id')}
            </PText>
            <PText data-e2e="pk_new_car_order_id" weight={'bold'} size={'x-small'}>
              {props.order.pk_new_car_order_id}
            </PText>
          </div>
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('order_type')}
            </PText>
            <PText weight={'bold'} size={'x-small'}>
              {props.order.order_type}
            </PText>
          </div>
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('model_text')}
            </PText>
            <PText weight={'bold'} size={'x-small'}>
              {matchMttForOrderOrPurchaseIntention(props.modelTypeTexts, props.order, t)}
            </PText>
          </div>
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('model_type')}
            </PText>
            <PText weight={'bold'} size={'x-small'}>
              {props.order.model_type}
            </PText>
          </div>
        </div>

        <div className="field-col">
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('quota_month')}
            </PText>
            <PText weight={'bold'} size={'x-small'}>
              {props.order.quota_month}
            </PText>
          </div>
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('model_year')}
            </PText>
            <PText weight={'bold'} size={'x-small'}>
              {props.order.model_year}
            </PText>
          </div>
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('dealer_name')}
            </PText>
            <PText weight={'bold'} size={'x-small'}>
              {props.order.dealer_name ?? '-'}
            </PText>
          </div>
          <div className="field-row">
            <PText color="contrast-medium" size={'xx-small'}>
              {t('dealer_number')}
            </PText>
            <PText weight={'bold'} size={'x-small'}>
              {props.order.dealer_number}
            </PText>
          </div>
        </div>
      </div>
      <PDivider></PDivider>
    </div>
  );
};

export default OrderActionSingleDetails;
