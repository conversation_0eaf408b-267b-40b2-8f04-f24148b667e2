import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { SendMessageBatchCommand, SendMessageBatchRequestEntry, SQSClient } from '@aws-sdk/client-sqs';
import { BatchGetCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { DataSource, DeepPartial, EntityManager, In, LessThanOrEqual, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { ModelTypeVisibilityModel } from '../../lib/entities/model-type-visibility-model';
import { NewCarOrderAuditTrailModel } from '../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
  NewCarOrderModelWithOptionalActionType,
} from '../../lib/entities/new-car-order-model';
import { OutboundProcessMappingModel } from '../../lib/entities/outbound-mapping-model';
import { CoraPurchaseIntentionModel } from '../../lib/entities/purchase-intention-model';
import { CoraMdDealer, CoraMdImporter, CoraMdOrderType, CoraMdShippingCode } from '../../lib/types/masterdata-types';
import { NcoAuditTrailSqsEvent } from '../../lib/types/nco-audit-trail-types';
import {
  CoraNCOBaseApiRequest,
  CoraNCOBaseApiResponse,
  CoraNCOConfigOrderedOptions,
  CoraNCOConfiguration,
} from '../../lib/types/new-car-order-types';
import { OneVmsSourceSystemKey, SpecialStatusCode } from '../../lib/types/process-steering-types';
import { Constants } from '../../lib/utils/constants';
import { NcoExportActionType } from '../backend/export-nco/types';
import { generateNewCarOrderId, getEnvVarWithAssert, transformModifiedAt } from './utils';
import {
  getAllAuthorizedOrgs,
  getPermissionForDealer,
  NcoValidationParams,
  validateOrderType,
  validatePortCode,
  validateShippingCode,
  ValidationReturnType,
} from './validation-helpers';

export async function createNewCarOrderId(
  props: { importerCode: string; ncoRepo: Repository<NewCarOrderModel> },
  logger: KasLambdaLogger,
): Promise<string | null> {
  const res = await createNewCarOrderIds({ ...props, amount: 1 }, logger);
  return res[0];
}

export async function createNewCarOrderIds(
  props: { amount: number; importerCode: string; ncoRepo: Repository<NewCarOrderModel> },
  logger: KasLambdaLogger,
): Promise<string[]> {
  logger.log(LogLevel.TRACE, 'createNewCarOrderId input', { data: props });
  const createdNcoids = new Set<string>();
  for (;;) {
    // Generate twice as many ncoids to have enough on conflicts (they are not saved in db until the order is created so it does not matter)
    for (let i = 0; i < props.amount * 2; i++) {
      createdNcoids.add(generateNewCarOrderId(props.importerCode));
    }
    const res = await props.ncoRepo.createQueryBuilder('new_car_order').whereInIds(createdNcoids).getMany();
    for (const nco of res) {
      createdNcoids.delete(nco.pk_new_car_order_id);
    }
    if (createdNcoids.size < props.amount) {
      logger.log(LogLevel.WARN, 'Failed to generate enough non existing nco ids', {
        data: { amount: props.amount, size: createdNcoids.size },
      });
      continue;
    }
    const _res = Array.from(createdNcoids).slice(0, props.amount);
    logger.log(LogLevel.INFO, 'createNewCarOrderIds output', { data: _res });
    return _res;
  }
}

export const getConfigRequestNco = async (
  props: {
    dbClient: DynamoDBClient;
    tableName: string;
    ncoId: string;
  },
  logger: KasLambdaLogger,
): Promise<NewCarOrderModel | undefined> => {
  logger.log(LogLevel.TRACE, 'getExistingNco input', { data: props });
  try {
    const cmd = new GetCommand({
      TableName: props.tableName,
      Key: { pk_new_car_order_id: props.ncoId },
    });
    logger.log(LogLevel.TRACE, 'getConfigRequestNco GetCommand', { data: cmd });
    const res = await props.dbClient.send(cmd);
    logger.log(LogLevel.TRACE, 'db response', { data: res });
    const result = res.Item ? (res.Item as NewCarOrderModel) : undefined;
    logger.log(LogLevel.TRACE, 'getConfigRequestNco output', { data: result });
    return result;
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Error getting existing nco request from dynamodb', { data: e });
    throw e;
  }
};

interface GetModifiedSinceRequestRes {
  validNcoIds: string[];
  modifiedNcoIds: string[];
}

export const getModifiedSinceRequest = async (
  props: { transactionManager: EntityManager; ncoIds: string[]; requestModifiedAtMap: Record<string, string> },
  logger: KasLambdaLogger,
): Promise<GetModifiedSinceRequestRes> => {
  const res: GetModifiedSinceRequestRes = {
    validNcoIds: [],
    modifiedNcoIds: [],
  };
  //ids from db
  const existingOrders = (await props.transactionManager.getRepository(NewCarOrderModel).find({
    where: { pk_new_car_order_id: In(props.ncoIds) },
    select: ['pk_new_car_order_id', 'modified_at'],
  })) as unknown as { pk_new_car_order_id: string; modified_at?: Date }[]; // TypeOrm returns this and not a NewCarOrderModel
  // map of existing orders modified_at timestamps
  const dbModifiedAtMap: Record<string, string> = Object.fromEntries(
    existingOrders.map((order) => [order.pk_new_car_order_id, transformModifiedAt(order.modified_at)]),
  );

  //find the ones modified already
  props.ncoIds.forEach((orderId) => {
    const requestModifiedAt = props.requestModifiedAtMap[orderId]; // From frontend
    const dbModifiedAt = dbModifiedAtMap[orderId]; // From DB

    if (requestModifiedAt === dbModifiedAt) {
      res.validNcoIds.push(orderId); // Add valid order IDs to the validNcoIds array
    } else {
      logger.log(LogLevel.WARN, `${orderId} modifed since request started, skipping update`, {
        data: { dbModifiedAt, requestModifiedAt, typeDb: typeof dbModifiedAt, typeReq: typeof requestModifiedAt },
      });
      res.modifiedNcoIds.push(orderId);
    }
  });
  return res;
};

export const getExistingNco = async (
  props: {
    ncoRepo: Repository<NewCarOrderModel>;
    ncoId: string;
  },
  logger: KasLambdaLogger,
): Promise<NewCarOrderModel | null> => {
  try {
    const nco = await props.ncoRepo.findOne({
      where: { pk_new_car_order_id: props.ncoId },
      relations: ['configuration', 'configuration.ordered_options', 'configuration.ordered_options.content'],
    });

    if (!nco) {
      logger.log(LogLevel.INFO, `getExistingNco() could not find order with ncoid ${props.ncoId}`);
    } else {
      logger.log(LogLevel.DEBUG, `Got db response for nco with ncoId ${props.ncoId}`, { data: nco });
    }
    return nco;
  } catch (error) {
    logger.log(LogLevel.ERROR, `DB Request error for getting order with ncoid ${props.ncoId}`, { data: error });
    return null;
  }
};

export type NcoActionsFunction = (
  transactionManager: EntityManager,
) => Promise<NewCarOrderModelWithOptionalActionType[]>;

export async function saveNcosWithAuditTrail(
  ncoDataSource: DataSource,
  affectedNcoIds: string[],
  actionType: NcoExportActionType,
  actionsFunction: NcoActionsFunction, //should return only orders that were touched successfully
  logger: KasLambdaLogger,
  doSqsExport: boolean = true,
): Promise<NewCarOrderModel[]> {
  try {
    logger.log(LogLevel.TRACE, 'saveNcosWithAuditTrail() called', { data: affectedNcoIds });
    let auditTrailResults: NewCarOrderAuditTrailModel[];
    //run everything in a transaction
    const ncoResult = await ncoDataSource.manager.transaction<NewCarOrderModel[]>(
      'REPEATABLE READ',
      async (transactionalEntityManager) => {
        logger.log(LogLevel.DEBUG, 'saveNcosWithAuditTrail() from inside transaction');
        const ncoRepo = transactionalEntityManager.getRepository(NewCarOrderModel);
        const ncoAuditTrailRepo = transactionalEntityManager.getRepository(NewCarOrderAuditTrailModel);

        //get the old nco objects first
        const oldNcoLookup: Record<string, NewCarOrderModel> = (
          await ncoRepo.find({
            where: { pk_new_car_order_id: In(affectedNcoIds) },
            relations: ['configuration', 'configuration.ordered_options', 'configuration.ordered_options.content'],
          })
        ).reduce(
          (runningLookup, nco) => ({
            ...runningLookup,
            [nco.pk_new_car_order_id]: nco,
          }),
          {},
        );

        logger.log(LogLevel.TRACE, 'saveNcosWithAuditTrail() got old ncos', { data: oldNcoLookup });

        //run the callers action function which should write to at least 1 nco
        const actionsResult = await actionsFunction(transactionalEntityManager);
        logger.log(LogLevel.TRACE, 'saveNcosWithAuditTrail() result of actions function', { data: actionsResult });

        const modifiedNcoIds = actionsResult.map((nco) => nco.pk_new_car_order_id);

        // using this to generated different actiontypes for ncos in actions function
        const modifiedNcos = actionsResult.map((nco) => ({
          ncoId: nco.pk_new_car_order_id,
          actionType: nco.action_type,
        }));

        //get the new nco objects after modification by caller
        const newNcoLookup: Record<string, NewCarOrderModel> = (
          await ncoRepo.find({
            where: { pk_new_car_order_id: In(modifiedNcoIds) },
            relations: ['configuration', 'configuration.ordered_options', 'configuration.ordered_options.content'],
          })
        ).reduce(
          (runningLookup, nco) => ({
            ...runningLookup,
            [nco.pk_new_car_order_id]: nco,
          }),
          {},
        );

        logger.log(LogLevel.TRACE, 'saveNcosWithAuditTrail() got the new ncos', { data: newNcoLookup });

        //insert an audit trail of the callers action function result
        const auditTrails = ncoAuditTrailRepo.create(
          modifiedNcos.map((nco) => ({
            pk_new_car_order_id: nco.ncoId,
            action_by: newNcoLookup[nco.ncoId].modified_by,
            action_type: nco.actionType ?? actionType,
            action_correlation_id: logger.getCorrelationId() ?? 'unknown',
            action_exported: false,
            old_nco: oldNcoLookup[nco.ncoId],
            new_nco: newNcoLookup[nco.ncoId],
          })),
        );

        const partialAuditTrailResults = (
          await ncoAuditTrailRepo.insert(auditTrails as QueryDeepPartialEntity<NewCarOrderAuditTrailModel>[])
        ).generatedMaps as NewCarOrderAuditTrailModel[];
        logger.log(LogLevel.INFO, 'Saving Audit Trail partial insert result', {
          data: JSON.stringify(partialAuditTrailResults),
        });

        //load created audit trails, since .insert is too stupid to return anything other than db generated fields
        auditTrailResults = await ncoAuditTrailRepo.findBy({
          pk_audit_trail_id: In(
            partialAuditTrailResults.map((partialAuditTrail) => partialAuditTrail.pk_audit_trail_id),
          ),
        });
        logger.log(LogLevel.TRACE, 'Saving Audit Trail get result', { data: JSON.stringify(auditTrailResults) });

        if (auditTrailResults.length !== modifiedNcoIds.length) {
          throw new Error('Did not save all audit trails for some reason, aborting transaction');
        }

        return actionsResult;
      },
    );
    logger.log(LogLevel.INFO, 'saveNcosWithAuditTrail() transaction finished', { data: ncoResult });

    //after successfull transaction, send audit trail to sqs for old kafka export proccessing
    doSqsExport && (await pushAuditTrailsToQueue(auditTrailResults!, logger));

    return ncoResult;
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error saving NCOs to DB', { data: error });
    throw error;
  }
}

const pushAuditTrailsToQueue = async (
  auditTrails: NewCarOrderAuditTrailModel[],
  logger: KasLambdaLogger,
): Promise<void> => {
  const EXPORT_NCO_SQS_URL = getEnvVarWithAssert('EXPORT_NCO_SQS_URL');
  logger.log(LogLevel.INFO, `Pushing ${auditTrails.length} into queue for export`, { data: auditTrails });

  const sqsClient = new SQSClient({ tls: true });

  const batchSize = 10; //aws sqs limit for batch messages

  for (let i = 0; i < auditTrails.length; i += batchSize) {
    const batch = auditTrails.slice(i, i + batchSize);
    const params = {
      QueueUrl: EXPORT_NCO_SQS_URL,
      Entries: batch.map((item) => {
        return {
          Id: item.pk_new_car_order_id + '_' + new Date(item.action_at).getTime(),
          MessageBody: JSON.stringify({
            pk_new_car_order_id: item.pk_new_car_order_id,
            action_at: new Date(item.action_at).toISOString(),
            action_correlation_id: item.action_correlation_id,
          } satisfies NcoAuditTrailSqsEvent),
        } satisfies SendMessageBatchRequestEntry;
      }),
    };
    try {
      const res = await sqsClient.send(new SendMessageBatchCommand(params));
      if (res.Failed && res.Failed.length > 0) {
        logger.log(
          LogLevel.ERROR,
          `Failed to send ${res.Failed.length} audit trail sqs messages, corresponding orders will not be exported!`,
          { data: res.Failed },
        );
        throw new Error(`Failed to send ${res.Failed.length} audit trail sqs messages.`);
      } else {
        logger.log(LogLevel.INFO, `Successfully sent sqs messages for batch`, { data: `${i} - ${i + batchSize - 1}` });
      }
    } catch (e) {
      logger.log(
        LogLevel.ERROR,
        `Error while pushing audit trails into sqs, corresponding orders will not be exported!`,
        { data: e },
      );
      throw e;
    }
  }
};

export function ncoApiToDbObj(
  apiNco: Omit<CoraNCOBaseApiRequest, 'configuration_signature'>,
  userName: string,
  oldDbNco?: NewCarOrderModel,
): NewCarOrderModel {
  return {
    ...apiNco,
    created_by: oldDbNco?.created_by ?? userName,
    modified_by: userName,
    order_status_onevms_code: oldDbNco?.order_status_onevms_code ?? Constants.CORA_NEW_CAR_ORDER_STATUS_NEW,
    order_status_onevms_error_code: oldDbNco?.order_status_onevms_error_code ?? 'null',
    order_invoice_onevms_code: oldDbNco?.order_invoice_onevms_code ?? Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
    order_status_onevms_timestamp_last_change:
      oldDbNco?.order_status_onevms_timestamp_last_change ?? new Date().toISOString(),
    changed_by_system: OneVmsSourceSystemKey.CORA,
    configuration: ncoConfigApiToDbObj(apiNco.configuration, userName, apiNco.pk_new_car_order_id),
  };
}

export function ncoConfigApiToDbObj(
  apiConfig: CoraNCOConfiguration,
  userName: string,
  ncoId: string,
): NcoConfigurationModel {
  const dbConfig: NcoConfigurationModel = {
    ...apiConfig,
    created_by: userName,
    modified_by: userName,
    ordered_options: apiConfig.ordered_options.map((apiOrderedOption) =>
      ncoConfigOrderedOptionApiToDb(apiOrderedOption, userName, ncoId),
    ),
  };

  return dbConfig;
}

function ncoConfigOrderedOptionApiToDb(
  apiOrderedOption: CoraNCOConfigOrderedOptions,
  userName: string,
  ncoId: string,
): NcoConfigOrderedOptionsModel {
  return {
    pk_option_id: `${ncoId}|${ncoConfigOrderedOptionGetIdFromObj(apiOrderedOption)}`,
    created_by: userName,
    modified_by: userName,
    option_id: apiOrderedOption.option_id,
    option_type: apiOrderedOption.option_type ?? '',
    validity_valid_until: apiOrderedOption.option_validity?.valid_until,
    validity_valid_from: apiOrderedOption.option_validity?.valid_from,
    validity_serial_to: apiOrderedOption.option_validity?.serial_to,
    validity_serial_from: apiOrderedOption.option_validity?.serial_from,
    validity_offer_period_start: apiOrderedOption.option_validity?.offer_period_start,
    validity_offer_period_end: apiOrderedOption.option_validity?.offer_period_end,
    validity_material_lead_time: apiOrderedOption.option_validity?.material_lead_time,
    validity_added_to_order_timestamp: apiOrderedOption.option_validity?.added_to_order_timestamp,
    referenced_package: apiOrderedOption.referenced_package,
    referenced_package_type: apiOrderedOption.referenced_package_type,
    referenced_package_sort_order: apiOrderedOption.referenced_package_sort_order,
    package_content_sort_order: apiOrderedOption.package_content_sort_order,
    option_subtype: apiOrderedOption.option_subtype ?? '',
    option_subtype_value: apiOrderedOption.option_subtype_value,
    content: apiOrderedOption.content?.map((apiContent) => ncoConfigOrderedOptionApiToDb(apiContent, userName, ncoId)),
  };
}

export function createNewCarOrderObjectFromPurchaseIntention(
  props: {
    purchaseIntention: CoraPurchaseIntentionModel;
    sourceSystem: OneVmsSourceSystemKey;
    ncoId: string;
  },
  logger: KasLambdaLogger,
): Omit<
  Required<Pick<NewCarOrderModel, 'deal_id'>> & NewCarOrderModel,
  | 'created_at'
  | 'created_by'
  | 'shipping_code'
  | 'order_type'
  | 'config_id'
  | 'configuration_expire'
  | 'modified_at'
  | 'modified_by'
  | 'configuration'
> {
  logger.log(LogLevel.TRACE, 'createNewCarOrderObjectFromDeal input', { data: props });
  const purchaseIntention = props.purchaseIntention;

  const res = {
    pk_new_car_order_id: props.ncoId,
    dealer_number: purchaseIntention.dealer_number,
    importer_code: purchaseIntention.importer_code,
    importer_number: purchaseIntention.importer_number,
    cnr: purchaseIntention.cnr,
    model_type: purchaseIntention.model_type,
    model_year: purchaseIntention.model_year,
    quota_month: purchaseIntention.quota_month,
    business_partner_id: purchaseIntention.business_partner_id,
    deal_id: purchaseIntention.purchase_intention_id,
    changed_by_system: props.sourceSystem,
    order_status_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_CONVERT,
    order_status_onevms_error_code: 'null',
    order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_CONVERT,
    order_status_onevms_timestamp_last_change: new Date().toISOString(),
  };
  logger.log(LogLevel.TRACE, 'createNewCarOrderObjectFromDeal output', { data: res });
  return res;
}

export function ncoConfigOrderedOptionGetIdFromObj(option: CoraNCOConfigOrderedOptions): string {
  return `${option.option_id}|${option.option_type ?? ''}|${option.option_subtype ?? ''}`;
}

export function ncoDbToApiObj(dbNco: NewCarOrderModel): CoraNCOBaseApiResponse {
  return {
    ...dbNco,
    created_at: new Date(dbNco.created_at ?? 0).toISOString(),
    created_by: dbNco.created_by,
    modified_at: new Date(dbNco.modified_at ?? 0).toISOString(),
    modified_by: dbNco.modified_by,
    order_status_onevms_timestamp_last_change: new Date(dbNco.order_status_onevms_timestamp_last_change).toISOString(),
    configuration: ncoConfigDbToApiObj(dbNco.configuration),
  };
}

export function ncoConfigDbToApiObj(dbConfig: NcoConfigurationModel): CoraNCOConfiguration {
  const apiConfig: CoraNCOConfiguration = {
    ordered_options: dbConfig.ordered_options.map(ncoConfigOrderedOptionDbToApi),
  };

  return apiConfig;
}

export function ncoConfigOrderedOptionDbToApi(
  dbOrderedOption: NcoConfigOrderedOptionsModel,
): CoraNCOConfigOrderedOptions {
  return {
    option_id: dbOrderedOption.option_id,
    option_type: dbOrderedOption.option_type ?? undefined,
    option_validity: {
      valid_until: dbOrderedOption.validity_valid_until ?? undefined,
      valid_from: dbOrderedOption.validity_valid_from ?? undefined,
      serial_to: dbOrderedOption.validity_serial_to ?? undefined,
      serial_from: dbOrderedOption.validity_serial_from ?? undefined,
      offer_period_start: dbOrderedOption.validity_offer_period_start ?? undefined,
      offer_period_end: dbOrderedOption.validity_offer_period_end ?? undefined,
      material_lead_time: dbOrderedOption.validity_material_lead_time ?? undefined,
      added_to_order_timestamp: dbOrderedOption.validity_added_to_order_timestamp ?? undefined,
    },
    referenced_package: dbOrderedOption.referenced_package ?? undefined,
    referenced_package_type: dbOrderedOption.referenced_package_type ?? undefined,
    referenced_package_sort_order: dbOrderedOption.referenced_package_sort_order ?? undefined,
    package_content_sort_order: dbOrderedOption.package_content_sort_order ?? undefined,
    option_subtype: dbOrderedOption.option_subtype ?? undefined,
    option_subtype_value: dbOrderedOption.option_subtype_value ?? undefined,
    content: dbOrderedOption.content?.map(ncoConfigOrderedOptionDbToApi),
  };
}

export const getModelTypeVisibility = async (
  props: {
    visibilityLevel: string;
    mtvRespoitory: Repository<ModelTypeVisibilityModel>;
    importer_number: string;
    model_type: string;
    model_year: string;
    cnr?: string;
  },
  logger: KasLambdaLogger,
): Promise<ModelTypeVisibilityModel[]> => {
  const mtv_from = new Date().toISOString().split('T', 1)[0];
  logger.log(LogLevel.DEBUG, 'Getting MTVs', { data: { mtv_from, props } });
  return await props.mtvRespoitory.find({
    where: {
      importer_number: props.importer_number,
      role: props.visibilityLevel,
      model_type: props.model_type,
      my4: props.model_year,
      valid_from: LessThanOrEqual(mtv_from),
      cnr: props.cnr,
    },
  });
};

export const validateNcoRequest = async (
  props: NcoValidationParams,
  logger: KasLambdaLogger,
): Promise<{ valid: boolean; error?: string }> => {
  logger.log(LogLevel.TRACE, 'validateNcoRequest input', { data: props });
  if (
    !props.tables.coraOrgRelationTableName ||
    !props.tables.shippingCodeTableName ||
    !props.tables.orderTypeTableName ||
    !props.tables.importerTableName ||
    !props.tables.dealerTableName
  ) {
    throw new Error('validateNcoRequest: Lambda is missing env variables');
  }

  try {
    //validate importer and dealer number and find out users role
    const authorizedOrgs = await getAllAuthorizedOrgs(
      {
        dynamoDb: props.dynamoDb,
        orgRelsTableName: props.tables.coraOrgRelationTableName,
        ppnId: props.ppnId,
      },
      logger,
    );

    //get role for user
    const isUserImp =
      getPermissionForDealer(
        {
          dealerNumber: props.newCarOrder.dealer_number,
          authorizedOrgs: authorizedOrgs,
        },
        logger,
      ) === 'Importer';

    //find org that matches dealer and importer number to the provided order
    const correspondingDealerOrg = authorizedOrgs.find(
      (org) =>
        org.dealer_number === props.newCarOrder.dealer_number &&
        org.importer_number === props.newCarOrder.importer_number,
    );
    if (!correspondingDealerOrg) {
      return { valid: false, error: 'User is not authorized for provided dealer or importer number' };
    }

    //batch get to get all required dynamodb items
    const batchGetParams = {
      RequestItems: {
        [props.tables.shippingCodeTableName]: {
          Keys: [{ pk_shipping_code: props.newCarOrder.shipping_code }],
        },
        [props.tables.orderTypeTableName]: {
          Keys: [{ pk_order_type: props.newCarOrder.order_type }],
        },
        [props.tables.importerTableName]: {
          Keys: [{ pk_importer_number: props.newCarOrder.importer_number }],
        },
        [props.tables.dealerTableName]: {
          Keys: [
            {
              pk_importer_number: props.newCarOrder.importer_number,
              sk_dealer_number: props.newCarOrder.dealer_number,
            },
          ],
        },
      },
    };
    const batchResponse = await props.dynamoDb.send(new BatchGetCommand(batchGetParams));
    const shippingCode = batchResponse.Responses?.[props.tables.shippingCodeTableName][0] as
      | CoraMdShippingCode
      | undefined;
    const orderType = batchResponse.Responses?.[props.tables.orderTypeTableName][0] as CoraMdOrderType | undefined;
    const importer = batchResponse.Responses?.[props.tables.importerTableName][0] as CoraMdImporter | undefined;
    const dealer = batchResponse.Responses?.[props.tables.dealerTableName][0] as CoraMdDealer | undefined;
    const modelTypes = await getModelTypeVisibility(
      {
        visibilityLevel: props.visibilityLevel,
        importer_number: props.newCarOrder.importer_number,
        model_type: props.newCarOrder.model_type,
        model_year: props.newCarOrder.model_year,
        mtvRespoitory: props.mtvRespoitory,
      },
      logger,
    );
    //validate existence of corresponding masterdata
    const res = performValidation(
      {
        customerRelatedOt: props.customerRelatedOt,
        dealer: dealer,
        importer: importer,
        isUserImp: isUserImp,
        modelTypes: modelTypes,
        newCarOrder: props.newCarOrder,
        orderType: orderType,
        shippingCode: shippingCode,
        skipOtDisabledCheck: props.skipOtDisabledCheck,
        skipScDisabledCheck: props.skipScDisabledCheck,
        visibilityLevel: props.visibilityLevel,
      },
      logger,
    );
    logger.log(LogLevel.INFO, 'validateNcoRequest output', { data: res });
    return res;
  } catch (err) {
    logger.log(LogLevel.WARN, `Custom validation threw an error`, { data: err });
    const res = { valid: false, error: err as string };
    logger.log(LogLevel.TRACE, 'validateNcoRequest output', { data: res });
    return res;
  }
};

export const performValidation = (
  props: {
    shippingCode: CoraMdShippingCode | undefined;
    orderType: CoraMdOrderType | undefined;
    importer: CoraMdImporter | undefined;
    dealer: CoraMdDealer | undefined;
    modelTypes: ModelTypeVisibilityModel[];
    newCarOrder: Omit<CoraNCOBaseApiRequest, 'pk_new_car_order_id'>;
    visibilityLevel: string;
    customerRelatedOt: boolean;
    isUserImp: boolean;
    skipScDisabledCheck: boolean | undefined;
    skipOtDisabledCheck: boolean | undefined;
  },
  logger: KasLambdaLogger,
): { valid: boolean; error?: string } => {
  logger.log(LogLevel.TRACE, 'performValidation input', { data: props });
  const res = _performValidation(props, logger);
  logger.log(LogLevel.TRACE, 'performValidation output', { data: res });
  return res;
};

const _performValidation = (
  props: {
    shippingCode: CoraMdShippingCode | undefined;
    orderType: CoraMdOrderType | undefined;
    importer: CoraMdImporter | undefined;
    dealer: CoraMdDealer | undefined;
    modelTypes: ModelTypeVisibilityModel[];
    newCarOrder: Omit<CoraNCOBaseApiRequest, 'pk_new_car_order_id'>;
    visibilityLevel: string;
    customerRelatedOt: boolean;
    isUserImp: boolean;
    skipScDisabledCheck: boolean | undefined;
    skipOtDisabledCheck: boolean | undefined;
  },
  logger: KasLambdaLogger,
): ValidationReturnType => {
  if (!props.importer || !props.dealer) {
    return { valid: false, error: 'Order params do not match masterdata (Imp/Dlr)' };
  }

  //validate model type and year (no access if no data found based on users visibility level or filter)
  if (props.modelTypes.length === 0) {
    logger.log(LogLevel.INFO, 'model_type validation failed', {
      data: {
        visibilityLevel: props.visibilityLevel,
        importer_number: props.newCarOrder.importer_number,
        model_type: props.newCarOrder.model_type,
        model_year: props.newCarOrder.model_year,
        cnr: props.newCarOrder.cnr,
      },
    });
    return { valid: false, error: 'Model type and year not allowed' };
  }

  const scValid = validateShippingCode({
    ...props,
    ncoDealerNumber: props.newCarOrder.dealer_number,
    ncoImporterNumber: props.newCarOrder.importer_number,
  });
  if (!scValid.valid) {
    return scValid;
  }

  const otValid = validateOrderType({
    ...props,
    ncoDealerNumber: props.newCarOrder.dealer_number,
    ncoImporterNumber: props.newCarOrder.importer_number,
  });
  if (!otValid.valid) {
    return otValid;
  }
  const pcValid = validatePortCode({
    ...props,
    ncoReceivingPortCode: props.newCarOrder.receiving_port_code,
    ncoDealerNumber: props.newCarOrder.dealer_number,
    ncoImporterNumber: props.newCarOrder.importer_number,
  });
  if (!pcValid.valid) {
    return pcValid;
  }

  //validate that, if "customerRelatedOt" is set, only allow ordertypes with that flag and vice versa
  if (
    (props.customerRelatedOt && !props.orderType!.is_customer_related) ||
    (!props.customerRelatedOt && props.orderType!.is_customer_related)
  ) {
    return { valid: false, error: 'Invalid switch of the ordertype' };
  }

  //validate importer code (handel dummy code 'DC' if code is missing for now)
  if (!props.importer.code && props.newCarOrder.importer_code !== 'DC') {
    return { valid: false, error: 'Importer has no code and order has not the dummy code' };
  } else if (props.importer.code && props.newCarOrder.importer_code !== props.importer.code) {
    return { valid: false, error: 'Importer code in order does not match importer number' };
  }

  //validate requested delivery date to be after quota month
  if (
    props.newCarOrder.requested_dealer_delivery_date &&
    new Date(props.newCarOrder.quota_month) > new Date(props.newCarOrder.requested_dealer_delivery_date)
  ) {
    return { valid: false, error: 'Quota month after requested dealer delivery date. Must be before!' };
  }

  return { valid: true };
};

export type CoraNCOOneVmsStatus = Pick<
  NewCarOrderModel,
  'order_status_onevms_code' | 'order_status_onevms_error_code'
> & {
  order_invoice_onevms_code?: string;
};

export type StatusUpdateStatement = Partial<CoraNCOOneVmsStatus>;

export const getStatusUpdateStatementsFromOutboundMapping = (
  mapping: OutboundProcessMappingModel,
): StatusUpdateStatement => {
  const updateStatements: StatusUpdateStatement = {};
  // Set order status based on mapping
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (mapping.order_status_code !== SpecialStatusCode.ALL) {
    updateStatements['order_status_onevms_code'] = mapping.order_status_code;
  }

  // Set order error status based on mapping
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (mapping.error_status_code !== SpecialStatusCode.ALL) {
    updateStatements['order_status_onevms_error_code'] = mapping.error_status_code;
  }

  // Set order invoice status based on mapping
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (mapping.invoice_status_code !== SpecialStatusCode.ALL) {
    updateStatements['order_invoice_onevms_code'] = mapping.invoice_status_code;
  }

  return updateStatements;
};

export const getStatusUpdateStatementsFromOutboundMappingDeepPartial = (
  mapping: OutboundProcessMappingModel,
): DeepPartial<NewCarOrderModel> => {
  const updateStatements: DeepPartial<NewCarOrderModel> = {};
  // Set order status based on mapping
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (mapping.order_status_code !== SpecialStatusCode.ALL) {
    updateStatements['order_status_onevms_code'] = mapping.order_status_code;
  }

  // Set order error status based on mapping
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (mapping.error_status_code !== SpecialStatusCode.ALL) {
    updateStatements['order_status_onevms_error_code'] = mapping.error_status_code;
  }

  // Set order invoice status based on mapping
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (mapping.invoice_status_code !== SpecialStatusCode.ALL) {
    updateStatements['order_invoice_onevms_code'] = mapping.invoice_status_code;
  }

  return updateStatements;
};

export const removeLocalOptionsFromNco = (nco: NewCarOrderModel): NewCarOrderModel => {
  if (nco.configuration.ordered_options.length > 0) {
    const ordered_options: NcoConfigOrderedOptionsModel[] = [];
    for (const opt of nco.configuration.ordered_options) {
      const _res = removeLocalOptionsFromOption(opt);
      if (_res) {
        ordered_options.push(_res);
      }
    }
    nco.configuration.ordered_options = ordered_options;
    // Remove PVMS Config, because we don't know how to change it
    nco.configuration_expire = undefined;
  }
  return nco;
};

export const removeLocalOptionsFromOption = (
  option: NcoConfigOrderedOptionsModel,
): NcoConfigOrderedOptionsModel | null => {
  if (option.option_type === Constants.CORA_NEW_CAR_ORDER_COFIG_OPTION_LOCAL) {
    return null;
  }
  if (option.content && option.content.length > 0) {
    const content: NcoConfigOrderedOptionsModel[] = [];
    for (const opt of option.content) {
      const _res = removeLocalOptionsFromOption(opt);
      if (_res) {
        content.push(_res);
      }
    }
    if (content.length > 0) {
      option.content = content;
    } else {
      option.content = undefined;
    }
  }
  return option;
};

export const combineStatusMappingWithOutboundMapping = (
  outboundStatusUpdateStatement: StatusUpdateStatement,
  mappingStatusUpdateStatement: CoraNCOOneVmsStatus | undefined,
  doInvoiceMapping: boolean,
): StatusUpdateStatement | undefined => {
  //prevent missing status mapping, (only) if CUSTOM is defined in outbound mapping
  if (
    !mappingStatusUpdateStatement &&
    (outboundStatusUpdateStatement.order_status_onevms_code === SpecialStatusCode.CUSTOM ||
      outboundStatusUpdateStatement.order_status_onevms_error_code === SpecialStatusCode.CUSTOM)
  ) {
    return undefined;
  }

  //use status from status mapping if CUSTOM, use status from outbound mapping otherwise
  const order_status_onevms_code =
    outboundStatusUpdateStatement.order_status_onevms_code === SpecialStatusCode.CUSTOM
      ? mappingStatusUpdateStatement!.order_status_onevms_code
      : outboundStatusUpdateStatement.order_status_onevms_code;

  const order_status_onevms_error_code =
    outboundStatusUpdateStatement.order_status_onevms_error_code === SpecialStatusCode.CUSTOM
      ? mappingStatusUpdateStatement!.order_status_onevms_error_code
      : outboundStatusUpdateStatement.order_status_onevms_error_code;

  let order_invoice_onevms_code = undefined;
  if (doInvoiceMapping) {
    //allow invoice mapping to be missing and set to NULL (logic from before)
    order_invoice_onevms_code =
      outboundStatusUpdateStatement.order_invoice_onevms_code === SpecialStatusCode.CUSTOM
        ? mappingStatusUpdateStatement?.order_invoice_onevms_code ?? SpecialStatusCode.NONE
        : outboundStatusUpdateStatement.order_invoice_onevms_code;
  }

  //only return fields that are not undefined for use as update statement
  const statusUpdateStatement: StatusUpdateStatement = {};
  if (order_status_onevms_code) statusUpdateStatement['order_status_onevms_code'] = order_status_onevms_code;
  if (order_status_onevms_error_code)
    statusUpdateStatement['order_status_onevms_error_code'] = order_status_onevms_error_code;
  if (order_invoice_onevms_code) statusUpdateStatement['order_invoice_onevms_code'] = order_invoice_onevms_code;

  return statusUpdateStatement;
};
