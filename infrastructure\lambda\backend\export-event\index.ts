import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'aws-lambda';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { secretCache } from '../../utils/secret-cache';
import { KafkaAdapter } from '../../utils/kafka';
import { getEnvVarWithAssert } from '../../utils/utils';
import { KafkaObjsWrapper, KafkaObjTyp } from '../export-nco/types';
import { processPvmsExport } from './export-handlers/process-pvms-export';
import { processStandardExport } from './export-handlers/process-standard-export';
import { createTypeORMDataSource } from '../../config/typeorm-config';
import { NewCarOrderAuditTrailModel } from '../../../lib/entities/new-car-order-audit-trail-model';
import { ObjectValidator } from '../../../lib/utils/object-validation';
import { parseAndValidateNotificationKafkaRecord } from './export-handlers/utils';
import { NotificationStatus, NotificationKafkaEvent } from '../../../lib/types/process-steering-types';
import { In } from 'typeorm';
import { processP06Export } from './export-handlers/process-p06-export';

// Env vars
const KAFKA_SECRET_ARN = getEnvVarWithAssert('KAFKA_SECRET_ARN');
const KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];
const NOTIFICATION_TOPIC = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
const STAGE = getEnvVarWithAssert('STAGE');
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');

const logger = new KasLambdaLogger('EventhandlerResultKafkaExportLambda', LogLevel.DEBUG);
const notificationEventValidator = new ObjectValidator<NotificationKafkaEvent>('NotificationKafkaEvent');

secretCache.initCache(KAFKA_SECRET_ARN, AURORA_SECRET_ARN);
const kafkaAdapter = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: KAFKA_SECRET_ARN,
  logger,
});

export const handler: Handler<MSKEvent, void> = async (event, context) => {
  logger.setRequestContext(context);
  const kafkaEvent = event;
  const parsedRecords = Object.entries(kafkaEvent.records)
    .filter(([partitionKey]) => partitionKey.includes(NOTIFICATION_TOPIC))
    .flatMap(
      ([, records]) =>
        records
          .filter((record) => {
            let ce_type = undefined;
            for (const header of record.headers) {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              if (header.ce_type) {
                ce_type = Buffer.from(header.ce_type).toString();
              }
            }
            return ce_type === `${KafkaObjTyp.NOTIFICATION}.${NotificationStatus.EVENT_HANDLER_IO}`;
          })
          .map((record) => parseAndValidateNotificationKafkaRecord(record, notificationEventValidator, logger))
          .filter((NEvent): NEvent is NotificationKafkaEvent => !!NEvent), // Type Guard
    );

  for (const notificationEvent of parsedRecords) {
    try {
      if (!notificationEvent.nco_id) {
        logger.log(LogLevel.WARN, 'Missing nco_id in NotificationKafkaEvent, not relevant for kafka export.', {
          data: notificationEvent,
        });
        continue;
      }
      const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, STAGE, [NewCarOrderAuditTrailModel]);
      const ncoAuditTrailRepo = dataSource.getRepository(NewCarOrderAuditTrailModel);
      // Load unexported audit trails for this NCO
      const ncoId = notificationEvent.nco_id;
      const auditTrails = await ncoAuditTrailRepo.findBy({
        pk_new_car_order_id: ncoId,
        action_exported: false,
      });
      logger.setCorrelationId(notificationEvent.transaction_id);
      logger.setObjectId(ncoId);

      // Perform exports to NCO, P06, PVMS and Notification topic sequentially (parallel had connection issues)
      const pvmsResult = await processPvmsExport(notificationEvent, auditTrails, kafkaAdapter, logger);
      const p06Result = await processP06Export(notificationEvent, auditTrails, kafkaAdapter, logger);
      const standardResult = await processStandardExport(notificationEvent, auditTrails, kafkaAdapter, logger);

      if (pvmsResult.failed || p06Result.failed || standardResult.failed) {
        const errorMsgs = [pvmsResult.error?.message, p06Result.error?.message, standardResult.error?.message]
          .filter(Boolean)
          .join(' | ');

        logger.log(LogLevel.ERROR, 'At least one export failed', {
          data: { pvmsResult, p06Result, standardResult },
        });

        await publishExportNotification(notificationEvent, NotificationStatus.ERROR, errorMsgs || 'unknown error');
        return;
      }
      // Mark audit trails as exported
      const allExportedIds = [
        ...new Set([...pvmsResult.exportedIds, ...p06Result.exportedIds, ...standardResult.exportedIds]),
      ];
      if (allExportedIds.length > 0) {
        await ncoAuditTrailRepo.update({ pk_new_car_order_id: In(allExportedIds) }, { action_exported: true });
        logger.log(LogLevel.INFO, 'Marked audit trails as exported', { data: allExportedIds });
      } else {
        logger.log(LogLevel.INFO, 'No Object was exported for this notification.');
        return;
      }

      // Export notification for successful export
      await publishExportNotification(notificationEvent, NotificationStatus.EXPORTED);
    } catch (err) {
      logger.log(LogLevel.ERROR, 'Export failed for notification', {
        data: err,
        correlationId: notificationEvent.transaction_id,
      });
      await publishExportNotification(
        notificationEvent,
        NotificationStatus.ERROR,
        err instanceof Error ? err.message : 'unknown error',
      );
    }
  }
};

// Helper to publish result to Notification Topic
async function publishExportNotification(
  event: NotificationKafkaEvent,
  status: NotificationStatus,
  errorMsg?: string,
): Promise<void> {
  const responseEvent: NotificationKafkaEvent = {
    ...event,
    status,
    action_at: new Date().toISOString(),
    details: errorMsg ? { error: errorMsg } : event.details,
  };

  const kafkaObjsWrapper: KafkaObjsWrapper<NotificationKafkaEvent> = {
    kafkaObjTyp: KafkaObjTyp.NOTIFICATION,
    kafkaObjs: [{ id: responseEvent.transaction_id, obj: responseEvent }],
    kafkaActionTyp: status,
  };

  await kafkaAdapter.pushObjsToTopic({
    topic: NOTIFICATION_TOPIC,
    kWrapper: kafkaObjsWrapper,
    correlationid: event.transaction_id,
  });

  logger.log(LogLevel.INFO, `Notification with status ${status} published`, { data: responseEvent });
}
