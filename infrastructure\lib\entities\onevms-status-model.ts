import { Column, Entity, PrimaryColumn } from 'typeorm';
import { CoraMdOneVmsStatusType } from '../types/masterdata-types';
import { Constants } from '../utils/constants';
import { BaseModel } from './base-model';

@Entity({ name: 'onevms_status', schema: Constants.CORA_MD_AURORA_SCHEMA })
export class OneVmsStatusModel extends BaseModel {
  @PrimaryColumn({ type: 'text', update: false })
  public status_code: string;

  @Column({ type: 'text', update: false })
  public status_type: CoraMdOneVmsStatusType;

  @Column({ type: 'text' })
  public status_description_EN: string;

  @Column({ type: 'text' })
  public status_description_DE: string;

  @Column({ type: 'bool', nullable: true })
  public is_deactivated?: boolean;
}
