import { <PERSON>electWrapper, PText } from '@porsche-design-system/components-react';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useCancelNewCarOrdersMutation } from '../../store/api/NewCarOrderApi';
import { useGetCancelReasonsQuery } from '../../store/api/StaticJsonApi';
import { OrderCancelReason } from '../../store/types';
import { CommonOrderActionProps } from '../shared/order-action-common-modal/OrderActionCommonModal';
import { CancelNcoApiRequest, NcoIdWithModifiedAt } from '../../../../infrastructure/lib/types/process-steering-types';
import ProcessSteeringOrderActionModal from '../shared/process-steering-components/order-action-common-modal/ProcessSteeringOrderActionModal';

const CancelOrdersModal: React.FC<CommonOrderActionProps> = (props: CommonOrderActionProps) => {
  const { t } = useTranslation();
  const [selectedCancelReason, setSelectedCancelReason] = useState<OrderCancelReason | null>(null);
  const orderCancelReasonsQuery = useGetCancelReasonsQuery(undefined);

  const [cancelOrders, cancelResult] = useCancelNewCarOrdersMutation();

  const CancelReasonComponent: React.FC = () => {
    return (
      <div className="order-form-container">
        <PText size={'small'}>{t('select_cancel_reason') + '*'}</PText>
        <PSelectWrapper
          dropdownDirection="auto"
          data-e2e="SelectCancelReason"
          filter={true}
          style={{ flexBasis: 'calc(50% - 10px)' }}
        >
          <select
            required
            disabled={orderCancelReasonsQuery.isLoading || !orderCancelReasonsQuery.data || cancelResult.isLoading}
            value={selectedCancelReason?.id}
            onChange={(e) => {
              const selectedReason = orderCancelReasonsQuery.data?.find((ocr) => ocr.id === e.target.value);
              if (selectedReason) {
                setSelectedCancelReason(selectedReason);
              }
            }}
          >
            <option>{t('cancel_reason_placeholder')}</option>
            {orderCancelReasonsQuery.data?.map((reason) => (
              <option key={reason.id} value={reason.id}>
                {`${reason.id} - ${reason.beschreibung}`}
              </option>
            ))}
          </select>
        </PSelectWrapper>
      </div>
    );
  };

  function handleSubmit(): void {
    const nco_ids_with_modified_at: NcoIdWithModifiedAt[] = props.orders.map((order) => ({
      pk_new_car_order_id: order.pk_new_car_order_id,
      modified_at: order.modified_at!,
    }));
    const apiRequest: CancelNcoApiRequest = {
      nco_ids_with_modified_at: nco_ids_with_modified_at,
      cancellation_reason: selectedCancelReason!.id,
    };
    cancelOrders({ data: apiRequest });
  }

  return (
    <ProcessSteeringOrderActionModal
      isLoading={cancelResult.isLoading}
      isError={cancelResult.isError}
      isSuccess={cancelResult.isSuccess}
      orders={props.orders}
      error={cancelResult.error}
      result={cancelResult.data}
      actionType={props.actionType}
      closeModal={props.closeModal}
      handleSubmit={handleSubmit}
      CustomComponent={CancelReasonComponent}
      isSubmitDisabled={cancelResult.isLoading || !selectedCancelReason}
    />
  );
};

export default CancelOrdersModal;
