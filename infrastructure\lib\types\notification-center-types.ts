import { NotificationStatus, OneVmsEventKey } from './process-steering-types';

export interface Transaction {
  action_by: string;
  action_at: string;
  transaction_id: string;
  amount_of_objects: number;
  event_type: OneVmsEventKey;
  hidden: boolean;
  last_update: string;
}

export interface SubTransaction {
  transaction_id: string;
  uuid: string;
  sub_transaction_id: string;
  obj_id?: string;
  details?: unknown;
  status: NotificationStatus;
  last_update: string;
}

export interface NotificationTransactionGetAllQueryParams {
  /**
   * @format date-time
   */
  from?: string;
  /**
   * @format date-time
   */
  until?: string;
  show_hidden?: string;
  action_type?: OneVmsEventKey;
}

export type NotificationTransactionPatchRequest = Pick<Transaction, 'transaction_id'> &
  Pick<Partial<Transaction>, 'hidden'>;

export interface NotificationTransactionResponseItem extends Transaction {
  sub_transactions: Omit<SubTransaction, 'transaction_id'>[];
}
export type NotificationTransactionResponse = NotificationTransactionResponseItem[];
