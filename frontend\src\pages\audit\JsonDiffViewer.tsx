import React, { PureComponent, useEffect, useRef } from 'react';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer-continued';

export interface DiffViewerProps {
  oldJson: Object;
  newJson: Object;
}
const JsonDiffViewer: React.FC<DiffViewerProps> = (props: DiffViewerProps) => {
  const viewer = useRef<ReactDiffViewer>(null);
  useEffect(() => {
    viewer.current?.resetCodeBlocks();
  }, [props.oldJson, props.newJson]);
  // Does not work sometimes so disabled
  // const highlightSyntax = (str: string) => (
  //   <pre
  //     style={{ display: 'inline' }}
  //     dangerouslySetInnerHTML={{
  //       __html: Prism.highlight(str, Prism.languages.javascript, 'javascript'),
  //     }}
  //   />
  // );
  return (
    <ReactDiffViewer
      ref={viewer}
      oldValue={props.oldJson}
      newValue={props.newJson}
      splitView={true}
      compareMethod={DiffMethod.JSON}
    />
  );
};

export default JsonDiffViewer;
