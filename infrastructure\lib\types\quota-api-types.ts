export interface QuotaApiConsumeBody {
  orders_to_consume: {
    new_car_order_id: string;
    model_year: string;
    model_type_code: string;
    importer_number: number;
    dealer_number: number;
    quota_month: string;
  }[];
}

/**
 * This is the response from the Quota API when consuming a or multiple quota.
 *
 * 1. It will return a boolean indicating if the quota was consumed or not.
 *    For multi consume, it will return true on full or partial consumption.
 * 2. The processed_quota_entries will indicate how many quota entries were successfully processed.
 * 3. The consumed_new_car_orders will return the new car order ids that were consumed.
 *
 * In conlusion, only the quota_consumed will be important for the single consume,
 * while the processed_quota_entries and consumed_new_car_orders will be important for the multi consume.
 *
 */
export interface QuotaApiConsumeResponse {
  quota_consumed: boolean;
  validation_response?: {
    processed_quota_entries: number;
    errors: Record<string, QuotaApiConsumeErr[]>;
    consumed_new_car_orders: string[];
  };
}

interface QuotaApiConsumeErr {
  quota_month: string;
  model_type_group: string;
  model_year: number;
  dealer_number: string;
  importer_number: string;
  error_message: string;
  error_code: QuotaApiErrorCode;
}

type QuotaApiErrorCode =
  | 'ERROR_0000'
  | 'ERROR_0001'
  | 'ERROR_0002'
  | 'ERROR_0403'
  | 'ERROR_2100'
  | 'ERROR_2101'
  | 'ERROR_2102'
  | 'ERROR_2103'
  | 'ERROR_2104'
  | 'ERROR_2001'
  | 'ERROR_2000'
  | 'ERROR_1003'
  | 'ERROR_1001'
  | 'ERROR_1000'
  | 'ERROR_1002'
  | 'ERROR_1005'
  | 'ERROR_3004'
  | 'ERROR_3005'
  | 'ERROR_3006'
  | 'ERROR_3007'
  | 'ERROR_3008'
  | 'ERROR_3009'
  | 'ERROR_3010'
  | 'ERROR_3011'
  | 'ERROR_3012'
  | 'ERROR_1009'
  | 'ERROR_1010';

export interface KafkaQuotaEventValue {
  consumed_new_car_order_ids: string[];
  /**
   * @format iso-date-time
   */
  created_at: string;
  dealer_number: number;
  importer_number: number;
  /**
   * @format iso-date-time
   */
  last_modified_at?: string;
  last_modified_by?: string;
  model_type_group: string;
  model_year: number;
  quota_consumed: number;
  quota_count: number;
  quota_id: string;
  /**
   * @pattern ^\d\d\d\d-\d\d$
   */
  quota_month: string;
  quota_open: number;
  model_type_groups: KafkaQuotaModelTypeGroup[];
}

export interface KafkaQuotaModelTypeGroup {
  consumed_new_car_order_ids: string[];
  model_type_group: string;
  model_year: number;
  quota_consumed: number;
  model_type_codes: string[]; //6-digit model types
}

// TODO What is this?
export interface CoraQuota {
  consumed_new_car_order_ids: string[];
  created_at: string;
  created_by: string; //does not exist in quota topic
  last_modified_at: string;
  last_modified_by?: string;
  dealer_number: number;
  importer_number: number;
  model_type: string;
  model_year: number;
  quota_consumed: number;
  quota_count: number;
  quota_id_without_month: string;
  quota_id: string;
  quota_month: string;
  quota_open: number;
}
