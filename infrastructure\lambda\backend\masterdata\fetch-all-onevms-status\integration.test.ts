import { OneVmsStatusModel } from '../../../../lib/entities/onevms-status-model';
import {
  buildLambdaArn,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
} from '../../../utils/integration-test-helpers';
import { CoraMdOneVmsStatusType } from '../../../../lib/types/masterdata-types';
import { DataSource, In } from 'typeorm';
const lambdaArn = buildLambdaArn('fetch-all-onevms-status');
const testStatuses: OneVmsStatusModel[] = [
  {
    status_code: 'PP1234',
    status_type: CoraMdOneVmsStatusType.Order,
    status_description_EN: 'Order Checks executed 1 - No PAG Quota',
    status_description_DE: 'Auftragsprüfungen durchgeführt 1 - Keine PAG Quote',
    is_deactivated: false,
    created_by: 'tester',
    modified_by: 'tester',
  },
  {
    status_code: 'PP5678',
    status_type: CoraMdOneVmsStatusType.Order,
    status_description_EN: 'Order Checks executed 2 - No PAG Quota',
    status_description_DE: 'Auftragsprüfungen durchgeführt 2 - Keine PAG Quote',
    is_deactivated: false,
    created_by: 'tester',
    modified_by: 'tester',
  },
  {
    status_code: 'PP9876',
    status_type: CoraMdOneVmsStatusType.Order,
    status_description_EN: 'Order Checks executed 3 - No PAG Quota',
    status_description_DE: 'Auftragsprüfungen durchgeführt 3 - Keine PAG Quote',
    is_deactivated: true,
    created_by: 'tester',
    modified_by: 'tester',
  },
];
let dataSource: DataSource;
beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([OneVmsStatusModel]);

  //save inbound status mapping
  await dataSource.getRepository(OneVmsStatusModel).save(testStatuses);
}, 120000);

afterAll(async () => {
  //delete inbound status mapping
  const statusCodes = testStatuses.map((t) => t.status_code);
  await dataSource.getRepository(OneVmsStatusModel).delete({
    status_code: In(statusCodes),
  });
  await dataSource.destroy();
}, 120000);

describe('Lambda Integration: get-onevms-status', () => {
  it('should return only active statuses (is_deactivated null or false)', async () => {
    const event = createApiGwEvent({});
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(200);
    const body = JSON.parse(res.body) as OneVmsStatusModel[];
    const codes = body.map((item) => item.status_code);
    expect(codes).toContain('PP1234');
    expect(codes).toContain('PP5678');
    expect(codes).not.toContain('PP9876');
  });
});
