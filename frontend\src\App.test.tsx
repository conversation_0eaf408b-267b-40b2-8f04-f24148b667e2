import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter, MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import App from './App';
import { store } from './store/configureStore';
import { PorscheDesignSystemProvider } from '@porsche-design-system/components-react';
import AuthContextProvider from './app/AuthContext';

describe('App Component', () => {
  it('renders the heading inside a router and Redux provider', () => {
    render(
      <Provider store={store}>
        <BrowserRouter>
          <PorscheDesignSystemProvider>
            <AuthContextProvider>
              <App />
            </AuthContextProvider>
          </PorscheDesignSystemProvider>
        </BrowserRouter>
      </Provider>,
    );
  });
});
