import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  BossOrg<PERSON>odel,
  BossOrgModelKey,
  BossOrgModelWithTime,
  BossOriginOrgModel,
} from '../../../../lib/types/boss-org-types';
import { BatchGetCommand, BatchWriteCommand } from '@aws-sdk/lib-dynamodb';
import { GeneralError } from '../../../utils/errors';
import { correlationHeader, getEnvVarWithAssert, splitIntoBatches } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

const ORG_TOPIC = getEnvVarWithAssert('TOPIC_BOSS_ORG');
const BOSS_ORG_TABLE_NAME = getEnvVarWithAssert('TABLE_NAME_BOSS_ORG');

const dbClient = new DynamoDBClient({});
const logger = new KasLambdaLogger('boss-orgs-sync', LogLevel.TRACE);

/**
 * This lambda aims to process the receiving boss orgs and save them for the later flat hierarchy calculation
 * @param event
 */
export const handler: Handler<MSKEvent, void> = async (event, context) => {
  logger.setRequestContext(context);
  const orgs = parseOrgsFromEvent(event);
  logger.setCorrelationId(context.awsRequestId);
  const filteredOrgs = filterOrgs(orgs);
  logger.log(LogLevel.DEBUG, `Received ${orgs.length} orgs from Boss and after filtering left ${filteredOrgs.length}`);

  const toPutOrgs = await processDeactivatedOrgs(filteredOrgs);
  await writeBossOrgsToDynamoDB(toPutOrgs);
};

/**
 * This function performs three primary tasks:
 * 1. Parse Event: From the original Kafka event, only the relevant properties are retained.
 *    If an org is deactivated, only the ppn_id is received.
 * 2. Sort: Kafka batch events are fetched. This means that within a group of events,
 *    there could be multiple events (actions on an org) referring to the same org. Especially for the first batch import!
 *    We are only concerned with the final state of the org.
 * 3. Map: The data is mapped to the DynamoDB data model.
 *
 * @param event
 */
function parseOrgsFromEvent(event: MSKEvent): BossOrgModel[] {
  return Object.entries(event.records)
    .filter(([key]) => key.includes(ORG_TOPIC))
    .flatMap(([, value]) => value.map(parseRecord))
    .filter(Boolean)
    .sort((a, b) => (a!.kafka_timestamp > b!.kafka_timestamp ? 1 : -1))
    .map((org) => ({
      pk_ppn_id: org?.pk_ppn_id,
      parent_ppn_id: org?.parent_ppn_id,
      additional_children: org?.additional_children,
      ppn_importer_partner_number: org?.ppn_importer_partner_number,
      ppn_porsche_partner_no: org?.ppn_porsche_partner_no,
      is_deactivated: org?.is_deactivated,
      display_name: org?.display_name,
      ppn_status: org?.ppn_status,
      is_relevant_for_order_create: org?.is_relevant_for_order_create,
    })) as BossOrgModel[];
}

/**
 * If an org is deactivated, we receive only the org's ppn_id as the MSKRecord key.
 * In this case, we set other properties at first to null.
 *
 * @param record
 */
function parseRecord(record: MSKRecord): BossOrgModelWithTime | undefined {
  try {
    const { value, key, timestamp } = record;

    if (value) {
      const kafkaBossOrgObj = JSON.parse(Buffer.from(value, 'base64').toString('utf8')) as BossOriginOrgModel;
      return {
        pk_ppn_id: kafkaBossOrgObj.ppn_id,
        parent_ppn_id: kafkaBossOrgObj.ppn_parent_id ?? '',
        additional_children: kafkaBossOrgObj.boss_additional_children,
        is_deactivated: false,
        ppn_importer_partner_number: kafkaBossOrgObj.ppn_importer_partner_number ?? '',
        ppn_porsche_partner_no: kafkaBossOrgObj.ppn_porsche_partner_no ?? '',
        display_name: kafkaBossOrgObj.ppn_display_name ?? 'N/A',
        kafka_timestamp: timestamp,
        ppn_status: kafkaBossOrgObj.ppn_status,
        is_relevant_for_order_create: kafkaBossOrgObj.is_relevant_for_order_create,
      };
    } else {
      return {
        pk_ppn_id: Buffer.from(key, 'base64').toString('utf8'),
        parent_ppn_id: null,
        additional_children: null,
        ppn_importer_partner_number: null,
        ppn_porsche_partner_no: null,
        is_deactivated: true,
        display_name: '',
        kafka_timestamp: timestamp,
        ppn_status: null,
        is_relevant_for_order_create: 'undefined',
      };
    }
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Could not parse boss org', {
      data: record,
      correlationId: correlationHeader(record),
    });
    throw new GeneralError({
      message: 'Unable to parse record',
      causedBy: e,
    });
  }
}

/**
 * Find out the same bossOrgs and calculate the final states
 * @param orgs
 */
function filterOrgs(orgs: BossOrgModel[]): BossOrgModel[] {
  const orgsRecord: Record<string, BossOrgModel[]> = {};
  orgs.forEach((org) => {
    if (!(org.pk_ppn_id in orgsRecord)) {
      orgsRecord[org.pk_ppn_id] = [org];
    } else {
      orgsRecord[org.pk_ppn_id].push(org);
    }
  });

  return Object.keys(orgsRecord).map((key) => {
    return orgsRecord[key].reduce((endOrg, org) => {
      if (org.is_deactivated) {
        endOrg.pk_ppn_id = org.pk_ppn_id;
        endOrg.is_deactivated = org.is_deactivated;
      } else {
        endOrg = org;
      }
      return endOrg;
    });
  });
}

/**
 * Find out the deactivated orgs that lack other properties,
 * then fetch the orgs from the db and fill up the other properties
 * @param filteredOrgs
 */
async function processDeactivatedOrgs(filteredOrgs: BossOrgModel[]): Promise<BossOrgModel[]> {
  const toPutOrgs: BossOrgModel[] = [];
  //Collect all the ppnIds of the deactivated orgs whose other properties are null
  const deactIds = new Set<string>();
  filteredOrgs.forEach((org) => {
    if (org.is_deactivated && org.parent_ppn_id === null) {
      deactIds.add(org.pk_ppn_id);
    } else {
      toPutOrgs.push(org);
    }
  });

  /**
   * Fetch the deactivated orgs from db and update the 'is_deactivated' to true
   */
  if (deactIds.size > 0) {
    logger.log(LogLevel.DEBUG, `Received ${deactIds.size} deactivated orgs`);
    const deactIdsArr: BossOrgModelKey[] = [];
    deactIds.forEach((deactId) => {
      deactIdsArr.push({
        pk_ppn_id: deactId,
      });
    });

    const batchGetParams = {
      RequestItems: {
        [BOSS_ORG_TABLE_NAME]: {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          Keys: [...deactIdsArr],
        },
      },
    };

    const response = await dbClient.send(new BatchGetCommand(batchGetParams)).catch((err) => {
      logger.log(LogLevel.ERROR, 'Fetching boss-org failed', { data: err as unknown });
      throw new GeneralError({
        message: `Failed to fetch deactivated orgs from ${BOSS_ORG_TABLE_NAME}`,
        causedBy: err,
      });
    });
    if (!response.Responses?.[BOSS_ORG_TABLE_NAME]) {
      throw new GeneralError({
        message: `Returned no data by fetching deactivated orgs from ${BOSS_ORG_TABLE_NAME} `,
      });
    }
    const responseOrgs = response.Responses[BOSS_ORG_TABLE_NAME] as BossOrgModel[];
    const toDeactOrgs = responseOrgs.map((record) => ({
      ...record,
      is_deactivated: true,
    })) as BossOrgModel[];
    toPutOrgs.push(...toDeactOrgs);
  }
  return toPutOrgs;
}

async function writeBossOrgsToDynamoDB(toPutOrgs: BossOrgModel[]): Promise<void> {
  if (toPutOrgs.length > 0) {
    const toPutBossOrgBatches = splitIntoBatches(toPutOrgs);
    for (const batch of toPutBossOrgBatches) {
      const batchParams = {
        RequestItems: {
          [BOSS_ORG_TABLE_NAME]: batch.map((org) => ({
            PutRequest: { Item: org },
          })),
        },
      };

      try {
        await dbClient.send(new BatchWriteCommand(batchParams));
        logger.log(LogLevel.INFO, `Successfully updated the table ${BOSS_ORG_TABLE_NAME}`);
      } catch (err) {
        batch.forEach((bom) => {
          logger.log(LogLevel.ERROR, 'Failed to save BossOrgModel', { data: bom });
        });
        throw new GeneralError({
          message: 'Failed to execute writeRequests',
          causedBy: err,
        });
      }
    }
  }
}
