# Required metadata
sonar.projectKey=KASHEARTBE-cora
sonar.projectName=cora
sonar.qualitygate.wait=true
sonar.host.url=https://skyway.porsche.com/sonarqube

# This property is used by SonarQube to track the evolution of your project's
# code quality over time. It helps you identify which version of your
# project a specific analysis belongs to.
# Another way do define this property is via command-line:
#   sonar:sonar -Dsonar.projectVersion=1.0.0
# sonar.projectVersion=1.0.0

# Optional
# Excluding specific files or directories
#----- Exclusions -----#
sonar.exclusions=\
  node_modules/**,\
  **/*.test.ts,\
  **/*.test.tsx,\
  **/*.spec.ts,\
  **/*.spec.tsx

sonar.javascript.lcov.reportPaths=infrastructure/coverage/lcov.info

# Optional
# Set the encoding of the source files
# sonar.sourceEncoding=UTF-8

# Optional
# Set the path to the Maven installation directory if not in the PATH
# sonar.maven.home=/path/to/maven/installation