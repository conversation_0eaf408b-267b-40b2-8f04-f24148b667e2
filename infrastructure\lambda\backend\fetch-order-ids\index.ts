import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';

import { sanitizeApiGwEvent, sendFail, sendSuccess } from '../../utils/api-helpers';
import { getAllowedDealers } from '../../utils/validation-helpers';
import { getEnvVarWithAssert } from '../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../utils/api-gw-handler';
import { createTypeORMDataSource } from '../../config/typeorm-config';
import { secretCache } from '../../utils/secret-cache';
import { ObjectValidator } from '../../../lib/utils/object-validation';
import { CoraNCOIdsQueryApiRequest } from '../../../lib/types/new-car-order-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../lib/entities/new-car-order-model';
import { Repository } from 'typeorm';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const objectValidator = new ObjectValidator<CoraNCOIdsQueryApiRequest>('CoraNCOIdsQueryApiRequest');

const fetchOrderIdsFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  logger.log(LogLevel.DEBUG, 'Fetching new car order ids for dealer', { data: sanitizeApiGwEvent({ event }, logger) });

  const queryParameters = event.queryStringParameters ?? {};
  const [validatedFilters, validation_errors] = objectValidator.validate(queryParameters);
  if (validatedFilters === null) {
    logger.log(LogLevel.WARN, 'ajv validation failed', { data: validation_errors });
    return sendFail(
      {
        message: 'Invalid Queryparameters. Validation failed with: ' + JSON.stringify(validation_errors),
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  logger.log(LogLevel.DEBUG, 'Validated filters', { data: validatedFilters });

  const dealerNumber = validatedFilters.dealer_number;

  const allowedDealers = await getAllowedDealers({ dynamoDb, event, includeDeactivated: true }, logger);

  if (allowedDealers.length === 0) {
    return sendFail(
      { message: 'User is not authorized to any dealers', status: 403, reqHeaders: event.headers },
      logger,
    );
  }
  if (!allowedDealers.some((dealer) => dealer.dealer_number === dealerNumber)) {
    return sendFail(
      { message: `User is not authorized for the dealer ${dealerNumber}`, status: 403, reqHeaders: event.headers },
      logger,
    );
  }

  const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, 'prod', [
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
  ]);
  const ncoRepo = dataSource.getRepository(NewCarOrderModel);
  const newCarOrders: NewCarOrderModel[] = [];
  try {
    const foundOrders = await findNotFinishedOrders(ncoRepo, dealerNumber);
    newCarOrders.push(...foundOrders);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Failed to fetch new car order ids', { data: error });
    return sendFail(
      {
        message: `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`,
        status: 500,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  const newCarOrderIds = newCarOrders.map((order) => order.pk_new_car_order_id);

  logger.log(LogLevel.DEBUG, 'Found new car order IDs:', { data: newCarOrderIds });
  return sendSuccess(
    {
      body: { new_car_order_ids: newCarOrderIds },
      reqHeaders: event.headers,
    },
    logger,
  );
};

async function findNotFinishedOrders(
  ncoRepo: Repository<NewCarOrderModel>,
  dealerNumber: string,
): Promise<NewCarOrderModel[]> {
  const foundOrders = await ncoRepo
    .createQueryBuilder('order')
    .select('order.pk_new_car_order_id')
    .where('order.dealer_number = :dealerNumber', { dealerNumber })
    .andWhere('order.order_status_onevms_code NOT LIKE :ocPrefix', { ocPrefix: 'OC%' })
    .andWhere('order.order_status_onevms_code NOT LIKE :oxPrefix', { oxPrefix: 'OX%' })
    .getMany();
  return foundOrders;
}

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('fetch-new-car-order-ids-for-dealer', LogLevel.TRACE)(event, context, fetchOrderIdsFunc);
