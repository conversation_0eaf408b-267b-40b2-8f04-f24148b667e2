import { DataSource, Repository } from 'typeorm';
import {
  buildLambdaArn,
  createSqsEvent,
  initDataSourceForIntTest,
  invokeGenericLambda,
} from '../../../../../utils/integration-test-helpers';
import { v4 as uuidv4 } from 'uuid';
import { CoraPurchaseIntentionModel } from '../../../../../../lib/entities/purchase-intention-model';
import { OneVmsSourceSystemKey, SQSBatchResponseWithError } from '../../../../../../lib/types/process-steering-types';
import { PvmsOrderDataDTOTransaction } from '../../../../../../lib/types/pvms-types';

const lambdaArn = buildLambdaArn('upsert-pvms-pi-data');

// Test data
const unconvertedPi: CoraPurchaseIntentionModel = {
  purchase_intention_id: 'PVMSIT1',
  dealer_number: '123456',
  importer_number: '12345',
  importer_code: 'DE1',
  model_type: 'MTDUMMY1',
  model_year: '2024',
  cnr: 'C23',
  quota_month: '2024-01',
  order_type: 'OT1',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTest',
  modified_by: 'IntegrationTest',
  vehicle_status_code: 'V070',
  is_converted: false,
  vehicle_configuration_pvmsnext: { testProp1: 'Test1' },
  vehicle_configuration_onevms: null,
  seller: '123456',
  business_partner_id: '0020000866',
};

const convertedPi: CoraPurchaseIntentionModel = {
  ...unconvertedPi,
  purchase_intention_id: 'PVMSIT2',
  model_type: 'MTDUMMY2',
  is_converted: true,
  vehicle_configuration_pvmsnext: { testProp2: 'Test2' },
};

// Default PVMS event
const defaultPvmsEvent: PvmsOrderDataDTOTransaction = {
  transaction_id: uuidv4(),
  ids: {
    new_car_order_id: 'PVMSIT3',
    vguid_pvms_DEPRECATED: '051Mkmoc7j{wqhMTY0uewW', //works for now but for how long?
    business_partner_id: '123456',
  },
  order_info: {
    trading_partner: {
      dealer_sold_to_number: '123456',
      dealer_ship_to_number: '123456',
      importer_number: '12345',
      importer_code: 'DE1',
    },
    base_info: {
      order_type: 'OT1',
      quota_month: '202401',
    },
    status_info: {
      order_status_pvms_code: 'O070',
      vehicle_status_pvms_code: 'V070',
      vehicle_status_pvms_timestamp: '2024-01-01T00:00:00Z',
      order_status_pvms_timestamp: '2024-01-02T00:00:00Z',
    },
    sales_info: {
      sales_person_id: '123456',
    },
  },
  model_info: {
    model_type: 'MTDUMMY3',
    model_year: 2024,
    country_code: 'C23',
  },
  logistics_info: {
    shipping_code: 'SC1',
    receiving_port_code: 'PC1',
  },
  appointment_date_info: {
    production_logistic_dates: {
      order_creation_date: new Date().toISOString(),
      requested_dealer_delivery_date: '2024-01-01',
    },
  },
};

let dataSource: DataSource;
let piRepo: Repository<CoraPurchaseIntentionModel>;

describe('PVMS PI Upsert Event Handler Integration Test', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([CoraPurchaseIntentionModel]);
    piRepo = dataSource.getRepository(CoraPurchaseIntentionModel);
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  beforeEach(async () => {
    // Save/reset test data
    await piRepo.save([unconvertedPi, convertedPi]);
  });

  afterEach(async () => {
    // Cleanup test data
    await piRepo.delete([
      unconvertedPi.purchase_intention_id,
      convertedPi.purchase_intention_id,
      defaultPvmsEvent.ids.new_car_order_id,
    ]);
  });

  it('Should fail if SQS event is faulty', async () => {
    const event = createSqsEvent([
      {
        ...defaultPvmsEvent,
        order_info: undefined, // Make event invalid
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was NOT created
    const pi = await piRepo.findOneBy({ purchase_intention_id: defaultPvmsEvent.ids.new_car_order_id });
    expect(pi).toBeNull();
  });

  it('Should fail if SQS event does not include a PI (wrong vehicle status)', async () => {
    const event = createSqsEvent([
      {
        ...defaultPvmsEvent,
        order_info: {
          ...defaultPvmsEvent.order_info,
          status_info: {
            ...defaultPvmsEvent.order_info.status_info,
            order_status_pvms_code: 'O070',
            vehicle_status_pvms_code: 'WROOONG', // Not a PI status
          },
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was NOT created
    const pi = await piRepo.findOneBy({ purchase_intention_id: defaultPvmsEvent.ids.new_car_order_id });
    expect(pi).toBeNull();
  });

  it('Should fail to update if PI exists and is_converted is true', async () => {
    const event = createSqsEvent([
      {
        ...defaultPvmsEvent,
        ids: {
          ...defaultPvmsEvent.ids,
          new_car_order_id: convertedPi.purchase_intention_id,
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI wasn't changed
    const pi = await piRepo.findOneBy({ purchase_intention_id: convertedPi.purchase_intention_id });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toBe(true);
    expect(pi?.model_type).toBe(convertedPi.model_type);
  });

  it('Should successfully update existing PI that is not converted', async () => {
    const event = createSqsEvent([
      {
        ...defaultPvmsEvent,
        ids: {
          ...defaultPvmsEvent.ids,
          new_car_order_id: unconvertedPi.purchase_intention_id,
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was updated
    const pi = await piRepo.findOneBy({ purchase_intention_id: unconvertedPi.purchase_intention_id });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toBe(false);
    expect(pi?.modified_by).toBe(OneVmsSourceSystemKey.PVMS);
    expect(pi?.model_type).toBe(defaultPvmsEvent.model_info.model_type);
    expect(pi?.vehicle_configuration_pvmsnext).toBeDefined();
    expect((pi?.vehicle_configuration_pvmsnext as Record<string, unknown>).testProp1).toBeUndefined();
  });

  it('Should successfully create new PI', async () => {
    const event = createSqsEvent([defaultPvmsEvent]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was created
    const pi = await piRepo.findOneBy({ purchase_intention_id: defaultPvmsEvent.ids.new_car_order_id });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toBe(false);
    expect(pi?.created_by).toBe(OneVmsSourceSystemKey.PVMS);
    expect(pi?.modified_by).toBe(OneVmsSourceSystemKey.PVMS);
    expect(pi?.model_type).toBe(defaultPvmsEvent.model_info.model_type);
    expect(pi?.vehicle_configuration_pvmsnext).toBeDefined();
  });
});
