import { APIGatewayProxyEvent } from 'aws-lambda';
import { mockClient } from 'aws-sdk-client-mock';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { auroraUnitTestSecret, mockContext } from '../../../utils/test-utils';
import { DataSource } from 'typeorm';
import { KasLambdaLogger } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { CoraPurchaseIntentionModel } from '../../../../lib/entities/purchase-intention-model';
import { createTypeORMDataSource } from '../../../config/typeorm-config';

const ddbMock = mockClient(DynamoDBClient);
const smMock = mockClient(SecretsManagerClient);
smMock.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(auroraUnitTestSecret) });

import { handler } from './index';
import { event } from '../../../../test/test-types';
import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import coraOrgRelData from '../../../../test/data/cora_org_rel.json';
import { CoraNCPurchaseIntentionApiResponse } from '../../../../lib/types/purchase-intention-types';

const exisitingDealId = 'CHZZ2345';

async function setupDatabase(): Promise<DataSource> {
  const dataSource = await createTypeORMDataSource(new KasLambdaLogger('test'), 'aurorasecretarn', 'test', [
    CoraPurchaseIntentionModel,
  ]);

  // Add test data for purchase intentions
  const purchaseIntentionRepository = dataSource.getRepository(CoraPurchaseIntentionModel);
  const _pi_insert = purchaseIntentionRepository.create({
    purchase_intention_id: exisitingDealId,
    order_type: 'KF',
    shipping_code: '1',
    model_type: '992110',
    model_year: '2024',
    quota_month: '2024-02',
    dealer_number: '3030001',
    importer_number: '2020001',
    seller: '300822',
    business_partner_id: '0900175729',
    receiving_port_code: null,
    requested_dealer_delivery_date: null,
    vehicle_status_code: 'V070',
    cnr: 'C10',
    importer_code: 'CH',
    created_by: 'PVMS',
    modified_by: 'PVMS',
    is_converted: false,
  });

  await purchaseIntentionRepository.save(_pi_insert);
  return dataSource;
}

describe('getPurchaseIntentionFunc Local Database Integration Test', () => {
  let dataSource: DataSource | undefined = undefined;

  beforeAll(async () => {
    dataSource = await setupDatabase();
  });

  afterAll(async () => {
    if (dataSource) {
      await dataSource.destroy(); // Clean up the database connection
    }
  });

  it('should return purchase intention successfully', async () => {
    const ppnId = 'ppn_id-IMPORTER_01';
    ddbMock
      .on(QueryCommand)
      .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });

    const _event: APIGatewayProxyEvent = {
      ...event,
      pathParameters: {
        purchaseIntentionId: exisitingDealId,
      },
      requestContext: {
        ...event.requestContext,
        authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
      },
      queryStringParameters: {},
    };

    const result = await handler(_event, mockContext, () => {
      return;
    });

    expect(result).toBeDefined();
    expect(result?.statusCode).toBe(200);
    const _result = JSON.parse(result?.body ?? '{}') as CoraNCPurchaseIntentionApiResponse;
    expect(_result.purchase_intention_id).toBe(exisitingDealId);
    expect(_result.dealer_name).toBeTruthy();
  });

  it('should return 404 if purchase intention is not found', async () => {
    const ppnId = 'ppn_id-IMPORTER_01';
    const _event: APIGatewayProxyEvent = {
      ...event,
      pathParameters: {
        purchaseIntentionId: 'DE112233',
      },
      requestContext: {
        ...event.requestContext,
        authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
      },
      queryStringParameters: {},
    };

    const result = await handler(_event, mockContext, () => {
      return;
    });

    expect(result).toBeDefined();
    expect(result?.statusCode).toBe(404);
  });

  it('should return 400 if purchase intention is invalid format', async () => {
    const ppnId = 'ppn_id-IMPORTER_01';
    const _event: APIGatewayProxyEvent = {
      ...event,
      pathParameters: {
        purchaseIntentionId: 'giff_purchase',
      },
      requestContext: {
        ...event.requestContext,
        authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
      },
      queryStringParameters: {},
    };

    const result = await handler(_event, mockContext, () => {
      return;
    });

    expect(result).toBeDefined();
    expect(result?.statusCode).toBe(400);
  });

  it('should return 404 if this user does not have permission to view purchase intention', async () => {
    const ppnId = 'ppn_id-NotInData';
    ddbMock
      .on(QueryCommand)
      .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });

    const _event: APIGatewayProxyEvent = {
      ...event,
      pathParameters: {
        purchaseIntentionId: exisitingDealId,
      },
      requestContext: {
        ...event.requestContext,
        authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
      },
      queryStringParameters: {},
    };

    const result = await handler(_event, mockContext, () => {
      return;
    });

    expect(result).toBeDefined();
    expect(result?.statusCode).toBe(404);
  });
});
