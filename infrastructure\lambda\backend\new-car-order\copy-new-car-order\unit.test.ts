import { GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { QueryCommand, QueryCommandOutput } from '@aws-sdk/lib-dynamodb';
import oldNewCarOrderJson from '../../../../test/data/old_new_car_order.json';
import {
  generateApiGatewayEvent,
  mockContext,
  quotaApiSecret,
  quotaMonthQuotasRecord,
  setupMocks,
} from '../../../utils/test-utils';
import quotaResponseJson from './unittest-files/quota_response.json';

const mocks = setupMocks({
  mockGenerateNewCarOrderId: 'COPYOD01',
  mockGetExistingNco: { returnNco: oldNewCarOrderJson as NewCarOrderModel },
  mockQuotaApi: quotaResponseJson,
});

import { ScanCommand } from '@aws-sdk/client-dynamodb';
import { SendMessageBatchCommand, SendMessageBatchCommandOutput } from '@aws-sdk/client-sqs';
import { handler } from '.';
import { NewCarOrderModel } from '../../../../lib/entities/new-car-order-model';
beforeEach(() => {
  mocks.ddbMock!.reset();
  mocks.smMock!.reset();
  jest.resetModules();
  process.env.STAGE = 'dev';
});

const coraOrgRels: QueryCommandOutput = {
  Items: [
    {
      pk_ppn_id: 'COTest',
      importer_number: '9690000',
      dealer_number: '1140020',
      is_deactivated: false,
      display_name: '',
      ppn_status: 'OPERATIVE',
      is_relevant_for_order_create: true,
      parent_ppn_id: 'TEST0001',
    },
  ],
  $metadata: {},
};

const dealers: QueryCommandOutput = {
  Items: [
    {
      pk_importer_number: '9690000',
      sk_dealer_number: '1140020',
      standard_port_code: 'S_01',
      alternative_port_codes: ['A_01', 'A_02'],
      display_name: 'Dealer 1',
      modified_at: '2024-12-09T12:13:06.880Z',
      modified_by: 'unit_test',
    },
  ],
  $metadata: {},
};

const sqsResult: SendMessageBatchCommandOutput = {
  Failed: [],
  Successful: [{ Id: 'MockId', MessageId: 'Message2', MD5OfMessageBody: 'DummyBody' }],
  $metadata: {},
};

describe('copyNewCarOrderFunc validations', () => {
  beforeEach(() => {
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });
    mocks.sqsMock!.on(SendMessageBatchCommand).resolves(sqsResult);
  });

  it('should return 400 for invalid NCO ID', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: { mock_quota_api: 'true' },
      pathParameters: { ncoId: 'INVALID_ID' },
      body: JSON.stringify({}),
    });

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(400);
    expect(res?.body).toContain('invalid id');
  });

  it('should return 400 if quota is empty', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: { mock_quota_api: 'true' },
      pathParameters: { ncoId: 'DCSUECQN' },
      body: JSON.stringify({}),
    });

    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS }).resolves(coraOrgRels);
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(400);
    expect(res?.body).toContain('Error in provided quotas');
  });

  it('should return 400 if quota month is more than 6', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: { mock_quota_api: 'true' },
      pathParameters: { ncoId: 'DCSUECQN' },
      body: JSON.stringify(quotaMonthQuotasRecord(8)),
    });

    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS }).resolves(coraOrgRels);
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(400);
    expect(res?.body).toContain('Error in provided quotas');
  });

  it('should return 400 if more than 100 orders are needed', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: { mock_quota_api: 'true' },
      pathParameters: { ncoId: 'DCSUECQN' },
      body: JSON.stringify({ '2025-01': 50, '2025-02': 51 }),
    });

    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS }).resolves(coraOrgRels);
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(400);
    expect(res?.body).toContain('Cannot create more than 100 orders in a single request');
  });

  it('should return 400 if the quota month format not correct', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: { mock_quota_api: 'true' },
      pathParameters: { ncoId: 'DCSUECQN' },
      body: JSON.stringify({ '2025-1': 50 }),
    });

    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS }).resolves(coraOrgRels);
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(400);
    expect(res?.body).toContain('Invalid quota month');
  });
});

describe('copyNewCarOrder process', () => {
  beforeEach(() => {
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });
    // mocks.ddbMock!.on(GetCommand, { TableName: process.env.TABLE_NAME_OT }).resolves(orderTypeResult);
    // mocks.ddbMock!.on(GetCommand, { TableName: process.env.STATUS_MAPPING_TABLE_NAME }).resolves(statusMappingResult);
  });

  it('not authorized dealer and should return 403', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: {
        mock_quota_api: 'true',
        newSelectedDealer: 'DLR1',
      },
      pathParameters: { ncoId: 'DCSUECQN' },
      body: JSON.stringify(quotaMonthQuotasRecord(5)),
    });
    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS }).resolves(coraOrgRels);
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });
    mocks.ddbMock!.on(ScanCommand, { TableName: process.env.TABLE_NAME_DLR }).resolves(dealers);

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(403);
    expect(res?.body).toContain('Newly selected dealer could not be found in allowed dealers of the user');
  });

  it('new selected Dealer and should return 200', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: {
        mock_quota_api: 'true',
        newSelectedDealer: '1140020',
      },
      pathParameters: { ncoId: 'DCSUECQN' },
      body: JSON.stringify(quotaMonthQuotasRecord(1)),
    });
    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS }).resolves(coraOrgRels);
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });
    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_DLR }).resolves(dealers);

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(200);
    expect(res?.body).toContain('"quota_failed":0');
  });

  it('should return 200', async () => {
    const event = generateApiGatewayEvent({
      queryStringParameters: {
        mock_quota_api: 'true',
      },
      pathParameters: { ncoId: 'DCSUECQN' },
      body: JSON.stringify(quotaMonthQuotasRecord(1)),
    });
    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_ORG_RELS }).resolves(coraOrgRels);
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });
    mocks.ddbMock!.on(QueryCommand, { TableName: process.env.TABLE_NAME_DLR }).resolves(dealers);

    const res = await handler(event, mockContext, () => {});

    expect(res?.statusCode).toBe(200);
    expect(res?.body).toContain('"quota_failed":0');
  });
});
