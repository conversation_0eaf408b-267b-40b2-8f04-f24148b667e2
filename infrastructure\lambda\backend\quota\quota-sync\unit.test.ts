import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { handler } from '.';
import { mockContext } from '../../../utils/test-utils';
import 'aws-sdk-client-mock-jest';
import jsonInputTest1 from './quota-test-input-1.json';

import { MSKEvent } from 'aws-lambda';
import { BatchWriteCommand } from '@aws-sdk/lib-dynamodb';
import { CoraQuota } from '../../../../lib/types/quota-api-types';

const ddbMock = mockClient(DynamoDBClient);

beforeEach(() => {
  ddbMock.reset();
  jest.resetModules();
});

describe('When called with quotas should write them to db', () => {
  it('write 2 elements with 6stelligem Modeltype to db', async () => {
    const res = await handler(input, mockContext, () => {});
    console.log('res', res);

    const ddbWriteCall = ddbMock.commandCalls(BatchWriteCommand)[0].lastArg as BatchWriteCommand;
    const writeCallInput = ddbWriteCall.input.RequestItems?.[process.env.QUOTA_TABLE_NAME!] as {
      PutRequest: { Item: CoraQuota };
    }[];
    const _input = jsonInputTest1.Message;
    console.log('kafka input', _input);
    console.log(writeCallInput);
    expect(writeCallInput.length).toBe(2);
    expect(writeCallInput[0].PutRequest.Item.quota_id).toBe(_input.quota_id);
    expect(writeCallInput[1].PutRequest.Item.quota_id).toBe(_input.quota_id);
    expect(writeCallInput[0].PutRequest.Item.quota_open).toBe(_input.quota_open);
    expect(writeCallInput[1].PutRequest.Item.quota_open).toBe(_input.quota_open);
    expect(
      writeCallInput.find(
        (q) =>
          q.PutRequest.Item.quota_id_without_month ===
          `QA-${_input.importer_number}-${_input.dealer_number}-982310-${_input.model_year}`,
      ),
    ).not.toBe(undefined);
    expect(
      writeCallInput.find(
        (q) =>
          q.PutRequest.Item.quota_id_without_month ===
          `QA-${_input.importer_number}-${_input.dealer_number}-982311-${_input.model_year}`,
      ),
    ).not.toBe(undefined);
  });
});

const input: MSKEvent = {
  bootstrapServers: '',
  eventSource: 'aws:kafka',
  eventSourceArn: '',
  records: {
    [process.env.QUOTA_KAFKA_TOPIC!]: [
      {
        headers: [],
        key: '',
        offset: 0,
        partition: 1,
        timestamp: 0,
        timestampType: 'CREATE_TIME',
        topic: '',
        value: Buffer.from(JSON.stringify(jsonInputTest1.Message)).toString('base64'),
      },
    ],
  },
};
