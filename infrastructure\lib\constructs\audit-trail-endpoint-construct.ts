import { Construct } from 'constructs';
import { LambdaIntegration, IAuthorizer, IResource } from 'aws-cdk-lib/aws-apigateway';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { KasNodejsFunction, KasKmsKey, KasStage } from '@kas-resources/constructs';
import { Constants } from '../utils/constants';
import { LambdaTypeOrmBundlingExternalModules, ConstantsCdk } from '../utils/constants_cdk';
import { Duration, RemovalPolicy, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';

export interface AuditTrailEndpointConstructProps {
  authorizer: IAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  auroraReaderSecret: ISecret;
  parentResource: IResource;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  typeormLayer: ILayerVersion;
}

export class AuditTrailEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: AuditTrailEndpointConstructProps) {
    super(scope, id);

    const auditTrailResource = props.parentResource.addResource('audit');
    const auditTrailNcoId = auditTrailResource.addResource('{ncoId}');

    // GET /audit-trail/{NCOId}
    const getAuditTrailForNco = new KasNodejsFunction(this, 'GetAuditTrailForNco', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/new-car-order/audit/get-audit-trail/index.ts',
      handler: 'handler',
      environment: {
        CORS_DOMAIN: props.corsDomain,
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
      },
      description: 'Get the audit trail for one specific new car order',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-get-audit-trail`,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      timeout: Duration.seconds(60),
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [props.typeormLayer],
      stage: props.stage,
    });

    props.auroraReaderSecret.grantRead(getAuditTrailForNco);

    const getAuditTrailLambdaIntegration = new LambdaIntegration(getAuditTrailForNco);
    auditTrailNcoId.addMethod('GET', getAuditTrailLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.ncoId': true,
      },
    });
  }
}
