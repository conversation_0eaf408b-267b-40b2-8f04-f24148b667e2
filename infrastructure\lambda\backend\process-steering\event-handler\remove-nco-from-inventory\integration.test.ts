/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DataSource, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
} from '../../../../../lib/entities/new-car-order-model';
import { CoraMdDealer, CoraMdImporter } from '../../../../../lib/types/masterdata-types';
import {
  DefaultEventHandlerResult,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  buildLambdaArn,
  initDataSourceForIntTest,
  prepareDynamodb,
  cleanupDynamodb,
  createStandartOrgRelPattern,
  createInboundEvent,
  createSqsEvent,
  invokeGenericLambda,
  pollNotifications,
} from '../../../../utils/integration-test-helpers';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.REMOVE_FROM_INVENTORY}`);
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('remove-from-inventory');

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const scPks = Object.values(scs).map((sc) => ({
  pk_shipping_code: sc.pk_shipping_code,
}));

const otPks = Object.values(ots).map((ot) => ({
  pk_order_type: ot.pk_order_type,
}));

const mdDealer: CoraMdDealer = ids.dealer01.md_org;
const mdImporter: CoraMdImporter = ids.importer.md_org;

//auth context apps test data
const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

//existing nco test objects
const ncoDlr1: NewCarOrderModel = {
  pk_new_car_order_id: 'ITREMOVE1',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otNoCustomerRel.pk_order_type,
  quota_month: '202311',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: scs.scBlacklistAllow.pk_shipping_code,
  receiving_port_code: 'ItRemoveNcoPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'IntegrationTestOrderStatus',
  order_status_onevms_error_code: 'IntegrationTestErrorStatus',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: 'IntegrationTestInvoiceStatus',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const defaultSqsEvent = {
  event_type: OneVmsEventKey.REMOVE_FROM_INVENTORY,
  user_auth_context: {
    organizationId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  nco_id: ncoDlr1.pk_new_car_order_id,
  modified_at: ncoDlr1.modified_at!,
  source_system: OneVmsSourceSystemKey.CORA_USER,
  payload: {},
};

let dataSource: DataSource;
let repository: Repository<NewCarOrderModel>;
let outboundEventMapping: OutboundProcessMappingModel | null;

describe('Remove nco from dealer inventory Event Handler Integration Test', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
      OutboundProcessMappingModel,
    ]);
    repository = dataSource.getRepository(NewCarOrderModel);

    const firstOrderRes = await repository.save(ncoDlr1);
    ncoDlr1.modified_at = firstOrderRes.modified_at!;
    defaultSqsEvent.modified_at = firstOrderRes.modified_at!;

    await prepareDynamodb([
      { tableName: orgRelTableName, objs: orgRels },
      {
        tableName: mdDlrTableName,
        objs: [ids.dealer01.md_org],
      },
      { tableName: mdImpTableName, objs: [ids.importer.md_org] },
      { tableName: mdScTableName, objs: Object.values(scs) },
      { tableName: mdOtTableName, objs: Object.values(ots) },
    ]);

    outboundEventMapping = await dataSource.getRepository(OutboundProcessMappingModel).findOneBy({
      source_event_handler: OneVmsEventHandlerKey.REMOVE_FROM_INVENTORY,
      event_result: DefaultEventHandlerResult.SUCCESS,
    });
  });

  afterAll(async () => {
    await repository.delete({
      pk_new_car_order_id: ncoDlr1.pk_new_car_order_id,
    });
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
      pk_new_car_order_id: ncoDlr1.pk_new_car_order_id,
    });
    await dataSource.destroy();

    await cleanupDynamodb([
      {
        tableName: orgRelTableName,
        pks: orgRelPks,
      },
      {
        tableName: mdDlrTableName,
        pks: [
          {
            pk_importer_number: mdDealer.pk_importer_number,
            sk_dealer_number: mdDealer.sk_dealer_number,
          },
        ],
      },
      {
        tableName: mdImpTableName,
        pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
      },
      {
        tableName: mdScTableName,
        pks: scPks,
      },
      {
        tableName: mdOtTableName,
        pks: otPks,
      },
    ]);
  });

  afterEach(async () => {
    //reset orders after every test
    await repository.save(ncoDlr1);
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
      pk_new_car_order_id: ncoDlr1.pk_new_car_order_id,
    });
  });

  it('Should NOT remove from inventory and return fail if input is missing required props', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, nco_id: undefined }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('SQS record is not valid');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoDlr1.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_invoice_onevms_code).not.toEqual(outboundEventMapping?.invoice_status_code);
  });

  it('Should NOT remove from inventory with faulty auth context', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        user_auth_context: {
          ...eventPayload.user_auth_context,
          kasApplications: appsWithNoRole,
        },
      },
    ]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Failed to get the visibility level');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoDlr1.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_invoice_onevms_code).not.toEqual(outboundEventMapping?.invoice_status_code);
  });

  it('Should NOT remove from inventory and return fail if nco cannot be found', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        nco_id: 'NOT_VALID_NCOID',
      },
    ]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Failed to find order with id');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoDlr1.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_invoice_onevms_code).not.toEqual(outboundEventMapping?.invoice_status_code);
  });

  it('Should NOT remove from inventory and return fail if modified_at does not match', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Nco was changed by someone else');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoDlr1.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_invoice_onevms_code).not.toEqual(outboundEventMapping?.invoice_status_code);
  });

  it('Should remove from inventory and return no fails ', async () => {
    defaultSqsEvent.modified_at = ncoDlr1.modified_at!;
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([eventPayload]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoDlr1.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_invoice_onevms_code).toEqual(outboundEventMapping?.invoice_status_code);
  });
});
