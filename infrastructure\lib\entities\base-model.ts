import { Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export class BaseModel {
  /**
   * @format date-time
   */
  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP(6)' })
  public created_at?: string;

  @Column({ type: 'text', update: false })
  public created_by: string;

  /**
   * @format date-time
   */
  @UpdateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  public modified_at?: string;

  @Column({ type: 'text' })
  public modified_by: string;
}
