import { Con<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SQSBatchResponse } from 'aws-lambda';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { EventHandlerContext } from '../event-handler-context';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs';
import {
  InboundEventHandlerEventPVMSUpdateNco,
  SQSBatchResponseWithError,
  NotificationStatus,
  OneVmsSourceSystemKey,
  OneVmsEventKey,
  DefaultEventHandlerResult,
  SpecialStatusCode,
} from '../../../../../lib/types/process-steering-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import {
  combineStatusMappingWithOutboundMapping,
  getStatusUpdateStatementsFromOutboundMapping,
  saveNcosWithAuditTrail,
  StatusUpdateStatement,
} from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { NewCarOrderModelWithoutCoraConfig } from '../../../../../lib/types/new-car-order-types';
import { PvmsOrderDataDTO, VehicleConfigurationPvmsNext } from '../../../../../lib/types/pvms-types';
import { getStatusObjFromPvmsStatus, pvmsToCoraQuota, pvmsToIsoDate } from '../../../../utils/utils';
import { DataSource, Repository } from 'typeorm';
import {
  MappingPvmsToOneVmsStatus,
  MappingPvmsToOneVmsInvoiceStatus,
} from '../../../../../data/status-mapping-pvms/types';
import statusMappingJson from '../../../../../data/status-mapping-pvms/status_mapping_data.json';

EventHandlerContext.init(OneVmsEventHandlerKey.PVMS_UPDATE_NCO, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventPVMSUpdateNco>(
  'InboundEventHandlerEventPVMSUpdateNco',
);

type PvmsWithMessageId = InboundEventHandlerEventPVMSUpdateNco & { messageId: string };
type PvmsWithError = PvmsWithMessageId & { errorMessage: string };

const pvmsUpdateNcoFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse> => {
  const sqsBatchResponse: SQSBatchResponseWithError = { batchItemFailures: [] };
  const successfulEvents: PvmsWithMessageId[] = [];
  const expectedFailedEvents: PvmsWithError[] = [];
  const unexpectedFailedEvents: PvmsWithError[] = [];
  const unParseableEvents: Partial<PvmsWithError>[] = [];

  const parsedEvents = event.Records.map((record) => {
    try {
      const parsed = JSON.parse(record.body) as InboundEventHandlerEventPVMSUpdateNco;
      const [body_validated, validation_errors] = sqsEventValidator.validate(parsed);
      if (body_validated === null) {
        throw new Error('Invalid event', { cause: validation_errors });
      }
      return { ...parsed, messageId: record.messageId };
    } catch (e) {
      const errorMessage = 'Failed to parse/validate event';
      logger.log(LogLevel.WARN, errorMessage, { data: (e as Error).cause });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventPVMSUpdateNco>),
        messageId: record.messageId,
        errorMessage,
      });
      return null;
    }
  }).filter(Boolean) as PvmsWithMessageId[];

  let ncoDataSource: DataSource;
  let ncoRepo: Repository<NewCarOrderModel>;

  try {
    ncoDataSource = await EventHandlerContext.getDataSource();
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      parsedEvents.map((parsedEvent) =>
        eventToNotification(parsedEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: parsedEvents.map((parsedEvent) => ({
        itemIdentifier: parsedEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  // Get outbound mapping config
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundSuccessMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === DefaultEventHandlerResult.SUCCESS,
    );

    if (!outboundSuccessMapping) {
      throw new Error(
        `No outbound mapping found for successful result of event handler ${EventHandlerContext.eventHandlerKey}.`,
      );
    }
    outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundSuccessMapping);
  } catch (e) {
    const message = 'Failed to get outbound event mapping config';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      parsedEvents.map((parsedEvent) =>
        eventToNotification(parsedEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );

    return {
      batchItemFailures: parsedEvents.map((parsedEvent) => ({
        itemIdentifier: parsedEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  for (const parsedEvent of parsedEvents) {
    try {
      const ncoId = parsedEvent.nco_id;
      logger.setCorrelationId(parsedEvent.transaction_id);
      logger.setObjectId(ncoId);

      const pvmsDto = parsedEvent.payload.value;
      const existingNco = await ncoRepo.findOneBy({ pk_new_car_order_id: ncoId });

      if (!existingNco) {
        expectedFailedEvents.push({ ...parsedEvent, errorMessage: 'Referenced NCO not found' });
        continue;
      }

      const validationResult = await EventHandlerContext.commonBusinessLogicValidation(parsedEvent, existingNco);
      if (!validationResult.valid) {
        expectedFailedEvents.push({ ...parsedEvent, errorMessage: validationResult.message! });
        continue;
      }

      const statusUpdateStatement = getStatusUpdateStatementFromPvmsStatus(
        parsedEvent,
        outboundStatusUpdateStatement,
        logger,
      );
      if (!statusUpdateStatement) {
        expectedFailedEvents.push({
          ...parsedEvent,
          errorMessage: 'No mapping for provided PVMS status/lock reason could be found',
        });
        continue;
      }

      //check if status actually changed, to update status update timestamp
      let oneVMSStatusTimestamp = existingNco.order_status_onevms_timestamp_last_change;
      if (
        (statusUpdateStatement.order_status_onevms_code &&
          existingNco.order_status_onevms_code !== statusUpdateStatement.order_status_onevms_code) ??
        (statusUpdateStatement.order_status_onevms_error_code &&
          existingNco.order_status_onevms_error_code !== statusUpdateStatement.order_status_onevms_error_code)
      ) {
        oneVMSStatusTimestamp = new Date().toISOString();
      }

      const newNco = pvmsOrderDataToCoraNcoDatabaseObject(pvmsDto, statusUpdateStatement, oneVMSStatusTimestamp);

      await saveNcosWithAuditTrail(
        ncoDataSource,
        [existingNco.pk_new_car_order_id],
        NcoExportActionType.UPDATE,
        async (transactionManager) => {
          const entity = await transactionManager.preload(NewCarOrderModel, {
            ...existingNco,
            ...newNco,
            modified_by: OneVmsSourceSystemKey.PVMS,
            created_at: existingNco.created_at,
            configuration_expire: existingNco.configuration_expire,
            configuration: existingNco.configuration,
          });
          if (!entity) {
            throw new Error(`Entity with ID ${existingNco.pk_new_car_order_id} not found for update`);
          }
          return [await transactionManager.save(entity)];
        },
        logger,
        false,
      );
      logger.log(
        LogLevel.INFO,
        `PVMS update request for NCO with NCO ID ${existingNco.pk_new_car_order_id} was successfully fulfilled.`,
      );
      successfulEvents.push(parsedEvent);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...parsedEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during update';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...parsedEvent, errorMessage: message });
      continue;
    }
  }

  const notificationEvents = [
    ...successfulEvents.map((e) => eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO)),
    ...expectedFailedEvents.map((e) => eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage)),
    ...unParseableEvents.map((e) =>
      unparseableEventToNotification(
        e,
        OneVmsEventKey.PVMS_UPDATE_NCO,
        NotificationStatus.EVENT_HANDLER_NIO,
        e.errorMessage,
      ),
    ),
    ...unexpectedFailedEvents.map((e) => eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage)),
  ];

  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications', { data: e });
  }

  return sqsBatchResponse;
};

const pvmsOrderDataToCoraNcoDatabaseObject = (
  pvmsOrderData: PvmsOrderDataDTO,
  statusUpdateStatement: StatusUpdateStatement,
  oneVMSStatusTimestamp: string,
  pvmsConfig?: VehicleConfigurationPvmsNext,
): Partial<NewCarOrderModelWithoutCoraConfig> => {
  //strip leading 0s from dealer_number and importer_number
  const dealerNumber = pvmsOrderData.order_info.trading_partner.dealer_sold_to_number.replace(/^0+/, '');
  const importerNumber = pvmsOrderData.order_info.trading_partner.importer_number.replace(/^0+/, '');
  const coraQuota = pvmsToCoraQuota(pvmsOrderData.order_info.base_info.quota_month);

  return {
    pk_new_car_order_id: pvmsOrderData.ids.new_car_order_id,
    importer_number: importerNumber,
    importer_code: pvmsOrderData.order_info.trading_partner.importer_code,
    dealer_number: dealerNumber,
    model_type: pvmsOrderData.model_info.model_type,
    model_year: pvmsOrderData.model_info.model_year.toString(),
    cnr: pvmsOrderData.model_info.country_code,
    quota_month: coraQuota,
    order_type: pvmsOrderData.order_info.base_info.order_type,
    shipping_code: pvmsOrderData.logistics_info.shipping_code,
    receiving_port_code: pvmsOrderData.logistics_info.receiving_port_code,
    requested_dealer_delivery_date:
      pvmsToIsoDate(pvmsOrderData.appointment_date_info?.production_logistic_dates?.requested_dealer_delivery_date) ??
      undefined,
    created_at:
      pvmsOrderData.appointment_date_info?.production_logistic_dates?.order_creation_date ?? new Date().toISOString(),
    created_by: 'PVMS',
    modified_by: 'PVMS',
    order_status_onevms_timestamp_last_change: oneVMSStatusTimestamp,
    business_partner_id: pvmsOrderData.ids.business_partner_id,
    configuration_expire: pvmsConfig,
    changed_by_system: OneVmsSourceSystemKey.PVMS,
    ...statusUpdateStatement,
  };
};

const getStatusUpdateStatementFromPvmsStatus = (
  pvmsEvent: InboundEventHandlerEventPVMSUpdateNco,
  outboundStatusUpdateStatement: StatusUpdateStatement,
  logger: KasLambdaLogger,
): StatusUpdateStatement | undefined => {
  const statusMapping = statusMappingJson as {
    mappings_to_one_vms_status: MappingPvmsToOneVmsStatus[];
    mappings_to_invoice_status: MappingPvmsToOneVmsInvoiceStatus[];
  };
  const pvmsVehicleStatus = pvmsEvent.payload.value.order_info.status_info.vehicle_status_pvms_code;
  const pvmsLockReason = pvmsEvent.payload.value.order_info.planning?.blocking_reason_planning ?? null;
  const pvmsInvoiceStatus = pvmsEvent.payload.value.order_info.status_info.order_status_pvms_code;

  const foundMapping = getStatusObjFromPvmsStatus(
    {
      pvmsStatus: pvmsVehicleStatus,
      statusMappingData: statusMapping.mappings_to_one_vms_status,
      pvmsLockStatus: pvmsLockReason,
    },
    logger,
  );

  const invoiceMatch = statusMapping.mappings_to_invoice_status.find(
    (entry) => entry.pvms_invoice_status === pvmsInvoiceStatus,
  );

  return combineStatusMappingWithOutboundMapping(
    outboundStatusUpdateStatement,
    foundMapping
      ? {
          order_status_onevms_code: foundMapping.one_vms_status,
          order_status_onevms_error_code: foundMapping.one_vms_error_status ?? SpecialStatusCode.NONE,
          order_invoice_onevms_code: invoiceMatch?.one_vms_invoice_status,
        }
      : undefined,
    pvmsEvent.payload.mapInvoice,
  );
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, pvmsUpdateNcoFunc);
