import { <PERSON>umn, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { Constants } from '../utils/constants';

@Entity({ name: `${Constants.AURORA_FAILED_STATUS_MAPPING_ORDERS_TABLE}` })
export class FailedStatusMappingOrdersModel {
  @PrimaryColumn({ type: 'text' })
  public key: string;

  @Column({ type: 'int' })
  public num_of_tries: number;

  //Unix Epoch time
  @Column({ type: 'timestamp with time zone' })
  public timestamp: string;

  @Column({ type: 'text' })
  public vehicle_status_pvms_code: string;

  @Column({ type: 'text' })
  public order_status_pvms_code: string;

  @Column({ type: 'text', nullable: true })
  public lock_status_pvms_code?: string | null;

  @Column({ type: 'jsonb' })
  public value: unknown;

  /**
   * @format date-time
   */
  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP(6)' })
  public created_at?: string;

  /**
   * @format date-time
   */
  @UpdateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  public modified_at?: string;
}
