import { Kas<PERSON><PERSON><PERSON>daLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import {
  DefaultEventHandlerResult,
  InboundEventHandlerEventCancelNco,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import {
  getStatusUpdateStatementsFromOutboundMapping,
  saveNcosWithAuditTrail,
  StatusUpdateStatement,
} from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { EventHandlerContext } from '../event-handler-context';

type CancelNcoWithMessageId = InboundEventHandlerEventCancelNco & { messageId: string };
type CancelNcoWithError = CancelNcoWithMessageId & { errorMessage: string };

EventHandlerContext.init(OneVmsEventHandlerKey.CANCEL_NCO, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventCancelNco>('InboundEventHandlerEventCancelNco');

const cancelNewCarOrdersFunc = async (
  event: SQSEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  const unParseableEvents: Partial<CancelNcoWithError>[] = [];
  const expectedFailedEvents: CancelNcoWithError[] = [];
  const unexpectedFailedEvents: CancelNcoWithError[] = [];
  const successfulEvents: CancelNcoWithMessageId[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  const cancelNcoEvents = event.Records.map((record) => {
    try {
      // Parse body from event object
      const cancelNcoEvent = JSON.parse(record.body) as InboundEventHandlerEventCancelNco | undefined;
      const [body_validated, validation_errors] = sqsEventValidator.validate(cancelNcoEvent);
      if (body_validated === null) {
        const message = 'SQS record is not valid, ajv validation failed';
        logger.log(LogLevel.WARN, message, { data: validation_errors });
        unParseableEvents.push({
          ...cancelNcoEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }

      //check cancellation_reason for validity
      if (!Constants.CANCELLATION_REASONS.map((sg) => sg.id).includes(body_validated.payload.cancellation_reason)) {
        const message = 'Unknown cancellation reason';
        logger.log(LogLevel.WARN, message, { data: cancelNcoEvent });
        expectedFailedEvents.push({
          ...body_validated,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }
      return { ...cancelNcoEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventCancelNco>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as CancelNcoWithMessageId[];

  //Get outbound event mapping config and status update statements
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === DefaultEventHandlerResult.SUCCESS,
    );
    // If there is no mapping, the action is aborted because Cancel NCO needs a status change.
    if (!outboundEventMapping) {
      throw new Error(
        `No outbound mapping found for successful result of event handler ${EventHandlerContext.eventHandlerKey}.`,
      );
    }
    outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
  } catch (e) {
    const message = `Failed to get outbound event mapping config`;
    logger.log(LogLevel.ERROR, message, {
      data: e,
    });
    await EventHandlerContext.pushNotificationsToKafka(
      cancelNcoEvents.map((cancelEvent) =>
        eventToNotification(cancelEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no outboundMapping, no party.
    return {
      batchItemFailures: cancelNcoEvents.map((cancelEvent) => ({
        itemIdentifier: cancelEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  let ncoDataSource: DataSource;
  let ncoRepo: Repository<NewCarOrderModel>;

  try {
    ncoDataSource = await EventHandlerContext.getDataSource();
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      cancelNcoEvents.map((cancelEvent) =>
        eventToNotification(cancelEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: cancelNcoEvents.map((cancelEvent) => ({
        itemIdentifier: cancelEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  //Process all events one by one
  for (const cancelNcoEvent of cancelNcoEvents) {
    logger.setObjectId(cancelNcoEvent.nco_id);
    logger.setCorrelationId(cancelNcoEvent.transaction_id);
    const userAttributes = cancelNcoEvent.user_auth_context;

    // Extract necessary attributes from auth context
    const visibilityLevel =
      userAttributes.kasApplications[EventHandlerContext.applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
    const ppnId = userAttributes.organizationId;

    if (!visibilityLevel) {
      const message = 'Failed to get the visibility level';
      logger.log(LogLevel.ERROR, message, { data: event });
      expectedFailedEvents.push({ ...cancelNcoEvent, errorMessage: message });
      continue;
    }

    try {
      const nco = await ncoRepo.findOne({
        select: [
          'pk_new_car_order_id',
          'dealer_number',
          'importer_number',
          'order_status_onevms_code',
          'order_status_onevms_error_code',
          'modified_at',
        ],
        where: { pk_new_car_order_id: cancelNcoEvent.nco_id },
      });

      //mark not found order in err result
      if (!nco) {
        const message = 'Failed to find order with id: ' + cancelNcoEvent.nco_id;
        logger.log(LogLevel.FATAL, message, {
          objectId: cancelNcoEvent.nco_id,
        });
        expectedFailedEvents.push({ ...cancelNcoEvent, errorMessage: message });
        continue;
      }

      const validationResult = await EventHandlerContext.commonBusinessLogicValidation(cancelNcoEvent, nco, ppnId);
      if (!validationResult.valid) {
        expectedFailedEvents.push({ ...cancelNcoEvent, errorMessage: validationResult.message! });
        continue;
      }

      await saveNcosWithAuditTrail(
        ncoDataSource,
        [nco.pk_new_car_order_id],
        NcoExportActionType.CANCEL,
        async (transactionManager: EntityManager) => {
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: nco.pk_new_car_order_id },
            {
              cancellation_reason: cancelNcoEvent.payload.cancellation_reason,
              changed_by_system: OneVmsSourceSystemKey.CORA,
              order_status_onevms_timestamp_last_change: new Date().toISOString(),
              modified_by: userAttributes.username,
              ...outboundStatusUpdateStatement,
            },
          );
          return await transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: nco.pk_new_car_order_id,
              cancellation_reason: cancelNcoEvent.payload.cancellation_reason,
            },
          });
        },
        logger,
        false,
      );

      logger.log(LogLevel.INFO, `NewCarOrder with NCO id ${nco.pk_new_car_order_id} was cancelled successfully.`);
      successfulEvents.push(cancelNcoEvent);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...cancelNcoEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during cancellation';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...cancelNcoEvent, errorMessage: message });
      continue;
    }
  }

  //Export results into notification topic
  const successfulNotifications = successfulEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO),
  );

  const expectedFailedNotifications = expectedFailedEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  const unparseableNotifications = unParseableEvents.map((e) =>
    unparseableEventToNotification(e, OneVmsEventKey.CANCEL, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  // Unexpected fail events are not consumed and can be retried
  const unexpectedNotifications = unexpectedFailedEvents.map((e) => {
    sqsBatchResponse.batchItemFailures.push({
      itemIdentifier: e.messageId,
      errorMessage: e.errorMessage,
    });
    return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
  });

  // Combine all notifications
  const notificationEvents: NotificationKafkaEvent[] = [
    ...successfulNotifications,
    ...expectedFailedNotifications,
    ...unparseableNotifications,
    ...unexpectedNotifications,
  ];

  //Push notifications to kafka
  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  //Return fails so that events are put into dlq
  return sqsBatchResponse;
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, cancelNewCarOrdersFunc);
