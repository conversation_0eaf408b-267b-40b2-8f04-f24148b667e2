import { FailedCarOrderInList } from '../types';
import { baseApi } from './BaseApi';

const apiPrefix = 'monitoring';
const monitoringApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getFailedStatusMappingOrders: builder.query<FailedCarOrderInList[], undefined>({
      query: () => ({ url: `${apiPrefix}/fetch-failed-order-data` }),
    }),
  }),
});

export const { useGetFailedStatusMappingOrdersQuery } = monitoringApi;
