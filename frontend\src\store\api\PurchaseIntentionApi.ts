import {
  ConvertPiApiRequest,
  InboundApiEventResponse,
} from '../../../../infrastructure/lib/types/process-steering-types';
import { CoraNCPurchaseIntentionApiResponse } from '../../../../infrastructure/lib/types/purchase-intention-types';
import { baseApi } from './BaseApi';
import { createQuotaStoreId } from './QuotaApi';

const apiPrefix = 'purchase-intention';
const purchaseIntentionApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPurchaseIntentionById: builder.query<CoraNCPurchaseIntentionApiResponse, string>({
      query: (id) => ({ url: `${apiPrefix}/${id}` }),
      providesTags: (result) => [{ type: 'currentPi', id: result?.purchase_intention_id }],
    }),
    convertDealToNewCarOrder: builder.mutation<InboundApiEventResponse, ConvertPiApiRequest>({
      // deal_id because in the new car order object it is still called deal_id
      query: (deal_convert_request_obj) => ({
        url: `${apiPrefix}/${deal_convert_request_obj.deal_id}/convert`,
        method: 'PATCH',
        body: JSON.stringify(deal_convert_request_obj),
      }),
      invalidatesTags: (_result, _error, arg) => [
        { type: 'currentPi', id: arg.deal_id ?? undefined },
        { type: 'piList' },
        { type: 'ncoList' },
        { type: 'quota', id: createQuotaStoreId(arg) },
      ],
    }),
  }),
});

export const { useGetPurchaseIntentionByIdQuery, useConvertDealToNewCarOrderMutation } = purchaseIntentionApi;
