import { Context, SQSEvent } from 'aws-lambda';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { v4 as uuidv4 } from 'uuid';
import { getEnvVarWithAssert } from '../../utils/utils';
import { secretCache } from '../../utils/secret-cache';
import { KafkaAdapter } from '../../utils/kafka';
import { NewCarOrderModel } from '../../../lib/entities/new-car-order-model';
import {
  KafkaObj,
  Ka<PERSON>kaObjsWrapper,
  KafkaObjTyp,
  NcoExportActionType,
  NewCarOrderKafkaObject,
  PVMS_EXPORT_ACTION_TYPES,
} from './types';
import { ObjectValidator } from '../../../lib/utils/object-validation';
import { NcoAuditTrailSqsEvent } from '../../../lib/types/nco-audit-trail-types';
import { createTypeORMDataSource } from '../../config/typeorm-config';
import { NewCarOrderAuditTrailModel } from '../../../lib/entities/new-car-order-audit-trail-model';
import { In, Repository } from 'typeorm';
import { ncoConfigDbToApiObj } from '../../utils/utils-typeorm';
import { OneVmsSourceSystemKey } from '../../../lib/types/process-steering-types';

const KAFKA_SECRET_ARN = getEnvVarWithAssert('KAFKA_SECRET_ARN');
const KAFKA_TOPIC_ONE_VMS = getEnvVarWithAssert('KAFKA_TOPIC_ONE_VMS');
const KAFKA_TOPIC_PVMS = getEnvVarWithAssert('KAFKA_TOPIC_PVMS');
const KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
const STAGE = getEnvVarWithAssert('STAGE');
const logger = new KasLambdaLogger('NewCarOrderExport', LogLevel.TRACE);

secretCache.initCache(KAFKA_SECRET_ARN, AURORA_SECRET_ARN);

const kafkaAdapter = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: KAFKA_SECRET_ARN,
  logger,
});

const ncoAuditTrailSqsEventValidator = new ObjectValidator<NcoAuditTrailSqsEvent>('NcoAuditTrailSqsEvent');

export const handler = async (event: SQSEvent, context: Context): Promise<void> => {
  logger.setRequestContext(context);
  logger.log(LogLevel.DEBUG, `NewCarOrder audit trail sqs event `, { data: event.Records });

  //parse sqs event
  const ncoAuditTrailSqsEvents = event.Records.map((record) => {
    const ncoAuditTrailSqsEvent = JSON.parse(record.body) as NcoAuditTrailSqsEvent | undefined;

    //check if sqs content is valid just in case some crash caused ivalid objs to be put into dlq
    const isValid = validateSqsEvent(ncoAuditTrailSqsEvent);
    if (!isValid) {
      logger.log(LogLevel.ERROR, `SQS record is not valid, skipping`, {
        data: ncoAuditTrailSqsEvent,
        correlationId: ncoAuditTrailSqsEvent?.action_correlation_id,
        objectId: ncoAuditTrailSqsEvent?.pk_new_car_order_id,
      });
      return undefined;
    }

    return ncoAuditTrailSqsEvent;
  }).filter(Boolean) as NcoAuditTrailSqsEvent[];

  let atRepo: Repository<NewCarOrderAuditTrailModel>;
  let auditTrails: NewCarOrderAuditTrailModel[];
  try {
    //create the typeorm datasource and repository obj for nco audit trails
    const ncoDataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, STAGE, [NewCarOrderAuditTrailModel]);
    atRepo = ncoDataSource.getRepository(NewCarOrderAuditTrailModel);

    //get all unexported audit trails for all supplied nco IDs
    auditTrails = await atRepo.findBy({
      pk_new_car_order_id: In(ncoAuditTrailSqsEvents.map((atEvent) => atEvent.pk_new_car_order_id)),
      action_exported: false,
    });
    logger.log(LogLevel.TRACE, `Audit Trail result from db `, { data: auditTrails });
  } catch (error) {
    logger.log(LogLevel.ERROR, `Could not read audit trails from db, letting lambda fail`, { data: error });
    throw error;
  }

  //loop over all found audit trails, convert to nco export and collect for onevms and pvms topics
  const oneVmsKafkaExportObjs: KafkaObj<NewCarOrderKafkaObject>[] = [];
  let pvmsKafkaExportObjs: KafkaObj<NewCarOrderKafkaObject>[] = [];
  auditTrails.forEach((auditTrail) => {
    logger.setCorrelationId(auditTrail.action_correlation_id);
    logger.setObjectId(auditTrail.pk_new_car_order_id);
    logger.log(LogLevel.TRACE, `Processing nco audit trail from db `, { data: auditTrail });

    if (!auditTrail.new_nco && !auditTrail.old_nco) {
      logger.log(LogLevel.ERROR, `broken audit trail, should not happen`, { data: auditTrail });
      return;
    }

    //check if timestamp matches sqs event, warn otherwise but export anyway
    const correspondingSqsEvent = ncoAuditTrailSqsEvents.find(
      (atSqsEvent) =>
        atSqsEvent.pk_new_car_order_id === auditTrail.pk_new_car_order_id &&
        atSqsEvent.action_at === new Date(auditTrail.action_at).toISOString(),
    );
    if (!correspondingSqsEvent) {
      logger.log(
        LogLevel.WARN,
        `Found unexported audit trail that does not belong to sqs event, this should not have happened. Exporting it anyway. `,
        { data: auditTrail },
      );
    }

    const kafkaObject = {
      id: auditTrail.pk_new_car_order_id,
      kafkaActionTyp: auditTrail.action_type,
      correlationId: auditTrail.action_correlation_id,
      importer: auditTrail.new_nco?.importer_number ?? auditTrail.old_nco?.importer_number,
      obj: flatToKafkaExportNewCarOrderObj(auditTrail.new_nco ?? auditTrail.old_nco!),
    };

    oneVmsKafkaExportObjs.push(kafkaObject);
    if (auditTrail.new_nco?.changed_by_system === OneVmsSourceSystemKey.PVMS) {
      //skip pvms export to break the event loop
      logger.log(LogLevel.INFO, `Skipping PVMS Kafka export because of changed_by_system for order: `, {
        data: auditTrail,
      });
    } else {
      // Copy the Object, so delete configuration_expire does not delete from pvms obj
      pvmsKafkaExportObjs.push(JSON.parse(JSON.stringify(kafkaObject)) as KafkaObj<NewCarOrderKafkaObject>);
    }
  });

  // Filter pvms exports to certain action types and remove null value quota month changes
  pvmsKafkaExportObjs = pvmsKafkaExportObjs.filter(
    (obj) => PVMS_EXPORT_ACTION_TYPES.includes(obj.kafkaActionTyp!) && obj.obj.order_info.base_info.quota_month,
  );

  // Export all items to one vms topic
  const kafkaObjsWrapperOneVms: KafkaObjsWrapper<NewCarOrderKafkaObject> = {
    kafkaObjTyp: KafkaObjTyp.NCO,
    kafkaObjs: oneVmsKafkaExportObjs.map((kafkaExportObj) => {
      const _kafkaItemOneVms = { ...kafkaExportObj };
      delete _kafkaItemOneVms.obj.configuration_expire;
      return _kafkaItemOneVms;
    }),
    kafkaActionTyp: NcoExportActionType.UPDATE, //will be overwritten by individual item action type
  };

  try {
    logger.log(LogLevel.INFO, `Pushing to OneVms Kafka (${KAFKA_TOPIC_ONE_VMS})`, { data: kafkaObjsWrapperOneVms });
    await kafkaAdapter.pushObjsToTopic({
      kWrapper: kafkaObjsWrapperOneVms,
      topic: KAFKA_TOPIC_ONE_VMS,
      correlationid: uuidv4(),
    });
  } catch (error) {
    logger.log(LogLevel.ERROR, `Could not push audit trails to kafka, letting lambda fail`, { data: error });
    throw error;
  }

  // Export all items to pvms topic
  const kafkaObjsWrapperPvms: KafkaObjsWrapper<NewCarOrderKafkaObject> = {
    kafkaObjTyp: KafkaObjTyp.NCO,
    kafkaObjs: pvmsKafkaExportObjs.map((kafkaExportObj) => {
      const kafkaObj = { ...kafkaExportObj.obj };

      // KASHEARTBE-1219 PVMS needs last modification timestamp to be set always
      kafkaObj.order_info.base_info.last_modified_by =
        kafkaObj.order_info.base_info.last_modified_by ?? kafkaObj.order_info.base_info.created_by;
      kafkaObj.appointment_date_info.production_logistic_dates.order_last_modification_timestamp =
        kafkaObj.appointment_date_info.production_logistic_dates.order_last_modification_timestamp ??
        kafkaObj.appointment_date_info.production_logistic_dates.order_creation_timestamp;

      return {
        ...kafkaExportObj,
        //special case for pvms, as they cannot handle the new refactored action types
        kafkaActionTyp:
          kafkaExportObj.kafkaActionTyp === NcoExportActionType.CONVERT
            ? ('converted' as NcoExportActionType)
            : kafkaExportObj.kafkaActionTyp,
        obj: kafkaObj,
      };
    }),
    kafkaActionTyp: NcoExportActionType.UPDATE, //will be overwritten by individual item action type
  };

  try {
    logger.log(LogLevel.INFO, `Pushing to Pvms Kafka (${KAFKA_TOPIC_PVMS})`, { data: kafkaObjsWrapperPvms });
    await kafkaAdapter.pushObjsToTopic({
      kWrapper: kafkaObjsWrapperPvms,
      topic: KAFKA_TOPIC_PVMS,
      correlationid: uuidv4(),
    });
  } catch (error) {
    logger.log(LogLevel.ERROR, `Could not push audit trails to kafka, letting lambda fail`, { data: error });
    throw error;
  }

  logger.log(LogLevel.DEBUG, `Successfully pushed all ncos to onevms and pvms topics, updating audit trails next`, {
    data: kafkaObjsWrapperOneVms,
  });

  try {
    await atRepo.update(
      { pk_new_car_order_id: In(auditTrails.map((at) => at.pk_new_car_order_id)) },
      {
        action_exported: true,
      },
    );
  } catch (error) {
    logger.log(
      LogLevel.ERROR,
      `Could not mark audit trails as exported in db. This should not happen and will result in double exports!`,
      { data: error },
    );
  }
};

function flatToKafkaExportNewCarOrderObj(obj: NewCarOrderModel): NewCarOrderKafkaObject {
  const kafkaObj: NewCarOrderKafkaObject = {
    ids: {
      new_car_order_id: obj.pk_new_car_order_id,
      business_partner_id: obj.business_partner_id ?? undefined,
    },
    logistics_info: {
      receiving_port_code: obj.receiving_port_code ?? undefined,
      shipping_code: obj.shipping_code,
    },
    model_info: {
      model_type: obj.model_type,
      model_year: Number(obj.model_year),
      country_code: obj.cnr,
    },
    order_info: {
      base_info: {
        order_type: obj.order_type,
        quota_month: obj.quota_month,
        created_by: obj.created_by,
        last_modified_by: obj.modified_by,
        cancellation_reason: obj.cancellation_reason ?? undefined,
      },
      trading_partner: {
        importer_code: obj.importer_code,
        dealer_sold_to_number: obj.dealer_number,
        dealer_ship_to_number: obj.dealer_number,
        importer_number: obj.importer_number,
      },
    },
    status_info: {
      order_status_code: obj.order_status_onevms_code,
      order_status_timestamp: obj.order_status_onevms_timestamp_last_change,
      order_error_status_code: obj.order_status_onevms_error_code,
      order_error_status_timestamp: obj.order_status_onevms_timestamp_last_change,
    },
    appointment_date_info: {
      production_logistic_dates: {
        order_creation_timestamp: obj.created_at ?? '',
        order_last_modification_timestamp: obj.modified_at,
        requested_dealer_delivery_date: obj.requested_dealer_delivery_date ?? undefined,
      },
    },
    configuration: ncoConfigDbToApiObj(obj.configuration),
    configuration_expire: obj.configuration_expire,
  };
  return kafkaObj;
}

function validateSqsEvent(input: NcoAuditTrailSqsEvent | undefined): boolean {
  const [body_validated, validation_errors] = ncoAuditTrailSqsEventValidator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}
