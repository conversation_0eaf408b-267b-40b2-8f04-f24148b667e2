import { Construct } from 'constructs';
import { RestApi, LambdaIntegration, IAuthorizer, IResource } from 'aws-cdk-lib/aws-apigateway';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { KasNodejsFunction, KasKmsKey, KasStage } from '@kas-resources/constructs';
import { Constants } from '../utils/constants';
import { RemovalPolicy, aws_ec2 as ec2, Duration } from 'aws-cdk-lib';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export interface FetchFailedCarOrdersConstructProps {
  api: RestApi;
  authorizer: IAuthorizer;
  stage: KasStage;
  logGroupKey: KasKmsKey;
  corsDomain: string;
  parentResource: IResource;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  auroraReaderSecret: ISecret;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  layers: ILayerVersion[];
}

export class FetchFailedCarOrdersEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: FetchFailedCarOrdersConstructProps) {
    super(scope, id);
    // GET /fetch-failed-status-order-data
    const fetchFailedCarOrders = new KasNodejsFunction(this, 'GetFailedOrders', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/monitoring/fetch-failed-order-data/index.ts',
      handler: 'handler',
      environment: {
        CORS_DOMAIN: props.corsDomain,
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
      },
      description: 'Get all failed orders',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-failed-status-order-data`,
      vpc: props.vpc,
      timeout: Duration.seconds(30),
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: props.layers,
      stage: props.stage,
    });
    props.auroraReaderSecret.grantRead(fetchFailedCarOrders);
    const failedCarOrdersResource = props.parentResource.addResource('fetch-failed-order-data');
    const fetchFailedCarOrdersIntegration = new LambdaIntegration(fetchFailedCarOrders);
    failedCarOrdersResource.addMethod('GET', fetchFailedCarOrdersIntegration, {
      authorizer: props.authorizer,
    });
  }
}
