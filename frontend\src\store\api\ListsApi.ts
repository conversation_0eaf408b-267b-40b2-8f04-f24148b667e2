import { baseApi } from './BaseApi';
import { PurchaseIntentionInList } from '../types';
import { CoraNCPurchaseIntentionQueryApiRequest } from '../../../../infrastructure/lib/types/purchase-intention-types';

const apiPrefix = 'lists';
const listsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPiList: builder.query<PurchaseIntentionInList[], CoraNCPurchaseIntentionQueryApiRequest | undefined>({
      query: (params) => ({ url: `${apiPrefix}/purchase-intention`, params: params }),
      providesTags: ['piList'],
    }),
  }),
});

export const { useGetPiListQuery, useLazyGetPiListQuery } = listsApi;
