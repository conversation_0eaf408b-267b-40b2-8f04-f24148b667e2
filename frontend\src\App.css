html,
body {
  overflow: hidden;
  height: 100%;
  margin: 0;
}

.app {
  padding: max(1rem, 2vw);
  position: relative;
  padding-top: 2vh;
}

.app-link {
  color: #61dafb;
}

.app-logo {
  margin: 0.5em;
}

.mini-list {
  margin-left: min(-1rem, -7vw);
  margin-right: min(-1rem, -7vw);
  margin-top: -38px;
}

.mini-list .ag-theme-quartz .ag-root,
.mini-list .ag-theme-quartz .ag-body-viewport,
.mini-list .ag-theme-quartz .ag-body-viewport-wrapper {
  overflow-y: hidden !important;
  overflow-x: scroll !important;
}

.tippy-box {
  overflow: visible !important;
}

.row-actions-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-grow: 0;
}

.app header {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
  border-bottom: #e3e4e4 solid 1px;
  margin-bottom: 18px;
}

.nav-user {
  background-color: #f2f2f2;
  display: flex;
  justify-content: flex-end;
  padding: 0px max(1rem, 7vw);
}

.nav-user > div {
  height: 2em;
  float: right;
  margin-top: 0.5em;
}

footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: rgba(14, 20, 24, 0.9);
}

.footer-wrapper {
  display: flex;
  justify-content: flex-end;
  padding: 0px max(1rem, 7vw);
  margin-bottom: 8px;
  margin-top: 8px;
}

.footer-wrapper div {
  float: right;
  margin: 0 10px;
}

.setDefaultPadding {
  padding: 26px 0 18px;
}

.over-everything {
  z-index: 9999999 !important;
}

.cora-headline {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  min-height: 56px;
  padding: 36px 0 18px;
}

.action-wrapper {
  display: flex;
  flex-grow: 0;
}

.centered-foreground-spinner {
  justify-content: center;
  width: 100%;
  height: 100%;
  position: fixed;
  align-items: center;
  left: 0px;
  top: 0px;
}
