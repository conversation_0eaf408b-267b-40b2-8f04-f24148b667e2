import { PSpinner, PText } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { FetchError } from '../errors/FetchErrors';
import { CoraMdDealerImporterApiResponse } from '../../store/types';
import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';

interface DealerImporterProps {
  data?: CoraMdDealerImporterApiResponse;
  isFetching: boolean;
  error?: FetchBaseQueryError | SerializedError;
}
export const DealerImporter: React.FC<DealerImporterProps> = ({ data, error, isFetching }) => {
  const area = 'authorized_dealer' as const;
  const { t } = useTranslation();

  if (isFetching) {
    return <PSpinner />;
  }

  if (error) {
    return <FetchError custom_error={error} error_area={area} />;
  }

  return (
    <>
      <PText>Importer: {JSON.stringify(data?.importer)}</PText>
      <PText>Dealer: {JSON.stringify(data?.dealer)}</PText>
    </>
  );
};
