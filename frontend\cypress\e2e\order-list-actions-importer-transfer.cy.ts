import { IMPORTER_TRANSFER_ERRORS } from '../../../infrastructure/lambda/backend/new-car-order/importer-transfer/types';
import { ModelTypeVisibilityModel } from '../../../infrastructure/lib/entities/model-type-visibility-model';
import { CoraNCOImporterTransferApiResponse } from '../../../infrastructure/lib/types/new-car-order-types';
import { USERNAME_WRITE } from '../support/constants';
import { generateMtvs, generateNewCarOrders } from '../support/order-lists-test-data';
import { retryableBefore } from '../support/retry';

const displayOrdersTab = '[data-e2e="display_orders"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';
const transferBtn = '[data-e2e="importer_transfer"]';
const confirmBtn = '[data-e2e="accept"]';
const closeBtn = '[data-e2e="close"]';
const transferModal = '[data-e2e="importer_transfer_order_modal"]';
const transferOrdersBtn: string = '[data-e2e="importer_transfer"]';

const importerLocator: string = '[data-e2e="SelectImporter"]';
const dealerLocator: string = '[data-e2e="SelectDealer"]';
const orderTypeLocator: string = '[data-e2e="SelectOrderType"]';
const shippingCodeLocator: string = '[data-e2e="SelectShippingCode"]';

const ordersEndpointURL = '**/new-car-order**';

const multiDetailsContainer = '[data-e2e="import_multi_details"]';
const newImporterNr = '9210000';
const newImporter = '9210000 - Porsche Cars Great Britain Ltd.';

const ncoPrefixThisTest = 'IT';
const preproductionOrders = generateNewCarOrders(ncoPrefixThisTest, 'PP0000');
const preproductionOrderMtvs = generateMtvs(preproductionOrders);
const newMtvs: ModelTypeVisibilityModel[] = preproductionOrders.map((order) => ({
  role: 'IMP',
  valid_from: '2024-01-01',
  cnr: order.cnr,
  importer_number: newImporterNr,
  model_type: order.model_type,
  my4: order.model_year,
  created_by: 'E2E_TEST',
  modified_by: 'E2E_TEST',
}));

const mtvs_importer_transfer = [...preproductionOrderMtvs, ...newMtvs];

describe('Order List Actions Importer Transfer', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task('prepareMtvRds', { objs: mtvs_importer_transfer }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: mtvs_importer_transfer });
  });

  it('Submit importer transfer for a single order, success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);

    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    //Importer transfer begins here
    cy.get('[row-index="0"]', { timeout: 10000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(transferBtn, { timeout: 3000 }).should('be.visible').click();

    cy.get(transferModal).should('exist').and('not.be.empty');
    cy.get(transferModal).find('.importer-transfer-details-single-container').should('be.visible');

    // Choose Importer and Dealer (Step 1)
    cy.get(importerLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(importerLocator, newImporter);
    cy.get(dealerLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(dealerLocator, 1);
    cy.get(transferModal).find(confirmBtn).click();

    // Step 2
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, 2);
    cy.get(shippingCodeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(shippingCodeLocator, 1);

    //confirm and check result
    cy.intercept('POST', `${ordersEndpointURL}/importer-transfer`).as('importerTransfer');
    cy.get(transferModal).find(confirmBtn).click();
    cy.wait('@importerTransfer');
    cy.get(transferModal).find('.header-success-container', { timeout: 30000 }).should('be.visible');
    cy.get(transferModal).find('.importer-transfer-details-single-container', { timeout: 30000 }).should('be.visible');

    cy.get('[data-e2e="new_pk_new_car_order_id"]')
      .invoke('text')
      .then(($text) => {
        const ncoId = $text.trim();
        //close the dialog and check if table was updated correctly
        cy.get(transferModal).find(closeBtn).click();
        cy.wait('@fetchOrders');
        cy.get('[row-index="0"]').should('be.visible');
        cy.get('[aria-label="New Car Order ID Filter Input"]')
          .clear()
          .type(ncoId, { delay: 100 })
          .should('have.value', ncoId);

        cy.wait(2000);
        // Get dealer number from selected order and compare with UI
        cy.get('[row-index="0"]')
          .find('[col-id="dealer_number"]')
          .invoke('text')
          .then(($dealerNumber) => {
            chai.expect($dealerNumber.trim()).to.not.equal(preproductionOrders[0].dealer_number);
          });
        cy.task('cleanupNcoRds', {
          ids: [ncoId],
        });
      });
  });

  it('Submit importer transfer for multiple orders, success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);

    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    //Importer transfer begins here
    cy.get('[row-index="0"]', { timeout: 10000 }).should('be.visible');
    cy.get('[row-index="1"]').should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="1"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[1].pk_new_car_order_id);
    cy.get(transferOrdersBtn).should('not.exist');

    cy.get('[row-index="0"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.wait(100);
    cy.get('[row-index="1"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();

    cy.get(transferOrdersBtn, { timeout: 1000 }).should('exist');
    cy.get(transferOrdersBtn).click({ force: true });

    cy.get(transferModal).should('exist').and('not.be.empty');
    cy.get(transferModal).find(multiDetailsContainer).should('be.visible');
    cy.get(transferModal).find(multiDetailsContainer).find('.col-container>.field-col').should('have.length', 2);
    cy.get(transferModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(0)
      .find('p-text')
      .should('have.length', 1);
    cy.get(transferModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(1)
      .find('p-text')
      .should('have.length', 1);

    // Select options for importer transfer (step 1)
    cy.get(importerLocator, { timeout: 5000 }).should('be.visible');
    cy.get(dealerLocator).should('be.visible');
    cy.selectOptionFromDropdown(importerLocator, newImporter);
    cy.selectOptionFromDropdown(dealerLocator, 1);
    cy.get(transferModal).find(confirmBtn).click();

    // Step 2
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, 2);
    cy.get(shippingCodeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(shippingCodeLocator, 1);

    //confirm and check result
    cy.intercept('POST', `${ordersEndpointURL}/importer-transfer`).as('importerTransfer');
    cy.get(transferModal).find(confirmBtn).click();

    cy.wait('@importerTransfer');
    cy.get(transferModal, { timeout: 10000 }).find('.header-success-container').should('be.visible');
    const copyIdsBtSelector = '[data-e2e="copy_ids"]';

    cy.get(copyIdsBtSelector, { timeout: 5000 }).should('be.visible');
    cy.get(copyIdsBtSelector).click();

    cy.window()
      .then((win) => win.navigator.clipboard.readText())
      .then((clipboardText) => {
        const ids = clipboardText.split(',').map((id) => id.trim());
        //close the dialog and check if table was updated correctly
        cy.get(transferModal).find(closeBtn).click();
        cy.wait('@fetchOrders');
        cy.get('[row-index="0"]').should('be.visible');

        // Filter for first order
        cy.get('[aria-label="New Car Order ID Filter Input"]')
          .clear()
          .type(ids[0], { delay: 100 })
          .should('have.value', ids[0]);
        cy.wait(3000);
        // Get dealer number from first selected order and compare with UI
        cy.get('[row-index="0"]')
          .find('[col-id="dealer_number"]')
          .invoke('text')
          .then(($dealerNumber) => {
            chai.expect($dealerNumber.trim()).to.not.equal(preproductionOrders[0].dealer_number);
          });

        // Filter for second order
        cy.get('[aria-label="New Car Order ID Filter Input"]').clear();
        cy.wait(3000);
        cy.get('[aria-label="New Car Order ID Filter Input"]')
          .type(ids[1], { delay: 100 })
          .should('have.value', ids[1]);
        cy.wait(3000);
        // Get dealer number from second selected order and compare with UI
        cy.get('[row-index="0"]')
          .find('[col-id="dealer_number"]')
          .invoke('text')
          .then(($dealerNumber) => {
            chai.expect($dealerNumber.trim()).to.not.equal(preproductionOrders[1].dealer_number);
          });
        cy.task('cleanupNcoRds', {
          ids,
        });
      });
  });

  it('Submit importer transfer for single order from list, error case (mocked cora call)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);

    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    //Importer transfer begins here
    cy.get('[row-index="0"]', { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(transferBtn, { timeout: 5000 }).should('be.visible').click();

    cy.get(transferModal).should('exist').and('not.be.empty');
    cy.get(transferModal).find('.importer-transfer-details-single-container').should('be.visible');

    // Select options for importer transfer (step 1)
    cy.get(importerLocator, { timeout: 5000 }).should('be.visible');
    cy.get(dealerLocator).should('be.visible');
    cy.selectOptionFromDropdown(importerLocator, 1);
    cy.selectOptionFromDropdown(dealerLocator, 1);
    cy.get(transferModal).find(confirmBtn).click();

    // Step 2
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.get(shippingCodeLocator).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, 2);
    cy.selectOptionFromDropdown(shippingCodeLocator, 1);

    //confirm and intercept cora api call to be able to let it fail
    cy.intercept('POST', '**/importer-transfer', (req) => {
      const impTransferResult: CoraNCOImporterTransferApiResponse = [
        {
          isSuccess: false,
          old_new_car_order_id: preproductionOrders[0].pk_new_car_order_id,
          error_msg: IMPORTER_TRANSFER_ERRORS.NCO_WITHOUT_QUOTA,
        },
      ];
      req.reply({
        body: impTransferResult,
      });
    });

    cy.get(transferModal).find(confirmBtn).click();
    cy.get(transferModal).find('.header-error-container', { timeout: 10000 }).should('be.visible');
    cy.get(transferModal).find(closeBtn).click();
  });
});
