import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { createApiGWHandlerWithInitLogger } from '../../../utils/api-gw-handler';
import { sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { secretCache } from '../../../utils/secret-cache';
import { InboundProcessMappingModel } from '../../../../lib/entities/inbound-mapping-model';

const lambdaLogger = new KasLambdaLogger('get-inbound-mappings', LogLevel.INFO);
const stage = getEnvVarWithAssert('STAGE');
const auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(auroraSecretArn);

const getInboundRulesFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  try {
    const dataSource = await createTypeORMDataSource(logger, auroraSecretArn, stage, [InboundProcessMappingModel]);
    const mappings = await dataSource.getRepository(InboundProcessMappingModel).find({
      order: {
        event: 'ASC',
      },
    });

    return sendSuccess({ body: mappings, reqHeaders: event.headers }, logger);
  } catch (error) {
    logger.log(LogLevel.WARN, 'Failed to fetch inbound mappings', { data: error });
    return sendFail({ message: 'Failed to fetch inbound mappings', status: 500, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGWHandlerWithInitLogger(lambdaLogger)(event, context, getInboundRulesFunc);
