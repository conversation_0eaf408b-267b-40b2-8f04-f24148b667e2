import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '../configureStore';
import { BannerState } from '@porsche-design-system/components-react';

export interface INotification {
  notificationId?: string;
  /**
   * UnixTimestampMs
   */
  showUntil?: number;
  state: BannerState;
  title?: string;
  msg: string;
}

export interface NotificationState {
  notifications: INotification[];
  status: 'idle' | 'loading' | 'failed';
}

export interface NotificationAction {
  payload: INotification;
  type: string;
}

const initialState: NotificationState = {
  notifications: [],
  status: 'idle',
};

export const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    displayNotification: (state, action: NotificationAction) => {
      const _n = action.payload;
      _n.notificationId = _n.notificationId ?? Math.floor(Math.random() * 10000).toString();
      let timeoutMs = 3000;
      if (_n.state === 'error') {
        timeoutMs = 10000;
      }
      _n.showUntil = _n.showUntil ?? new Date().getTime() + timeoutMs;
      state.notifications.push(_n);
    },
    dropNotification: (state, action: NotificationAction) => {
      state.notifications = [...state.notifications.filter((n) => n.notificationId !== action.payload.notificationId)];
    },
  },
});

export const { displayNotification, dropNotification } = notificationSlice.actions;
export const selectNotifications = (state: RootState): INotification[] => state.notifications.notifications;

export default notificationSlice.reducer;
