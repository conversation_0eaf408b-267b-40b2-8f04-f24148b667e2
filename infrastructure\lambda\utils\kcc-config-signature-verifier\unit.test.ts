import { Kas<PERSON><PERSON><PERSON><PERSON>Logger } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { KccConfigSignatureVerifier } from './kcc-config-signature-verifier';

const jwtResponse = {
  kty: 'EC',
  x: '7dqNSEfCsbzY-d3tfdvYPxDwPm4Z6Ts7eP4HXWMnBPs',
  y: 'S9EGRuNGVWbOw8VNRTlSUmZ_wBr6EAYTiWW4RYlTrqI',
  crv: 'P-256',
};
jest.spyOn(KccConfigSignatureVerifier, 'fetchPublicKey').mockResolvedValue(jwtResponse);

beforeEach(() => {
  jest.resetModules();
});

describe('KccConfig Validation Checks', () => {
  it('valid config should work', async () => {
    const config = {
      kas: {
        model_info: {
          model_type: '9923B2',
          model_year: 2025,
          country_code: 'C00',
        },
        configuration: {
          ordered_options: [
            {
              option_id: '2T',
              option_type: 'Exterior Color',
            },
            {
              option_id: '1V',
              option_type: 'Top Color',
            },
            {
              option_id: 'KX',
              option_type: 'Interior Color',
            },
            {
              option_id: '59B',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: 'Q2J',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '1G8',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '2UH',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '2V4',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '1N3',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '8T3',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: 'QK3',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '4GP',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '8IU',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: 'FT3',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: 'VC2',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '95B',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '2PJ',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '9VL',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '7Y8',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '0I2',
              option_type: 'Individual',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: 'VR4',
              option_type: 'Exclusive',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: 'UD1',
              option_type: 'Exclusive',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '0P9',
              option_type: 'Exclusive',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '2D4',
              option_type: 'Exclusive',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
            {
              option_id: '8VH',
              option_type: 'Exclusive',
              referenced_package: '',
              referenced_package_type: '',
              referenced_package_sort_order: 0,
              option_subtype_value: '',
              option_validity: {
                valid_until: '',
                valid_from: '',
                serial_to: '',
                serial_from: '',
                offer_period_start: '',
                offer_period_end: '',
                material_lead_time: '',
              },
            },
          ],
        },
      },
      pvms: {
        Id: 'APP-1104',
        Cnr: 'C00',
        Importernr: '9690000',
        Dealernr: '1160140',
        Importerid: 'DE',
        PpnUserId: 'schwellnusfr',
        BpId: '',
        IsDealerConfig: '',
        LeadId: '',
        ProspectKit: false,
        Modeltype: '9923B2',
        Modelyear: 'S',
        ConfigName: null,
        CofferId: '',
        TeqId: '',
        PorscheCode: '',
        TotalPriceGross: '171633.70',
        Taxes: '27403.70',
        Currency: 'u20ac',
        ConfigModified: '0',
        CommentsModified: '0',
        ExteriorColor: '2T',
        TopColor: '1V',
        InteriorColor: 'KX',
        FreeZOffer: '',
        VehicleOption: {
          Id: '1',
          OptionIndividual: {
            results: [
              {
                Id: '59B',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: 'Q2J',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '1G8',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '2UH',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '2V4',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '1N3',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '8T3',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: 'QK3',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '4GP',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '8IU',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: 'FT3',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: 'VC2',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '95B',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '2PJ',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '9VL',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '7Y8',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '0I2',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
            ],
          },
          OptionExclusive: {
            results: [
              {
                Id: 'VR4',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: 'UD1',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '0P9',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '2D4',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
              {
                Id: '8VH',
                ContentOf: '',
                UpgradeTo: '',
                DealerComment: '',
                ImporterComment: '',
                PagComment: '',
                SortOrder: '',
              },
            ],
          },
          OptionCustomTailoring: {
            results: [],
          },
          OptionLocal: {
            results: [],
          },
          OptionZoffer: {
            results: [],
          },
          OptionPackage: {
            results: [],
          },
        },
        Vguid: '',
        VehicleWLTPHeader: {
          results: [
            {
              uuid: 'd41712f0-2121-42cd-9632-c1cecf7d6a1f',
              response_date: '/Date(1734007095660)/',
              return_code: '200',
              error_message: 'OK',
              check_date: '/Date(1725148800000)/',
              DataSource: 'P',
            },
          ],
        },
        VehicleWLTPBody: {
          results: [
            {
              Typification: 'WLTP_EU',
              EngineType: 'ICE',
              DataSource: 'P',
              WLTPBodyWLTPBodyRecord: {
                results: [
                  {
                    DataType: 'GENERAL_DATA',
                    DataSource: 'P',
                    WLTPBodyRecordWLTPDataRecord: {
                      results: [
                        {
                          ValueType: '',
                          FuelType: '',
                          EnergyManagementType: '',
                          DataSource: 'P',
                          WLTPDataRecordWLTPValue: {
                            results: [
                              {
                                Key: 'TIRE_ROLLING_RESISTANCE_TOTAL',
                                Value: '9.8',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: '0/00',
                                DataSource: 'P',
                              },
                              {
                                Key: 'TIRE_ROLLING_RESISTANCE_FRONT',
                                Value: '9.8',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: '0/00',
                                DataSource: 'P',
                              },
                              {
                                Key: 'TIRE_ROLLING_RESISTANCE_REAR',
                                Value: '9.8',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: '0/00',
                                DataSource: 'P',
                              },
                              {
                                Key: 'DRAG_COEFFICIENT',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: '',
                                DataSource: 'P',
                              },
                              {
                                Key: 'DRAG_COEFFICIENT_H',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: '',
                                DataSource: 'P',
                              },
                              {
                                Key: 'AERODYNAMIC_DRAG',
                                Value: '0.65',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'm2',
                                DataSource: 'P',
                              },
                              {
                                Key: 'AERODYNAMIC_DRAG_H',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'm2',
                                DataSource: 'P',
                              },
                              {
                                Key: 'FRONT_SURFACE_A',
                                Value: '2.09',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'm2',
                                DataSource: 'P',
                              },
                              {
                                Key: 'FRONT_SURFACE_A_H',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'm2',
                                DataSource: 'P',
                              },
                              {
                                Key: 'MASS_VEHICLE',
                                Value: '1657',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'MASS_VEHICLE_FRONT',
                                Value: '614',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'MASS_VEHICLE_REAR',
                                Value: '1043',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'MASS_ACTUAL',
                                Value: '1732',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'EU_LEER_MIN',
                                Value: '1675.0',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'EU_LEER_MAX',
                                Value: '1770.0',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'TEST_MASS_VEHICLE',
                                Value: '1790',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'TEST_MASS_VEHICLE_H',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'kg',
                                DataSource: 'P',
                              },
                              {
                                Key: 'HIGH_TYPING',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: '',
                                DataSource: 'P',
                              },
                              {
                                Key: 'VEHICLE_RESISTANCE_COEFFICENT_F0',
                                Value: '166.6',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'N',
                                DataSource: 'P',
                              },
                              {
                                Key: 'VEHICLE_RESISTANCE_COEFFICENT_F0_H',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'N',
                                DataSource: 'P',
                              },
                              {
                                Key: 'VEHICLE_RESISTANCE_COEFFICENT_F1',
                                Value: '1.446',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'N/(km/h)',
                                DataSource: 'P',
                              },
                              {
                                Key: 'VEHICLE_RESISTANCE_COEFFICENT_F1_H',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'N/(km/h)',
                                DataSource: 'P',
                              },
                              {
                                Key: 'VEHICLE_RESISTANCE_COEFFICENT_F2',
                                Value: '0.02632',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'N/(km/h)2',
                                DataSource: 'P',
                              },
                              {
                                Key: 'VEHICLE_RESISTANCE_COEFFICENT_F2_H',
                                Value: '',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'N/(km/h)2',
                                DataSource: 'P',
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                  {
                    DataType: 'INTERPOLATIONS',
                    DataSource: 'P',
                    WLTPBodyRecordWLTPDataRecord: {
                      results: [
                        {
                          ValueType: 'CONSUMPTION',
                          FuelType: 'PETROL_E10',
                          EnergyManagementType: 'PURE',
                          DataSource: 'P',
                          WLTPDataRecordWLTPValue: {
                            results: [
                              {
                                Key: 'LOW',
                                Value: '18.5',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'l/100km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'MEDIUM',
                                Value: '10.1',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'l/100km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'HIGH',
                                Value: '8.6',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'l/100km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'EXTRA_HIGH',
                                Value: '9.0',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'l/100km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'COMBINED',
                                Value: '10.4',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'l/100km',
                                DataSource: 'P',
                              },
                            ],
                          },
                        },
                        {
                          ValueType: 'CO2',
                          FuelType: 'PETROL_E10',
                          EnergyManagementType: 'PURE',
                          DataSource: 'P',
                          WLTPDataRecordWLTPValue: {
                            results: [
                              {
                                Key: 'LOW',
                                Value: '421',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'g/km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'MEDIUM',
                                Value: '229',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'g/km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'HIGH',
                                Value: '197',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'g/km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'EXTRA_HIGH',
                                Value: '205',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'g/km',
                                DataSource: 'P',
                              },
                              {
                                Key: 'COMBINED',
                                Value: '236',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: 'g/km',
                                DataSource: 'P',
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                  {
                    DataType: 'IP_FAMILY',
                    DataSource: 'P',
                    WLTPBodyRecordWLTPDataRecord: {
                      results: [
                        {
                          ValueType: '',
                          FuelType: '',
                          EnergyManagementType: '',
                          DataSource: 'P',
                          WLTPDataRecordWLTPValue: {
                            results: [
                              {
                                Key: 'NAME',
                                Value: 'IP-EA992II2B00AT00-WP0',
                                ValueFrom: '',
                                ValueTo: '',
                                Unit: '0/00',
                                DataSource: 'P',
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            },
          ],
        },
        VehicleTag: {
          results: [
            {
              Key: 'KB',
              Value: 'PCCDP',
            },
          ],
        },
        XTraceId: '17f92862-0b89-4c6d-bb69-80fc6a4cba0e',
      },
    };
    const signature =
      'MEUCIQD/pz89maV14HiwM+ZuVSAEJPxRjR8tfHjJ13vgwobRjQIgcBVC1wHPO2uggGEmmaiI/MfrAE/nK8gFH+QSMWkBdWQ=';
    const cookie = 'KasAuthorization-somestage=asdf';
    const res = await KccConfigSignatureVerifier.verifySignature(
      config,
      signature,
      cookie,
      'somestage',
      new KasLambdaLogger('test'),
    );
    expect(res).toBe(true);
  });
});
