import React, { useEffect, useMemo, useState } from 'react';
import { useGetAuditTrailByNcoIdQuery } from '../../store/api/AuditApi';
import { useParams } from 'react-router-dom';
import { PButtonPure, PHeading } from '@porsche-design-system/components-react';
import { FetchError } from '../errors/FetchErrors';
import { ColDef, GridApi, GridReadyEvent, RowClickedEvent } from 'ag-grid-community';
import '../lists/etc/ag-grid-compact-mode.css';
import '../lists/etc/ag-grid-action-menu.css';
import '@porsche-design-system/components-react/ag-grid/theme.css';
import { AgGridReact } from 'ag-grid-react';
import { useTranslation } from 'react-i18next';
import { NewCarOrderAuditTrailModel } from '../../../../infrastructure/lib/entities/new-car-order-audit-trail-model';
import AuditDetailModal from './AuditDetailModal';
import { useInIframe } from '../../utils/useInIframe';
import { useTheme } from '../../utils/useTheme';

type AuditTrailListEntry = NewCarOrderAuditTrailModel & {
  order_status_onevms_code: string;
  order_status_onevms_error_code: string;
};

const AuditMainPage: React.FC = () => {
  const { nco_id } = useParams();
  const theme = useTheme();
  const isInIframe = useInIframe();

  if (!nco_id) {
    return <div>No NewCarOrderId set in path</div>;
  }

  const auditNcoRes = useGetAuditTrailByNcoIdQuery(nco_id);

  if (auditNcoRes.isError) {
    return <FetchError custom_error={auditNcoRes.error} error_area="audit_nco" />;
  }
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const { t, i18n } = useTranslation();

  const [selectedAuditTrail, setSelectedAuditTrail] = useState<NewCarOrderAuditTrailModel>();
  const [auditDetailModalOpen, setAuditDetailModalOpen] = useState<boolean>(false);

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
    params.api.setGridOption('loading', auditNcoRes.isLoading || auditNcoRes.isFetching);
  };

  useEffect(() => {
    if (gridApi) {
      gridApi.setGridOption('loading', auditNcoRes.isLoading || auditNcoRes.isFetching);
    }
  }, [auditNcoRes.isFetching, gridApi, auditNcoRes.isLoading]);

  useEffect(() => {
    if (auditNcoRes.data) {
      setRowData(
        auditNcoRes.data.map((at) => {
          return {
            ...at,
            order_status_onevms_code: at.new_nco?.order_status_onevms_code ?? '',
            order_status_onevms_error_code: at.new_nco?.order_status_onevms_error_code ?? '',
          };
        }),
      );
    }
  }, [auditNcoRes.data, gridApi, t, i18n.language]);

  const columns = useMemo<ColDef<AuditTrailListEntry>[]>(
    () => [
      {
        headerName: t('new_car_order_id'),
        headerTooltip: t('new_car_order_id'),
        field: 'pk_new_car_order_id',
        tooltipField: 'pk_new_car_order_id',
      },
      {
        headerName: t('timestamp'),
        headerTooltip: t('timestamp'),
        field: 'action_at',
        tooltipField: 'action_at',
        sort: 'desc',
        cellRenderer: (params: any) => {
          const isoString = params.value;
          if (!isoString) return '';
          const date = new Date(isoString);
          const formattedDate = date.toLocaleString('de-DE', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
          });
          return formattedDate;
        },
      },
      {
        headerName: t('action_by'),
        headerTooltip: t('action_by'),
        field: 'action_by',
        tooltipField: 'action_by',
      },
      {
        headerName: t('action_type'),
        headerTooltip: t('action_type'),
        field: 'action_type',
        tooltipField: 'action_type',
      },
      {
        headerName: t('order_status_onevms_code'),
        headerTooltip: t('order_status_onevms_code'),
        field: 'order_status_onevms_code',
        tooltipField: 'order_status_onevms_code',
      },
      {
        headerName: t('order_status_onevms_error_code'),
        headerTooltip: t('order_status_onevms_error_code'),
        field: 'order_status_onevms_error_code',
        tooltipField: 'order_status_onevms_error_code',
      },
      {
        headerName: t('action_exported'),
        headerTooltip: t('action_exported'),
        field: 'action_exported',
        tooltipField: 'action_exported',
      },
      {
        headerName: t('correlation_id'),
        headerTooltip: t('correlation_id'),
        field: 'action_correlation_id',
        tooltipField: 'action_correlation_id',
      },
    ],
    [t],
  );

  const handleRowClick = (event: RowClickedEvent) => {
    setSelectedAuditTrail(event.data);
    setAuditDetailModalOpen(true);
  };

  const [rowData, setRowData] = useState<AuditTrailListEntry[]>([]);

  const handleRefresh = async () => {
    gridApi?.setGridOption('loading', true);
    await auditNcoRes.refetch();
  };

  return (
    <div>
      <AuditDetailModal
        open={auditDetailModalOpen}
        auditTrail={selectedAuditTrail}
        closeModal={() => setAuditDetailModalOpen(false)}
      ></AuditDetailModal>
      <div
        className={`${theme === 'light' ? 'ag-theme-pds' : 'ag-theme-pds-dark'} compact`}
        style={{ width: '100%', overflow: 'hidden', position: 'relative' }}
      >
        <PHeading size="medium" style={{ paddingBottom: '1vh', height: '3vh' }}>
          Audit Trail - {nco_id}
          <PButtonPure
            theme={theme}
            size={'small'}
            icon="reset"
            underline={false}
            style={{ marginLeft: '20px', verticalAlign: 'middle' }}
            onClick={handleRefresh}
          ></PButtonPure>
        </PHeading>
        <div
          style={{
            height: isInIframe ? '90vh' : '80vh',
            overflow: 'hidden',
            flex: '1 1 0px',
            width: '100%',
          }}
        >
          <AgGridReact
            gridId="FailedOrderGrid"
            onGridReady={onGridReady}
            columnDefs={columns}
            floatingFiltersHeight={70}
            enableCellTextSelection={true}
            defaultColDef={{
              filter: 'agTextColumnFilter',
              sortable: true,
              resizable: true,
              editable: false,
              floatingFilter: true,
              flex: 1,
              minWidth: 100,
              maxWidth: 400,
              cellStyle: () => ({
                display: 'flex',
                alignItems: 'center',
              }),
              wrapHeaderText: true,
              autoHeight: true,
              wrapText: true,
            }}
            tooltipShowDelay={1000}
            tooltipHideDelay={4000}
            rowData={rowData}
            rowSelection="multiple"
            domLayout="normal"
            suppressRowClickSelection
            alwaysShowHorizontalScroll={false}
            suppressHorizontalScroll={false}
            onRowClicked={handleRowClick}
            // Pagination props
            pagination={true}
            paginationPageSize={100}
            paginationPageSizeSelector={[20, 100, 500]}
          />
        </div>
      </div>
    </div>
  );
};

export default AuditMainPage;
