/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DataSource, In } from 'typeorm';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';
import { CoraNCOBaseApiRequest, CoraNCOCopyApiResponse } from '../../../../lib/types/new-car-order-types';
import { ncoApiToDbObj } from '../../../utils/utils-typeorm';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { OneVmsSourceSystemKey } from '../../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn('copy-new-car-order');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;

// Testdaten
const importerOrgId = 'uuid-copy-order-importer-01';
const dealerOrgId = 'uuid-copy-order-dealer-01';
const dealer2OrgId = 'uuid-copy-order-dealer-02';
const dealer3OrgId = 'uuid-copy-order-dealer-03';
const existingNcoId = 'EXIST001';

const mdDealer1 = {
  pk_importer_number: 'ItNcoImpCopy',
  sk_dealer_number: 'ItNcoDlrCopy',
  display_name: 'Test Dealer 1',
  standard_port_code: 'ItNcoPc1',
  alternative_port_codes: ['ItNcoPc2', 'ItNcoPc3'],
};

const mdDealer2 = {
  pk_importer_number: 'ItNcoImpCopy',
  sk_dealer_number: 'ItNcoDlrCopy2',
  display_name: 'Test Dealer 2',
  standard_port_code: 'ItNcoPc1',
  alternative_port_codes: ['ItNcoPc2', 'ItNcoPc3'],
};

const mdDealer3 = {
  pk_importer_number: 'differentImporter',
  sk_dealer_number: 'ItNcoDlrCopy3',
  display_name: 'Test Dealer 3',
  standard_port_code: 'ItNcoPc1',
  alternative_port_codes: ['ItNcoPc2', 'ItNcoPc3'],
};

const mdDealerNotAuthorizedByImp = {
  pk_importer_number: 'ItNcoImp1',
  sk_dealer_number: 'ItNcoDlrCopy4',
  display_name: 'Test Dealer 4',
  standard_port_code: 'ItNcoPc1',
  alternative_port_codes: ['ItNcoPc2', 'ItNcoPc3'],
};

const mdImporter = {
  pk_importer_number: 'ItNcoImpCopy',
  code: 'IT',
  display_name: 'Test Importer',
  port_codes: ['ItNcoPc1', 'ItNcoPc2', 'ItNcoPc3', 'ItNcoPc4'],
};

const orgRels: CoraOrgRelModel[] = [
  {
    pk_ppn_id: importerOrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoImpCopy',
    display_name: 'Importer Test',
    importer_number: 'ItNcoImpCopy',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  {
    pk_ppn_id: dealerOrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: mdDealer1.sk_dealer_number,
    display_name: 'Dealer Test 1',
    importer_number: 'ItNcoImpCopy',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  {
    pk_ppn_id: dealer2OrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: mdDealer2.sk_dealer_number,
    display_name: 'Dealer Test 2',
    importer_number: 'ItNcoImpCopy',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  {
    pk_ppn_id: dealer3OrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: mdDealer3.sk_dealer_number,
    display_name: 'Dealer Test 3',
    importer_number: 'differentImporter',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  {
    pk_ppn_id: dealerOrgId,
    parent_ppn_id: dealerOrgId,
    dealer_number: mdDealer1.sk_dealer_number,
    display_name: 'Dealer Test 1',
    importer_number: 'ItNcoImpCopy',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  {
    pk_ppn_id: dealer2OrgId,
    parent_ppn_id: dealer2OrgId,
    dealer_number: mdDealer2.sk_dealer_number,
    display_name: 'Dealer Test 2',
    importer_number: 'ItNcoImpCopy',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

//model type visibility test data
const mtvImpInPast: ModelTypeVisibilityModel = {
  //For IMP visible date in past
  importer_number: 'ItNcoImpCopy',
  cnr: 'C23',
  model_type: 'MT0001',
  my4: '2030',
  role: 'IMP',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};
const mtvImpInFuture: ModelTypeVisibilityModel = {
  //For IMP visible date in future
  importer_number: 'ItNcoImpCopy',
  cnr: 'C23',
  model_type: 'MT0002',
  my4: '2030',
  role: 'IMP',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvDlrInPast: ModelTypeVisibilityModel = {
  //For DLR visible date in past
  importer_number: 'ItNcoImpCopy',
  cnr: 'C23',
  model_type: 'MT0001',
  my4: '2030',
  role: 'DLR',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};
const mtvDlrInFuture: ModelTypeVisibilityModel = {
  //For DLR visible date in future
  importer_number: 'ItNcoImpCopy',
  cnr: 'C23',
  model_type: 'MT0002',
  my4: '2030',
  role: 'DLR',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvs: ModelTypeVisibilityModel[] = [mtvImpInPast, mtvImpInFuture, mtvDlrInPast, mtvDlrInFuture];

const scs = [
  {
    pk_shipping_code: 'ItScBlackAllowCp',
    description: 'Allowed Shipping Code',
    imp_is_blacklist: false,
    importers: ['ItNcoImpCopy'],
    dlr_is_blacklist: false,
    dealers: ['ItNcoDlrCopy'],
    is_deactivated: false,
  },
];

const ots = [
  {
    pk_order_type: 'ItOtWhiteAllowCp',
    description: 'Allowed Order Type',
    imp_is_blacklist: false,
    importers: ['ItNcoImpCopy'],
    dlr_is_visible: true,
    is_deactivated: false,
  },
];

//auth context apps test data
const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithVisibilityDlr = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'DLR',
    },
  ],
};

const newCarOrderFetchObject: NewCarOrderModel = {
  pk_new_car_order_id: existingNcoId,
  dealer_number: mdDealer1.sk_dealer_number,
  importer_code: 'IT',
  importer_number: mdImporter.pk_importer_number,
  created_by: mdDealer1.sk_dealer_number,
  modified_by: 'TestRunner',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'ItOtWhiteAllowCp',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'ItScBlackAllowCp',
  receiving_port_code: 'ItNcoPc1',
  order_status_onevms_code: 'PP2000',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2024-10-12T00:00:00.000Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  deal_id: existingNcoId,
  cnr: 'C23',
  configuration: {
    created_by: mdDealer1.sk_dealer_number,
    modified_by: 'TestRunner',
    ordered_options: [
      {
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        created_by: mdDealer1.sk_dealer_number,
        modified_by: 'TestRunner',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            option_id: 'CONTENT1',
            option_type: 'CONTENTTYPE1',
            validity_valid_until: '2050-12-20',
            validity_valid_from: '2022-12-20',
            validity_serial_to: '2050-12-20',
            validity_serial_from: '2022-12-20',
            validity_offer_period_start: '2022-12-20',
            validity_offer_period_end: '2050-12-20',
            validity_material_lead_time: '0',
            created_by: mdDealer1.sk_dealer_number,
            modified_by: 'TestRunner',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
        ],
      },
    ],
  },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const consumedQuotas = {
  '2024-11': 10,
  '2024-12': 2,
  '2025-01': 4,
};

// Mock quota api
const defaultEventParams = {
  queryStringParameters: { mock_quota_api: 'true' },
};

const noMockEventParams = {
  queryStringParameters: { mock_quota_api: 'false' },
};

let dataSource: DataSource;
const createdNcoIds: string[] = [];

let testMtvs: ModelTypeVisibilityModel[] = [];

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    ModelTypeVisibilityModel,
  ]);
  const mtvRepository = dataSource.getRepository(ModelTypeVisibilityModel);
  testMtvs = await mtvRepository.save(mtvs);
  const repository = dataSource.getRepository(NewCarOrderModel);
  await repository.save(
    ncoApiToDbObj(
      newCarOrderFetchObject as unknown as CoraNCOBaseApiRequest,
      'IntegrationTester',
      newCarOrderFetchObject,
    ),
  );
  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    { tableName: mdDlrTableName, objs: [mdDealer1, mdDealer2, mdDealer3, mdDealerNotAuthorizedByImp] },
    { tableName: mdImpTableName, objs: [mdImporter] },
    { tableName: mdScTableName, objs: scs },
    { tableName: mdOtTableName, objs: ots },
  ]);
});

afterAll(async () => {
  const repository = dataSource.getRepository(NewCarOrderModel);
  await repository.delete({ pk_new_car_order_id: newCarOrderFetchObject.pk_new_car_order_id });
  await repository.delete({ pk_new_car_order_id: In(createdNcoIds) });
  await dataSource.getRepository(ModelTypeVisibilityModel).remove(testMtvs);
  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRels.map((org) => ({ pk_ppn_id: org.pk_ppn_id, parent_ppn_id: org.parent_ppn_id })),
    },
    {
      tableName: mdDlrTableName,
      pks: [
        { pk_importer_number: mdDealer1.pk_importer_number, sk_dealer_number: mdDealer1.sk_dealer_number },
        { pk_importer_number: mdDealer2.pk_importer_number, sk_dealer_number: mdDealer2.sk_dealer_number },
        { pk_importer_number: mdDealer3.pk_importer_number, sk_dealer_number: mdDealer3.sk_dealer_number },
        {
          pk_importer_number: mdDealerNotAuthorizedByImp.pk_importer_number,
          sk_dealer_number: mdDealerNotAuthorizedByImp.sk_dealer_number,
        },
      ],
    },
    { tableName: mdImpTableName, pks: [{ pk_importer_number: mdImporter.pk_importer_number }] },
    { tableName: mdScTableName, pks: scs.map((sc) => ({ pk_shipping_code: sc.pk_shipping_code })) },
    { tableName: mdOtTableName, pks: ots.map((ot) => ({ pk_order_type: ot.pk_order_type })) },
  ]);
});

describe('Copy NewCarOrder Integration Tests', () => {
  it('should return 400 if nco id is missing in path parameters', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(400);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('Could not get order, invalid id');
  });

  it('should return 401 if org is missing in auth context', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      kasApplications: appsWithVisibilityImp,
      pathParameters: { ncoId: existingNcoId },
      body: consumedQuotas,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toStrictEqual(401);
    const body = JSON.parse(res.body);
    expect(body.message).not.toBeUndefined();
    expect(body.message).toContain('Auth context is missing');
    expect(body.pk_new_car_order_id).toBeUndefined();
  });

  it('should return 401 if visibility is missing in auth context', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      userOrgId: importerOrgId,
      kasApplications: appsWithNoRole,
      pathParameters: { ncoId: existingNcoId },
      body: consumedQuotas,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toStrictEqual(401);
    const body = JSON.parse(res.body);
    expect(body.message).not.toBeUndefined();
    expect(body.message).toContain('Auth context is missing');
    expect(body.pk_new_car_order_id).toBeUndefined();
  });

  it('should return 403 if user is not authorized for any dealers', async () => {
    const event = createApiGwEvent({
      userOrgId: 'uuid-not-going-to-work',
      kasApplications: appsWithVisibilityDlr,
      queryStringParameters: { newSelectedDealer: mdDealer2.sk_dealer_number },
      pathParameters: { ncoId: existingNcoId },
      body: consumedQuotas,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(403);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('User is not authorized to any dealers');
  });

  it('should return 404 if source NewCarOrder cannot be fetched', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: 'NOTEXIST1' },
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(404);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('Error fetching source NewCarOrder');
  });

  it('should return 400 if consumed quotas are not in event body', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: existingNcoId },
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(400);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('Error in provided quotas');
  });

  it('should return 400 if more than six quota months are requested', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: existingNcoId },
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      body: { '2024-11': 1, '2024-12': 1, '2025-01': 1, '2025-02': 1, '2025-03': 1, '2025-04': 1, '2025-05': 1 },
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(400);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('Error in provided quotas');
  });

  it('should return 400 if total orders exceed limit', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: existingNcoId },
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      body: { '2025-03': 80, '2025-04': 50 },
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(400);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('Cannot create more than 100 orders in a single request');
  });

  it('should return 403 if newSelectedDealer relation is not in allowed dealers', async () => {
    const event = createApiGwEvent({
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      queryStringParameters: { newSelectedDealer: mdDealerNotAuthorizedByImp.sk_dealer_number },
      pathParameters: { ncoId: existingNcoId },
      body: consumedQuotas,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(403);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('Newly selected dealer could not be found in allowed dealers of the user');
  });

  it('should return 409 if the selected dealer is not in the same market', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: existingNcoId },
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      queryStringParameters: { newSelectedDealer: mdDealer3.sk_dealer_number },
      body: consumedQuotas,
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(409);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('The selected dealer is not in the same market');
  });

  it('should return 409 if quota month is invalid (mock err case 1)', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      pathParameters: { ncoId: existingNcoId },
      body: { '2999-01': 1 },
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toStrictEqual(409);
    const body = JSON.parse(res.body);
    expect(body.message).not.toBeUndefined();
    expect(body.message).toContain('no quota available');
    expect(body.pk_new_car_order_id).toBeUndefined();
  });

  it('should return 409 if quota month is invalid (mock err case 2)', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      pathParameters: { ncoId: existingNcoId },
      body: { '2888-01': 1 },
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toStrictEqual(409);
    const body = JSON.parse(res.body);
    expect(body.message).not.toBeUndefined();
    expect(body.message).toContain('no quota available');
    expect(body.pk_new_car_order_id).toBeUndefined();
  });

  it('should return 409 if quota month is invalid (real quota API err case)', async () => {
    const event = createApiGwEvent({
      ...noMockEventParams,
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      pathParameters: { ncoId: existingNcoId },
      body: { '2888-01': 1 },
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toStrictEqual(409);
    const body = JSON.parse(res.body);
    expect(body.message).not.toBeUndefined();
    expect(body.message).toContain('no quota available');
    expect(body.pk_new_car_order_id).toBeUndefined();
  }, 30000);

  it('should return 409 if total consumed quota is zero', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: existingNcoId },
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      body: { '2024-11': 0 },
    });
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(409);
    const body = JSON.parse(res.body);
    expect(body.message).toContain('Error creating new car order, no quota available');
  });

  it('should return 200 and the correct response for a valid request from a DLR', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: existingNcoId },
      userOrgId: dealerOrgId,
      kasApplications: appsWithVisibilityDlr,
      body: consumedQuotas,
    });

    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(200);

    const body: CoraNCOCopyApiResponse = JSON.parse(res.body);
    expect(Array.isArray(body)).toBe(true);
    expect(body).toHaveLength(Object.keys(consumedQuotas).length);

    for (const [quotaMonth, quotaRequested] of Object.entries(consumedQuotas)) {
      const result = body.find((q) => q.quota_month === quotaMonth);

      expect(result).toBeDefined();

      // Validate the structure of the result
      expect(result).toHaveProperty('quota_month', quotaMonth);
      expect(result).toHaveProperty('quota_consumed');
      expect(result).toHaveProperty('quota_failed');
      expect(result).toHaveProperty('new_car_order_ids');
      expect(Array.isArray(result!.new_car_order_ids)).toBe(true);

      // Validate the returned values
      expect(result!.quota_consumed).toBeLessThanOrEqual(quotaRequested);
      expect(result!.quota_failed).toBe(quotaRequested - result!.quota_consumed);
      expect(result!.new_car_order_ids.length).toBe(result!.quota_consumed);
      const newCarOrderIds = body.map((item: { new_car_order_ids: string[] }) => item.new_car_order_ids).flat();
      createdNcoIds.push(...newCarOrderIds);
    }
  });

  it('should return 200 and the correct response for a valid request from an IMP, who switches the dealer', async () => {
    const event = createApiGwEvent({
      ...defaultEventParams,
      pathParameters: { ncoId: existingNcoId },
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibilityImp,
      queryStringParameters: { newSelectedDealer: mdDealer2.sk_dealer_number },
      body: consumedQuotas,
    });

    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toBe(200);

    const body: CoraNCOCopyApiResponse = JSON.parse(res.body);
    expect(Array.isArray(body)).toBe(true);
    expect(body).toHaveLength(Object.keys(consumedQuotas).length);

    for (const [quotaMonth, quotaRequested] of Object.entries(consumedQuotas)) {
      const result = body.find((q) => q.quota_month === quotaMonth);

      expect(result).toBeDefined();

      // Validate the structure of the result
      expect(result).toHaveProperty('quota_month', quotaMonth);
      expect(result).toHaveProperty('quota_consumed');
      expect(result).toHaveProperty('quota_failed');
      expect(result).toHaveProperty('new_car_order_ids');
      expect(Array.isArray(result!.new_car_order_ids)).toBe(true);

      // Validate the returned values
      expect(result!.quota_consumed).toBeLessThanOrEqual(quotaRequested);
      expect(result!.quota_failed).toBe(quotaRequested - result!.quota_consumed);
      expect(result!.new_car_order_ids.length).toBe(result!.quota_consumed);
      const newCarOrderIds = body.map((item: { new_car_order_ids: string[] }) => item.new_car_order_ids).flat();
      createdNcoIds.push(...newCarOrderIds);
    }
  });
});
