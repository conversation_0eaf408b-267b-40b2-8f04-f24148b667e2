import { Column, <PERSON>tity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { BaseModel } from './base-model';
import { Constants, OneVmsEventHandlerKey } from '../utils/constants';
import { OneVmsEventKey, OneVmsSourceSystemKey } from '../types/process-steering-types';

@Entity({ name: 'inbound_process_rules', schema: Constants.CORA_MD_AURORA_SCHEMA })
@Unique(['order_status_code', 'error_status_code', 'invoice_status_code', 'event', 'source_system'])
export class InboundProcessMappingModel extends BaseModel {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'text', nullable: false })
  public order_status_code: string;

  @Column({ type: 'text', nullable: false })
  public error_status_code: string;

  @Column({ type: 'text', nullable: false })
  public invoice_status_code: string;

  @Column({ type: 'text', nullable: false })
  public event: OneVmsEventKey;

  @Column({ type: 'text', nullable: false })
  public source_system: OneVmsSourceSystemKey;

  @Column({ type: 'text', nullable: false })
  public target_event_handler: OneVmsEventHandlerKey;
}
