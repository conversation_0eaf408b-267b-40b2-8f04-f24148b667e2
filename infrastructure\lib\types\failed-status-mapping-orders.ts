export interface CoraFailedStatusMappingOrdersDatabaseObject {
  object_id: string;
  key: string;
  numOfTries: number;
  value: string;
  failed_status_timestamp: string;
  vehicle_status_pvms_code: string;
  new_car_order_id: string;
}

export interface Ids {
  new_car_order_id: string;
}

export interface StatusInfo {
  vehicle_status_pvms_code: string;
}
export interface OrderInfo {
  status_info: StatusInfo;
}

interface Value {
  ids: Ids;
  order_info: OrderInfo;
}
export interface FailedStatusMappingOrderDTO {
  key: string;
  numOfTries: number;
  timestamp: string;
  value: Value;
}
