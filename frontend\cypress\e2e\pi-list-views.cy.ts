import { USERNAME_WRITE } from '../support/constants';
import { generateMtvs, generatePIs } from '../support/order-lists-test-data';
import { retryableBefore } from '../support/retry';

const piPrefix = 'PIL';
const piEndpointURL = '**/purchase-intention/get';

const pis = generatePIs(piPrefix, 3);
pis[2].is_converted = true; // set the last pi to be converted
pis[0].quota_month = '2020-12'; // set the first pi to have a different quota month
pis[0].model_year = '2020'; // set the first pi to have a different model year

//add mtvs for every pi so that is and orders are visible in lists
const mtvs = generateMtvs(pis);

const displayPurchaseIntentionsTab: string = '[data-e2e="display_purchase_intentions"]';

describe('Purchase Intention List Tests', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task('preparePiRds', { objs: pis }, { timeout: 10000 });
    cy.task('prepareMtvRds', { objs: mtvs }, { timeout: 10000 });
    cy.visit('/lists/purchase-intentions');
  });

  afterEach(() => {
    cy.task('cleanupPiRds', {
      ids: pis.map((pi) => pi.purchase_intention_id),
    });
    cy.task('cleanupNcoRds', {
      ids: pis.map((pi) => pi.purchase_intention_id),
    });
    cy.task('cleanupMtvRds', { objs: mtvs });
  });

  it('Display purchase intention list', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    //sort asc by pi ic
    cy.get('.ag-header').find('[col-id="purchase_intention_id"]').click();

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="purchase_intention_id"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="business_partner_id"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="quota_month"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="model_year"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="model_text"]').should('be.visible');
    //scroll table to right so that the rest of the columns is visible
    cy.get('[row-index="0"]').find('[col-id="dealer_name"]').scrollIntoView();
    cy.get('[row-index="0"]').find('[col-id="model_type"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="dealer_number"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="dealer_name"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="actions"]').should('be.visible');
  });

  it('Sort by purchase intention id', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    cy.intercept('POST', `${piEndpointURL}`).as('fetchPis');
    //sort asc by pi ic
    cy.get('.ag-header').find('[col-id="purchase_intention_id"]').click();
    //filter for test PIs
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[0].purchase_intention_id);

    // click again to make it desc
    cy.get('.ag-header').find('[col-id="purchase_intention_id"]').click();
    //sorting resets filters...
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[1].purchase_intention_id);
  });

  it('Sort by quota month', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    cy.intercept('POST', `${piEndpointURL}`).as('fetchPis');

    //sort asc by pi ic
    cy.get('.ag-header').find('[col-id="quota_month"]').click();

    //filter for test PIs
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[0].purchase_intention_id);

    // click again to make it desc
    cy.get('.ag-header').find('[col-id="quota_month"]').click();
    //sorting resets filters...
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[1].purchase_intention_id);
  });

  it('Sort by model year', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    cy.intercept('POST', `${piEndpointURL}`).as('fetchPis');
    //sort asc by pi ic
    cy.get('.ag-header').find('[col-id="model_year"]').click();
    //filter for test PIs
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[0].purchase_intention_id);

    // click again to make it desc
    cy.get('.ag-header').find('[col-id="model_year"]').click();
    //sorting resets filters...
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[1].purchase_intention_id);
  });

  it('Filter by purchase intention id prefix and check that converted PIs are not visible', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    cy.intercept('POST', `${piEndpointURL}`).as('fetchPis');
    //sort asc by pi ic
    cy.get('.ag-header').find('[col-id="model_year"]').click();
    //filter for test PIs
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('[row-index="0"]', { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[0].purchase_intention_id);
    cy.get('[row-index="1"]', { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="1"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[1].purchase_intention_id);
    cy.get('[row-index="2"]', { timeout: 5000 }).should('not.exist');
  });

  it('Filter by quota month', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    cy.intercept('POST', `${piEndpointURL}`).as('fetchPis');
    //filter for test PIs
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    const quotaYear = pis[1].quota_month.slice(0, 4); // only year
    cy.get('input[aria-label="Quota Month Filter Input"]')
      .type(quotaYear, { delay: 100 })
      .should('have.value', quotaYear);

    cy.get('[row-index="0"]', { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[1].purchase_intention_id);
  });

  it('Filter by model year', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    cy.intercept('POST', `${piEndpointURL}`).as('fetchPis');
    //filter for test PIs
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    cy.get('input[aria-label="Model Year Filter Input"]').type('2024', { delay: 100 }).should('have.value', '2024');

    cy.get('[row-index="0"]', { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="purchase_intention_id"]')
      .should('have.text', pis[1].purchase_intention_id);
  });
});
