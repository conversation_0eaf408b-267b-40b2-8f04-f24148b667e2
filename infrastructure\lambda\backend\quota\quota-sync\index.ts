import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { CoraQuota } from '../../../../lib/types/quota-api-types';
import { Batch<PERSON>riteCommand, BatchWriteCommandInput } from '@aws-sdk/lib-dynamodb';
import { GeneralError } from '../../../utils/errors';
import { correlationHeader, getEnvVarWithAssert, splitIntoBatches } from '../../../utils/utils';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { KafkaQuotaEventValue } from '../../../../lib/types/quota-api-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

const QUOTA_KAFKA_TOPIC = getEnvVarWithAssert('QUOTA_KAFKA_TOPIC');
const QUOTA_TABLE_NAME = getEnvVarWithAssert('QUOTA_TABLE_NAME');

const dbClient = new DynamoDBClient({});
const logger = new KasLambdaLogger('quota-sync', LogLevel.TRACE);
const objectValidator = new ObjectValidator<KafkaQuotaEventValue>('KafkaQuotaEventValue');

/**
 * Consume the Quota kafka topic and save the quotas in dynamodB
 * @param event
 */
export const handler: Handler<MSKEvent, void> = async (event, context) => {
  logger.setRequestContext(context);
  const quotas = parseQuotasFromEvent(event);

  logger.setCorrelationId(context.awsRequestId);
  //filter out duplicate quotas and only contain the newest entries
  const filteredQuotas = quotas.reduce((accumulator: Record<string, CoraQuota>, currentObject: CoraQuota) => {
    const { quota_id_without_month, quota_month, last_modified_at } = currentObject;

    //filter uses quota month to prevent false deletion of non-duplicates
    const concatenatedUniqueQuotaID = `${quota_id_without_month}-${quota_month}`;

    if (
      //eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      !accumulator[concatenatedUniqueQuotaID] ||
      last_modified_at > accumulator[concatenatedUniqueQuotaID].last_modified_at
    ) {
      accumulator[concatenatedUniqueQuotaID] = currentObject;
    }

    return accumulator;
  }, {});

  const finalQuotas = Object.values(filteredQuotas);

  logger.log(LogLevel.DEBUG, `Received ${quotas.length} quotas from SG1`);
  logger.log(LogLevel.DEBUG, `After filtering, saving ${finalQuotas.length} quotas to DynamoDB`);
  await writeQuotasToDynamoDB(finalQuotas);
};

/**
 * This function performs three primary tasks:
 * 1. Parse Event: From the original Kafka event, only the relevant properties are retained.
 *
 * @param event
 */
function parseQuotasFromEvent(event: MSKEvent): CoraQuota[] {
  return Object.entries(event.records)
    .filter(([key]) => key.includes(QUOTA_KAFKA_TOPIC))
    .flatMap(([, value]) => value.map(parseRecord).flat())
    .filter(Boolean) as CoraQuota[];
}

/**
 *
 * Null values should not exist because a quota is not deleted, it can only be consumed and set to 0.
 *
 * @param record
 */
function parseRecord(record: MSKRecord): CoraQuota[] | undefined {
  try {
    logger.setCorrelationId(correlationHeader(record));
    const { value } = record;

    if (value) {
      const rawQuotaEvent = JSON.parse(Buffer.from(value, 'base64').toString('utf8')) as unknown;

      //validate event and only process valid messages
      const [body, validation_errors] = objectValidator.validate(rawQuotaEvent);
      if (body === null) {
        logger.log(LogLevel.WARN, 'Object validation failed, skipping object', {
          data: { error: validation_errors, object: rawQuotaEvent },
        });
        return undefined;
      }
      const coraQuotas: CoraQuota[] = [];

      body.model_type_groups.forEach((mtg) => {
        mtg.model_type_codes.forEach((mtc) => {
          coraQuotas.push({
            consumed_new_car_order_ids: mtg.consumed_new_car_order_ids,
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            created_at: body.created_at ?? new Date().toISOString(),
            created_by: 'unknown',
            dealer_number: body.dealer_number,
            importer_number: body.importer_number,
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            last_modified_at: body.last_modified_at ?? body.created_at ?? new Date().toISOString(), //for sorting later
            last_modified_by: body.last_modified_by,
            model_type: mtc,
            model_year: mtg.model_year,
            quota_consumed: mtg.quota_consumed,
            quota_count: body.quota_count,
            quota_id: body.quota_id,
            quota_id_without_month: `QA-${body.importer_number}-${body.dealer_number}-${mtc}-${mtg.model_year}`,
            quota_month: body.quota_month,
            quota_open: body.quota_open,
          });
        });
      });

      return coraQuotas;
    } else {
      // this case should not exist
      logger.log(LogLevel.ERROR, 'Received a record without value', { data: record });
      return undefined;
    }
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Could not parse quota', { data: { error: e, object: record } });
    return undefined;
  }
}

async function writeQuotasToDynamoDB(quotas: CoraQuota[]): Promise<void> {
  if (quotas.length > 0) {
    const quotasToPut = splitIntoBatches(quotas);
    for (const batch of quotasToPut) {
      const batchParams: BatchWriteCommandInput = {
        RequestItems: {
          [QUOTA_TABLE_NAME]: batch.map((quota) => ({
            PutRequest: { Item: quota },
          })),
        },
      };

      try {
        await dbClient.send(new BatchWriteCommand(batchParams));
        logger.log(LogLevel.INFO, `Successfully updated the table ${QUOTA_TABLE_NAME}`);
      } catch (err) {
        batch.forEach((quotaBatch) => {
          logger.log(LogLevel.ERROR, 'Failed to save Quota', { data: quotaBatch });
        });
        throw new GeneralError({
          message: 'Failed to execute writeRequests',
          causedBy: err,
        });
      }
    }
  }
}
