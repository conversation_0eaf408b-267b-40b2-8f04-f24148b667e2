import { InvokeCommand, LambdaClient, LogType } from '@aws-sdk/client-lambda';
import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  APIGatewayProxyEventPathParameters,
  APIGatewayProxyEventQueryStringParameters,
  SQSEvent,
  SQSRecord,
  MSKEvent,
  MSKRecord,
} from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { Constants } from '../../lib/utils/constants';
import { NativeAttributeValue } from '@aws-sdk/util-dynamodb';
import {
  BatchWriteCommand,
  BatchWriteCommandInput,
  GetCommand,
  GetCommandInput,
  PutCommand,
  PutCommandOutput,
  QueryCommand,
} from '@aws-sdk/lib-dynamodb';
import { devProps } from '../../lib/context';
import { KasApplication } from '../../lib/types/kas-auth-types';
import { DataSource } from 'typeorm';
import { Class } from 'type-fest';
import { RdsSecret, secretCache } from './secret-cache';
import { GetParameterCommand, SSMClient } from '@aws-sdk/client-ssm';
import {
  DeleteScheduleCommand,
  DeleteScheduleCommandInput,
  GetScheduleCommand,
  GetScheduleCommandInput,
  GetScheduleCommandOutput,
  SchedulerClient,
} from '@aws-sdk/client-scheduler';
import { CoraOrgRelModel } from '../../lib/types/boss-org-types';
import { CoraMdDealer, CoraMdImporter, CoraMdOrderType, CoraMdShippingCode } from '../../lib/types/masterdata-types';
import { SubTransaction } from '../../lib/types/notification-center-types';
import { InboundEventHandlerEvent, OneVmsSourceSystemKey } from '../../lib/types/process-steering-types';
import { KafkaObjTyp } from '../backend/export-nco/types';

const client = new LambdaClient({ region: Constants.DEFAULT_REGION });
const dynamoDb = new DynamoDBClient({ region: Constants.DEFAULT_REGION });
const ssmClient = new SSMClient({ region: Constants.DEFAULT_REGION });
const schedulerEBClient = new SchedulerClient({ region: Constants.DEFAULT_REGION });

export const invokeApiGwLambda = async (
  funcName: string,
  payload: APIGatewayProxyEvent,
  displayLogs?: boolean,
): Promise<APIGatewayProxyResult> => {
  return await invokeGenericLambda<APIGatewayProxyResult>(funcName, payload, displayLogs);
};

export const invokeGenericLambda = async <T>(funcName: string, payload: unknown, displayLogs?: boolean): Promise<T> => {
  const command = new InvokeCommand({
    FunctionName: funcName,
    Payload: JSON.stringify(payload),
    LogType: LogType.Tail,
  });

  const { Payload, LogResult } = await client.send(command);
  if (!Payload) throw new Error('Lambda did not return anything');

  const result = JSON.parse(Buffer.from(Payload).toString()) as T;
  const logs = LogResult ? Buffer.from(LogResult, 'base64').toString() : null;
  if (displayLogs) {
    console.log('Execution logs:\n', logs);
    console.log('Result: \n', result);
  }
  return result;
};

export interface CreateApiGwEventInput {
  body?: Record<string, unknown>;
  path?: string;
  pathParameters?: APIGatewayProxyEventPathParameters;
  queryStringParameters?: APIGatewayProxyEventQueryStringParameters;
  userOrgId?: string;
  kasApplications?: Record<string, KasApplication[]>;
}

export const createApiGwEvent = (input: CreateApiGwEventInput): APIGatewayProxyEvent => {
  return {
    body: input.body ? JSON.stringify(input.body) : null,
    headers: {},
    multiValueHeaders: {},
    httpMethod: '',
    path: input.path ?? '',
    pathParameters: input.pathParameters ?? null,
    queryStringParameters: input.queryStringParameters ?? null,
    multiValueQueryStringParameters: {},
    stageVariables: {},
    resource: '',
    requestContext: {
      authorizer: {
        userAttributes: JSON.stringify({
          organizationId: input.userOrgId,
          kasApplications: input.kasApplications ?? [],
        }),
      },
      protocol: '',
      httpMethod: '',
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        clientCert: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '',
        user: null,
        userAgent: null,
        userArn: null,
      },
      path: '',
      requestTimeEpoch: 0,
      resourceId: '',
      resourcePath: '',
      domainName: '',
      requestId: '',
      routeKey: '',
      stage: '',
      accountId: '',
      apiId: '',
      domainPrefix: '',
    },
    isBase64Encoded: false,
  };
};

export const createSqsEvent = (messages: unknown[]): SQSEvent => {
  return {
    Records: messages.map((message) => ({
      messageId: uuidv4(),
      body: JSON.stringify(message),
    })) as SQSRecord[],
  };
};

export interface DynamoPrepareProps {
  tableName: string;
  objs: unknown[];
}

export interface DynamoCleanupProps {
  tableName: string;
  pks: Record<string, NativeAttributeValue>[];
}

export interface DynamoUpdateProps {
  tableName: string;
  tablePk: string;
  objs: Record<string, unknown>[];
}

//insert all given objs into tableName
export const prepareDynamodb = async (props: DynamoPrepareProps[]): Promise<unknown> => {
  for (const prop of props) {
    const params: BatchWriteCommandInput = {
      RequestItems: {},
    };
    const fullTableName = prop.tableName;
    params.RequestItems![fullTableName] = prop.objs.map((sc) => ({
      PutRequest: {
        Item: sc as Record<string, unknown>,
      },
    }));
    await dynamoDb.send(new BatchWriteCommand(params));
  }
  return;
};

export const cleanupDynamodb = async (props: DynamoCleanupProps[]): Promise<unknown> => {
  for (const prop of props) {
    const params: BatchWriteCommandInput = {
      RequestItems: {},
    };
    const fullTableName = prop.tableName;
    params.RequestItems![fullTableName] = prop.pks.map((pk) => ({
      DeleteRequest: {
        Key: pk, //eg { pk_ppn_id: uuid, parent_ppn_id: uuid }
      },
    }));
    await dynamoDb.send(new BatchWriteCommand(params));
  }
  return;
};

//updates all given objs in tableName
export const updateItemsInDynamodb = async (props: DynamoUpdateProps): Promise<PutCommandOutput[]> => {
  const results: PutCommandOutput[] = [];
  for (const item of props.objs) {
    const updateCommandParams = {
      TableName: props.tableName,
      Key: {
        [props.tablePk]: item[props.tablePk] as string,
      },
      Item: item,
    };
    const res = await dynamoDb.send(new PutCommand(updateCommandParams));
    results.push(res);
  }
  return results;
};

export const buildLambdaArn = (funcName: string): string => {
  return `arn:aws:lambda:${Constants.DEFAULT_REGION}:${devProps.env.account}:function:${Constants.APPLICATION_SHORT_NAME}-dev-${funcName}`;
};

export const getItemInDynamoDb = async (
  tableName: string,
  key: Record<string, NativeAttributeValue>,
): Promise<Record<string, unknown> | undefined> => {
  const getCommand: GetCommandInput = {
    Key: key,
    TableName: tableName,
  };

  return (await dynamoDb.send(new GetCommand(getCommand))).Item;
};

export async function initDataSourceForIntTest(entities: Class[]): Promise<DataSource> {
  try {
    const aurora_writer_secret_arn = await fetchSSMParameter(
      `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/dev/${Constants.SSM_AURORA_ADMIN_SECRET_ARN_PARAM}`,
    );
    secretCache.initCache(aurora_writer_secret_arn);
    const rdsSecret = await secretCache.getSecret<RdsSecret>(aurora_writer_secret_arn).catch((error) => {
      console.log(error);
      throw error;
    });
    return new DataSource({
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: rdsSecret.username,
      password: rdsSecret.password,
      database: rdsSecret.dbname,
      synchronize: true,
      ssl: {
        rejectUnauthorized: false,
      },
      logging: ['error'],
      entities: entities,
    }).initialize();
  } catch (error) {
    console.error('Failed to initialize data source:', error);
    throw error;
  }
}

async function fetchSSMParameter(parameterName: string): Promise<string> {
  try {
    const command = new GetParameterCommand({
      Name: parameterName,
      WithDecryption: true,
    });
    const response = await ssmClient.send(command);
    const parameterValue = response.Parameter?.Value;
    if (parameterValue) {
      return parameterValue;
    } else {
      throw new Error(`Could not find the parameter ${parameterName}`);
    }
  } catch (error) {
    console.error('Failed to fetch SSM Parameter:', error);
    throw error;
  }
}

export async function getEventbridgeSchedule(scheduleName: string): Promise<GetScheduleCommandOutput | undefined> {
  const getInput: GetScheduleCommandInput = {
    Name: scheduleName,
  };
  try {
    return await schedulerEBClient.send(new GetScheduleCommand(getInput));
  } catch (error) {
    console.error('Error getting Schedule', error);
    return undefined;
  }
}

export async function deleteEventbridgeSchedules(scheduleNames: string[]): Promise<boolean> {
  try {
    for (const name of scheduleNames) {
      const deleteInput: DeleteScheduleCommandInput = {
        Name: name,
      };
      await schedulerEBClient.send(new DeleteScheduleCommand(deleteInput));
    }
    return true;
  } catch (error) {
    console.error('Error getting Schedule', error);
    return false;
  }
}

export function addParentRelation(orgs: CoraOrgRelModel[], parentId: string): CoraOrgRelModel[] {
  return orgs.map((org) => ({ ...org, parent_ppn_id: parentId }));
}

interface StandardOrgs {
  importer: {
    ppn_id: string;
    md_org: CoraMdImporter;
  };
  importer02: {
    ppn_id: string;
    md_org: CoraMdImporter;
  };
  dealerGroup: {
    ppn_id: string;
  };
  dealer01: {
    ppn_id: string;
    md_org: CoraMdDealer;
  };
  dealer02: {
    ppn_id: string;
    md_org: CoraMdDealer;
  };
  dealer03: {
    ppn_id: string;
    md_org: CoraMdDealer;
  };
  allOrgsOrgId: string;
}
/**
 * Importer01 has dealer01/02
 * Importer02 has dealer 03
 */
export function createStandartOrgRelPattern(test_name: string): {
  ids: StandardOrgs;
  orgRels: CoraOrgRelModel[];
  scs: {
    scBlacklistDeny: CoraMdShippingCode;
    scBlacklistAllow: CoraMdShippingCode;
    scWhitelistDeny: CoraMdShippingCode;
    scWhitelistAllow: CoraMdShippingCode;
  };
  ots: {
    otBlacklistAllow: CoraMdOrderType;
    otBlacklistDeny: CoraMdOrderType;
    otWhitelistAllow: CoraMdOrderType;
    otWhitelistDeny: CoraMdOrderType;
    otCustomerRel: CoraMdOrderType;
    otNoCustomerRel: CoraMdOrderType;
  };
} {
  const ids = {
    allOrgsOrgId: `uuid-${test_name}-all-orgs`,
    importer01OrgId: `uuid-${test_name}-importer-01`,
    importer02OrgId: `uuid-${test_name}-importer-02`,
    dealerGroupOrgId: `uuid-${test_name}-dealer-group-01`,
    dealer01OrgId: `uuid-${test_name}-dealer-01`,
    dealer02OrgId: `uuid-${test_name}-dealer-02`,
    dealer03OrgId: `uuid-${test_name}-dealer-03`,
  };

  //masterdata dealer and importer test data
  const mdImporter01 = {
    pk_importer_number: 'ItNcoImp01' + test_name,
    code: 'IT',
    display_name: 'IntegrationTest - Importer 01',
    port_codes: ['ItNcoPc4'],
    modified_at: '2023-10-04T14:16:30.079Z',
    modified_by: 'integration_test',
  };

  const mdImporter02 = {
    pk_importer_number: 'ItNcoImp02' + test_name,
    code: 'IT',
    display_name: 'IntegrationTest - Importer 02',
    port_codes: ['ItNcoPc4'],
    modified_at: '2023-10-04T14:16:30.079Z',
    modified_by: 'integration_test',
  };

  const mdDealer01 = {
    pk_importer_number: mdImporter01.pk_importer_number,
    sk_dealer_number: 'ItNcoDlr01' + test_name,
    display_name: 'IntegrationTest - Dealer 01',
    standard_port_code: 'ItNcoPc1',
    alternative_port_codes: ['ItNcoPc2', 'ItNcoPc3'],
    modified_at: '2023-08-01T12:13:06.880Z',
    modified_by: 'integration_test',
  };
  const mdDealer02 = {
    pk_importer_number: mdImporter01.pk_importer_number,
    sk_dealer_number: 'ItNcoDlr02' + test_name,
    display_name: 'IntegrationTest - Dealer 02',
    standard_port_code: 'ItNcoPc1',
    alternative_port_codes: ['ItNcoPc2', 'ItNcoPc3'],
    modified_at: '2023-08-01T12:13:06.880Z',
    modified_by: 'integration_test',
  };
  const mdDealer03 = {
    pk_importer_number: mdImporter02.pk_importer_number,
    sk_dealer_number: 'ItNcoDlr03' + test_name,
    display_name: 'IntegrationTest - Dealer 03',
    standard_port_code: 'ItNcoPc1',
    alternative_port_codes: ['ItNcoPc2', 'ItNcoPc3'],
    modified_at: '2023-08-01T12:13:06.880Z',
    modified_by: 'integration_test',
  };

  //shipping code test data
  const scBlacklistDeny = {
    pk_shipping_code: 'ItScBlackDeny' + test_name,
    description: 'Integration Test Blackist Deny',
    imp_is_blacklist: true,
    importers: [mdImporter01.pk_importer_number, mdImporter02.pk_importer_number],
    dlr_is_blacklist: true,
    dealers: [mdDealer01.sk_dealer_number, mdDealer02.sk_dealer_number, mdDealer03.sk_dealer_number],
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const scBlacklistAllow: CoraMdShippingCode = {
    pk_shipping_code: 'ItScBlackAllow' + test_name,
    description: 'Integration Test Blackist Allow',
    imp_is_blacklist: true,
    importers: ['ItNcoImpOther'],
    dlr_is_blacklist: true,
    dealers: ['ItNcoDlrOther'],
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const scWhitelistDeny = {
    pk_shipping_code: 'ItScWhiteDeny' + test_name,
    description: 'Integration Test Whitelist Deny',
    imp_is_blacklist: false,
    importers: ['ItNcoImpOther'],
    dlr_is_blacklist: false,
    dealers: ['ItNcoDlrOther'],
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const scWhitelistAllow = {
    pk_shipping_code: 'ItScWhiteAllow' + test_name,
    description: 'Integration Test Whitelist Allow',
    imp_is_blacklist: false,
    importers: [mdImporter01.pk_importer_number, mdImporter02.pk_importer_number],
    dlr_is_blacklist: false,
    dealers: [mdDealer01.sk_dealer_number, mdDealer02.sk_dealer_number, mdDealer03.sk_dealer_number],
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  //order type test data
  const otBlacklistDeny = {
    pk_order_type: 'ItOtBlackDeny' + test_name,
    description: 'Integration Test Blacklist Deny',
    imp_is_blacklist: true,
    importers: [mdImporter01.pk_importer_number, mdImporter02.pk_importer_number],
    dlr_is_visible: false,
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const otBlacklistAllow = {
    pk_order_type: 'ItOtBlackAllow' + test_name,
    description: 'Integration Test Blacklist Allow',
    imp_is_blacklist: true,
    importers: ['ItNcoImpOther'],
    dlr_is_visible: true,
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const otWhitelistDeny = {
    pk_order_type: 'ItOtWhiteDeny' + test_name,
    description: 'Integration Test Whitelist Deny',
    imp_is_blacklist: false,
    importers: ['ItNcoImpOther'],
    dlr_is_visible: false,
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const otWhitelistAllow = {
    pk_order_type: 'ItOtWhiteAllow' + test_name,
    description: 'Integration Test Whitelist Allow',
    imp_is_blacklist: false,
    importers: [mdImporter01.pk_importer_number, mdImporter02.pk_importer_number],
    dlr_is_visible: true,
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const otCustomerRel = {
    pk_order_type: 'ItOTCustomerRel' + test_name,
    description: 'Integration Test Customer Related',
    imp_is_blacklist: true,
    importers: ['ItNcoImpOther'],
    dlr_is_visible: true,
    is_customer_related: true,
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  const otNoCustomerRel = {
    pk_order_type: 'ItOTNoCustomerRel' + test_name,
    description: 'Integration Test No Customer Related',
    imp_is_blacklist: true,
    importers: ['ItNcoImpOther'],
    dlr_is_visible: true,
    is_customer_related: false,
    is_deactivated: false,
    created_at: new Date().toISOString(),
    created_by: 'integration_test',
    modified_at: new Date().toISOString(),
    modified_by: 'integration_test',
  };

  let orgRels: CoraOrgRelModel[] = [];
  const orgRelImporters: CoraOrgRelModel[] = [
    {
      pk_ppn_id: ids.importer01OrgId,
      parent_ppn_id: ids.importer01OrgId,
      dealer_number: mdImporter01.pk_importer_number,
      display_name: mdImporter01.display_name,
      importer_number: mdImporter01.pk_importer_number,
      is_deactivated: false,
      ppn_status: 'OPERATIVE',
      is_relevant_for_order_create: true,
    },
    {
      pk_ppn_id: ids.importer02OrgId,
      parent_ppn_id: ids.importer02OrgId,
      dealer_number: mdImporter02.pk_importer_number,
      display_name: mdImporter02.display_name,
      importer_number: mdImporter02.pk_importer_number,
      is_deactivated: false,
      ppn_status: 'OPERATIVE',
      is_relevant_for_order_create: true,
    },
  ];
  orgRels = orgRels.concat(orgRelImporters);
  const orgRelDealerGroups: CoraOrgRelModel[] = [
    {
      pk_ppn_id: ids.dealerGroupOrgId,
      parent_ppn_id: ids.dealerGroupOrgId,
      display_name: 'IntegrationTest - Dealer Group 01',
      is_deactivated: false,
      ppn_status: 'OPERATIVE',
      is_relevant_for_order_create: true,
    },
  ];
  orgRels = orgRels.concat(orgRelDealerGroups);
  const orgRelDealers: CoraOrgRelModel[] = [
    {
      pk_ppn_id: ids.dealer01OrgId,
      parent_ppn_id: ids.dealer01OrgId,
      dealer_number: mdDealer01.sk_dealer_number,
      display_name: mdDealer01.display_name,
      importer_number: mdImporter01.pk_importer_number,
      is_deactivated: false,
      ppn_status: 'OPERATIVE',
      is_relevant_for_order_create: true,
    },
    {
      pk_ppn_id: ids.dealer02OrgId,
      parent_ppn_id: ids.dealer02OrgId,
      dealer_number: mdDealer02.sk_dealer_number,
      display_name: mdDealer02.display_name,
      importer_number: mdImporter01.pk_importer_number,
      is_deactivated: false,
      ppn_status: 'OPERATIVE',
      is_relevant_for_order_create: true,
    },
    {
      pk_ppn_id: ids.dealer03OrgId,
      parent_ppn_id: ids.dealer03OrgId,
      dealer_number: mdDealer03.sk_dealer_number,
      display_name: mdDealer03.display_name,
      importer_number: mdImporter02.pk_importer_number,
      is_deactivated: false,
      ppn_status: 'OPERATIVE',
      is_relevant_for_order_create: true,
    },
  ];
  orgRels = orgRels.concat(orgRelDealers);
  orgRels = orgRels.concat(addParentRelation(orgRels, ids.allOrgsOrgId));
  orgRels = orgRels.concat(addParentRelation([orgRelDealers[0], orgRelDealers[1]], ids.importer01OrgId));
  // orgRels = orgRels.concat(addParentRelation([orgRelDealers[0], orgRelDealers[1]], ids.dealerGroupOrgId));
  orgRels = orgRels.concat(addParentRelation([orgRelDealers[2]], ids.importer02OrgId));
  // orgRels = orgRels.concat(addParentRelation(orgRelDealerGroups, ids.importer01OrgId));
  return {
    ids: {
      importer: { ppn_id: ids.importer01OrgId, md_org: mdImporter01 },
      importer02: { ppn_id: ids.importer02OrgId, md_org: mdImporter02 },
      dealerGroup: { ppn_id: ids.dealerGroupOrgId },
      dealer01: { ppn_id: ids.dealer01OrgId, md_org: mdDealer01 },
      dealer02: { ppn_id: ids.dealer02OrgId, md_org: mdDealer02 },
      dealer03: { ppn_id: ids.dealer03OrgId, md_org: mdDealer03 },
      allOrgsOrgId: ids.allOrgsOrgId,
    },
    scs: {
      scBlacklistDeny,
      scBlacklistAllow,
      scWhitelistDeny,
      scWhitelistAllow,
    },
    ots: {
      otBlacklistAllow,
      otBlacklistDeny,
      otWhitelistAllow,
      otWhitelistDeny,
      otCustomerRel,
      otNoCustomerRel,
    },
    orgRels: orgRels,
  };
}

export async function pollNotifications(
  tableName: string,
  transactionId: string,
  maxWaitTimeMillis: number = 10000,
  intervalMillis: number = 1000,
): Promise<SubTransaction[]> {
  const start = Date.now();
  await new Promise<void>((resolve) => setTimeout(resolve, 1000));

  // Poll until timeout or until items are found
  while (Date.now() - start < maxWaitTimeMillis) {
    const items = await querySubTxTableByTransactionId(tableName, transactionId);
    if (items.length > 0) {
      return items;
    }
    await new Promise<void>((resolve) => setTimeout(resolve, intervalMillis));
  }
  return [];
}

async function querySubTxTableByTransactionId(tableName: string, transactionId: string): Promise<SubTransaction[]> {
  try {
    const params = {
      TableName: tableName,
      KeyConditionExpression: 'transaction_id = :txId',
      ExpressionAttributeValues: {
        ':txId': transactionId,
      },
    };
    const command = new QueryCommand(params);
    const result = await dynamoDb.send(command);
    return (result.Items ?? []) as SubTransaction[];
  } catch (error) {
    console.error('Failed to fetch Notifications:', error);
    throw error;
  }
}

export function createInboundEvent(
  defaultEvent: Omit<InboundEventHandlerEvent, 'transaction_id' | 'sub_transaction_id' | 'action_at'>,
): InboundEventHandlerEvent {
  return {
    ...defaultEvent,
    transaction_id: uuidv4(),
    sub_transaction_id: uuidv4(),
    action_at: new Date().toISOString(),
  };
}

export function createKafkaEvent(events: MSKRecord[]): MSKEvent {
  return {
    eventSource: 'aws:kafka',
    eventSourceArn: 'aws:lambda:dummy:arn',
    bootstrapServers: 'localhost:9092',
    records: {
      topic: events,
    },
  };
}

/* eslint @typescript-eslint/no-explicit-any: 0 */
export function createKafkaRecord(
  sourceSystem: OneVmsSourceSystemKey,
  topic: string,
  value: any,
  ce_type: string = `${KafkaObjTyp.NCO}.${sourceSystem}`,
  key?: string,
): MSKRecord {
  return {
    topic,
    partition: 0,
    offset: 0,
    timestamp: Date.now(),
    timestampType: 'CREATE_TIME',
    key: Buffer.from(key ?? 'test-key').toString('base64'),
    value: Buffer.from(JSON.stringify(value)).toString('base64'),
    headers: [
      {
        ce_type: Array.from(Buffer.from(ce_type)),
      },
    ],
  };
}
