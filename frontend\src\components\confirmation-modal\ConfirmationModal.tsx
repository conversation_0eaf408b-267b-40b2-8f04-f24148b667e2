import { PButton, PButtonGroup, PModal } from '@porsche-design-system/components-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

export interface ConfirmationModalProps {
  isOpen: boolean;
  message: string | JSX.Element;
  title: string;
  onClose: () => void;
  handleOk: () => void;
  handleCancel: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = (props: ConfirmationModalProps) => {
  const { t } = useTranslation();

  return (
    <PModal
      data-e2e="ConfirmationModal"
      open={props.isOpen}
      onDismiss={(): void => props.handleCancel()}
      dismissButton={false}
      heading={props.title}
      className="confirmation-modal"
    >
      {props.message}
      <PButtonGroup className="footer">
        <PButton
          data-e2e="accept"
          variant="primary"
          title={t('confirm_action') ?? ''}
          aria-label={t('confirm_action') ?? ''}
          onClick={(): void => props.handleOk()}
        >
          {t('confirm_action')}
        </PButton>
        <PButton
          data-e2e="cancel"
          variant="tertiary"
          title={t('cancel') ?? ''}
          aria-label={t('cancel') ?? ''}
          onClick={(): void => props.handleCancel()}
        >
          {t('cancel')}
        </PButton>
      </PButtonGroup>
    </PModal>
  );
};

export default ConfirmationModal;
