#!/bin/bash
set -e
AWS_REGION="eu-west-1"
SECRET_NAME="cora-aurora-admin-secret"

# Use STAGE environment variable from GitLab CI, default to 'dev' if not set
STAGE=${STAGE:-dev}
CLUSTER_ARN_PARAM="/cora/${STAGE}/Aurora/auroraClusterArn"

echo "Using stage: $STAGE"

# Extract ARNs and DB name from AWS
CLUSTER_ARN=$(aws ssm get-parameter --name "$CLUSTER_ARN_PARAM" --query 'Parameter.Value' --output text --region "$AWS_REGION")
SECRET_ARN=$(aws secretsmanager describe-secret --secret-id "$SECRET_NAME" --query 'ARN' --output text --region "$AWS_REGION")
SECRET_VALUE=$(aws secretsmanager get-secret-value --secret-id "$SECRET_NAME" --query 'SecretString' --output text --region "$AWS_REGION")
export DB_NAME=$(echo "$SECRET_VALUE" | jq -r '.dbname')

export SECRET_ARN
export CLUSTER_ARN
export AWS_REGION
export IS_LOCAL=0

echo "Aurora Data API ARNs and DB name exported for migration run."


# Run the migration command
npm run migration:run

echo "Migration run completed."