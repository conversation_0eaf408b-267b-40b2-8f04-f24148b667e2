import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { Kas<PERSON>ambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { AuthorizedDealer, CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { CoraMdImporter } from '../../../../lib/types/masterdata-types';
import { Constants } from '../../../../lib/utils/constants';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { getPpnId, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert, getImporterByImporterNumbers } from '../../../utils/utils';
import { extractDlrsAndImpsFromOrgs } from '../../../utils/validation-helpers';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const tableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const impTableName = getEnvVarWithAssert('TABLE_NAME_IMPORTER');

const fetchAuthorizedDealersFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  if (!tableName) {
    throw new Error('Table name not defined');
  }

  //get the org id of the user from auth context
  const ppnId = getPpnId({ event: event }, logger);
  if (!ppnId) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: event });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  const includeDeactivated = event.queryStringParameters
    ? event.queryStringParameters['include_deactivated'] === 'true'
    : false;

  //get all Orgs the user has access to
  const cmd = new QueryCommand({
    TableName: tableName,
    IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
    KeyConditionExpression: `${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk} = :value`,
    ExpressionAttributeValues: { ':value': ppnId },
  });

  try {
    logger.log(LogLevel.DEBUG, 'QueryCommand', { data: cmd });
    const dynamoResult = await dynamoDb.send(cmd);
    if (dynamoResult.Items) {
      const activeUserOrgs = (dynamoResult.Items as CoraOrgRelModel[]).filter((org) =>
        includeDeactivated ? true : !org.is_deactivated,
      );
      logger.log(LogLevel.DEBUG, 'Response active user orgs', { data: activeUserOrgs });
      const { imps, dlrs } = extractDlrsAndImpsFromOrgs({ orgs: activeUserOrgs }, logger);
      logger.log(LogLevel.INFO, 'imp,dlrs', { data: { imps, dlrs } });
      const impNrs = imps.map((imp) => imp.importer_number).filter((impNr) => impNr != undefined);
      logger.log(LogLevel.INFO, 'Importers', { data: impNrs });
      // Extract unique dealer numbers (deduplicated)
      const dlrNrs = dlrs.map((dlr) => dlr.dealer_number).filter((dlrNr) => dlrNr !== undefined);

      logger.log(LogLevel.INFO, 'Dealers', { data: dlrNrs });

      // Deduplicate dealers based on dealer_number
      const seenDlrs = new Set<string>();
      const uniqueDlrs = dlrs.filter((dlr) => {
        if (!seenDlrs.has(dlr.dealer_number!)) {
          seenDlrs.add(dlr.dealer_number!);
          return true;
        }
        return false;
      });

      // Extract importer numbers ,may contain duplicates
      const allImpNrs = uniqueDlrs
        .map((dlr) => dlr.importer_number)
        .filter((impNr): impNr is string => impNr !== undefined);

      // Deduplicate importer numbers before using them in the query so we can extract the display names
      const uniqueImpNrs = [...new Set(allImpNrs)];
      logger.log(LogLevel.DEBUG, 'Importer Numbers for Display Names', {
        data: uniqueImpNrs,
      });
      let allImporters: CoraMdImporter[] = [];
      // in case there are no importers to fetch, skip the query
      if (uniqueImpNrs.length > 0) {
        allImporters = await getImporterByImporterNumbers(
          { dynamoDb, impTableName, importerKeys: uniqueImpNrs },
          logger,
        );
      }
      logger.log(LogLevel.DEBUG, 'All Fetched Importers', { data: allImporters });
      // Get operative importers and create a mapping of importer numbers to display names
      const operativeImportersMap = allImporters
        .filter((imp) => imp.ppn_status === 'OPERATIVE')
        .reduce<Record<string, string>>((acc, imp) => {
          acc[imp.pk_importer_number] = imp.display_name;
          return acc;
        }, {});

      logger.log(LogLevel.INFO, 'Operative Importers Map', { data: operativeImportersMap });

      // Filter dealers with operative importers and map to response format
      const dlrResponse: AuthorizedDealer[] = uniqueDlrs
        .filter((dlr) => dlr.importer_number! in operativeImportersMap) // Only keep dealers with operative importers
        .map((dlr) => ({
          dealer_number: dlr.dealer_number!,
          display_name: dlr.display_name,
          importer_number: dlr.importer_number!,
          importer_display_name: operativeImportersMap[dlr.importer_number!] || 'Unknown Importer', // Default value if missing
          dealer_ppn_status: dlr.ppn_status,
          //if user has access to importer of dealer, role = 'Importer'. Otherwise, role = 'Dealer
          role: imps.find((imp) => imp.importer_number === dlr.importer_number) ? 'Importer' : 'Dealer',
        }));
      return sendSuccess({ body: dlrResponse, reqHeaders: event.headers }, logger);
    } else {
      return sendSuccess({ body: [], reqHeaders: event.headers }, logger);
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, `Failed to query dynamodb`, { data: { error: error, cmd: cmd } });
    throw error;
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-authorized-dealers', LogLevel.TRACE)(event, context, fetchAuthorizedDealersFunc);
