import { Buffer } from 'buffer';
import { BOSS_APP_NAME } from '../config/constants';
import { User } from '../store/slices/UserMenuSlice';
import { StageConfig } from '../store/types';

export interface KasUserAttributes {
  username: string; //'xyzxyz';
  firstName: string; //'Max';
  lastName: string; //'Mustermann';
  porschePartnerNo: string; //'9500090';
  kasApplications: Record<
    string, //'tyd'
    KasRole[]
  >;
}
export interface KasRole {
  role: string; //'power_master_user'
  modelTypeVisibility: string; //'DLR'
}

export function getCookie(name: string): string {
  const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
  if (match) return match[2];
  else return '';
}

function parseCookie(token: string): KasUserAttributes | undefined {
  if (token === '' && window.location.hostname === 'localhost') {
    return {
      username: 'xyzxyz',
      firstName: 'Test',
      lastName: 'User',
      porschePartnerNo: '9500090',
      kasApplications: {
        [BOSS_APP_NAME]: [
          {
            role: 'ppn_approle_boss_dev_basic_application_role',
            modelTypeVisibility: 'IMP',
          },
        ],
      },
    };
  }
  try {
    return JSON.parse(Buffer.from(token, 'base64').toString('utf-8')) as KasUserAttributes;
  } catch (error) {
    console.error('Error parsing UserAttribute Cookie', error);
    console.error('Cookie content:', token);
    return undefined;
  }
}

export const loadUserFromToken = (config: StageConfig): User => {
  const _cookieName = 'KasUserAttributes-' + config.stage;
  const token = parseCookie(getCookie(_cookieName));
  if (!token) {
    console.error('Cookie could not be parsed, cookie name:', _cookieName);
  }

  return {
    username: token?.username ?? 'unknown',
    lastName: token?.lastName ?? 'unknown',
    firstName: token?.firstName ?? 'unknown',
    porschePartnerNo: token?.porschePartnerNo ?? 'unknown',
    expires: Date.now() / 1000 + 900, // set to 15 min
  };
};
