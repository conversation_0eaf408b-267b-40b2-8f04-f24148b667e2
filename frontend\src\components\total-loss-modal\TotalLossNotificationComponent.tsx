import React from 'react';
import { PInlineNotification } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { CarOrderInList, OrderActionType } from '../../store/types';
import { OneVmsEventKey } from '../../../../infrastructure/lib/types/process-steering-types';

interface TotalLossNotificationProps {
  actionType: OrderActionType | OneVmsEventKey;
  orders: CarOrderInList[];
  deadlineDate: string;
}

const TotalLossNotificationComponent: React.FC<TotalLossNotificationProps> = ({ actionType, orders, deadlineDate }) => {
  const { t } = useTranslation();

  return (
    <>
      <PInlineNotification dismissButton={false}>
        {t(
          actionType === OneVmsEventKey.TOTAL_LOSS_REVOKE && orders.length > 0
            ? `${actionType}_info_pl`
            : `${actionType}_info`,
          { deadlineDate },
        )}
      </PInlineNotification>
      <br />
      <br />
    </>
  );
};

export default TotalLossNotificationComponent;
