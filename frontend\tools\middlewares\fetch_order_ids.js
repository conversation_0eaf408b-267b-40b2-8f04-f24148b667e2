const fs = require('fs');
const path = require('path');
const prefix = '/new-car-order/new-car-order-ids';

module.exports = function (req, res, next) {
  if (req.method === 'GET' && req.url === prefix && !!req.query) {
    fs.readFile(path.join(__dirname, '../db.json'), 'utf8', (err, data) => {
      if (err) {
        return res.status(500).jsonp({ message: 'Internal Server Error' });
      }

      try {
        let json = JSON.parse(data)['new-car-order'];
        const { importer_number, dealer_number } = req.query;

        // Filter the data based on importer_number or dealer_number
        const filteredData = json.filter(
          (item) =>
            importer_number &&
            item.importer_number === importer_number &&
            dealer_number &&
            item.dealer_number === dealer_number,
        );
        console.log(filteredData.map((item) => item.pk_new_car_order_id));
        if (filteredData.length > 0) {
          // Extract only the IDs from the filtered data
          const newCarOrderIds = filteredData.map((item) => item.pk_new_car_order_id);
          return res.jsonp(newCarOrderIds); // Return as an array of IDs
        } else {
          return res.status(404).jsonp([]); // Return an empty array if no records are found
        }
      } catch (e) {
        return res.status(500).jsonp({ message: 'Internal Server Error' });
      }
    });
  } else {
    next();
  }
};
