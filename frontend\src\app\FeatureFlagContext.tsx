import React, { PropsWithChildren, useContext } from 'react';
import { useGetStageConfigQuery } from '../store/api/StaticJsonApi';
import { CoraFeatureFlags } from '../../../infrastructure/lib/types_cdk/cdk-types';

interface FeatureFlagContextType {
  featureFlags: CoraFeatureFlags;
  isLoading: boolean;
}

const defaultFeatureFlags: CoraFeatureFlags = {
  copyOrder: false,
  updateNcoCoreData: false,
  reportRevokeTotalLoss: false,
  deallocateQuota: false,
  importerTransfer: false,
  handleDealerInventory: false,
  buySellTransfer: false,
  ncoInvoiceMapping: false,
};

const FeatureFlagContext = React.createContext<FeatureFlagContextType>({} as FeatureFlagContextType);

const FeatureFlagContextProvider: React.FC<PropsWithChildren> = (props) => {
  const stageConfigRes = useGetStageConfigQuery(undefined);
  return (
    <FeatureFlagContext.Provider
      value={{
        featureFlags: stageConfigRes.data?.featureFlags ?? defaultFeatureFlags,
        isLoading: stageConfigRes.isLoading,
      }}
    >
      {props.children}
    </FeatureFlagContext.Provider>
  );
};

const useFeatureFlagContext = (): FeatureFlagContextType => useContext(FeatureFlagContext);

export { FeatureFlagContext, useFeatureFlagContext };

export default FeatureFlagContextProvider;
