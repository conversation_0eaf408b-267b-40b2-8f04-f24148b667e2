import { Kas<PERSON><PERSON><PERSON><PERSON>Logger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { KccNCOConfigWindowMessagePayload } from '../../../../../lib/types/new-car-order-types';
import {
  CoraNcoUpdateApiRequest,
  CorePayload,
  InboundEventDispatcherEvent,
  NcoInfo,
  OneVmsEventKey,
} from '../../../../../lib/types/process-steering-types';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { createApiGWHandlerWithInitLogger } from '../../../../utils/api-gw-handler';
import { sendFail } from '../../../../utils/api-helpers';
import { ApiHandlerError } from '../../../../utils/errors';
import { KccConfigSignatureVerifier } from '../../../../utils/kcc-config-signature-verifier/kcc-config-signature-verifier';
import { EventApiContext } from '../event-api-context';
const objectValidator = new ObjectValidator<CoraNcoUpdateApiRequest>('CoraNcoUpdateApiRequest');

EventApiContext.init(OneVmsEventKey.UPDATE_NCO);

const updateNcoFunc = async (
  event: APIGatewayProxyEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const new_car_order_id = event.pathParameters ? event.pathParameters['ncoId'] : undefined;

  if (!new_car_order_id) {
    logger.log(LogLevel.INFO, 'new_car_order_id path param is required but missing', { data: event });
    return sendFail({ message: 'Missing ncoId query parameter', status: 400, reqHeaders: event.headers }, logger);
  }
  // Parse and validate request body
  let body_validated: CoraNcoUpdateApiRequest;
  let userAttributes: KasAuthEndpointResponse;
  try {
    const _res = EventApiContext.commonEventInputValidation(event, objectValidator);
    body_validated = _res.body_validated;
    userAttributes = _res.userAttributes;
  } catch (error) {
    if (error instanceof ApiHandlerError) {
      return sendFail(
        { message: error.message, status: error.statusCode, reqHeaders: event.headers },
        EventApiContext.logger,
      );
    } else {
      EventApiContext.logger.log(LogLevel.ERROR, 'Unexepected Error during validation', { data: error });
      throw error;
    }
  }
  // Validate Config with signature and public key for all stages except dev
  if (EventApiContext.stage !== 'dev') {
    const cookieHeader = event.headers.cookie ?? event.headers.Cookie;
    const configurationSignature = body_validated.payload.configuration_signature;

    const kccPayload: KccNCOConfigWindowMessagePayload = {
      kas: {
        model_info: {
          model_type: body_validated.payload.model_type,
          model_year: parseInt(body_validated.payload.model_year, 10),
          country_code: body_validated.payload.cnr,
        },
        configuration: body_validated.payload.configuration,
      },
      pvms: body_validated.payload.configuration_expire,
    };
    const isValidSignature = await KccConfigSignatureVerifier.verifySignature(
      kccPayload,
      configurationSignature,
      cookieHeader,
      EventApiContext.stage,
      logger,
    );

    if (!isValidSignature) {
      const errMessage = 'KCC config signature validation was not successful';
      logger.log(LogLevel.WARN, errMessage, { data: kccPayload });
      return sendFail({ message: errMessage, status: 400, reqHeaders: event.headers }, logger);
    } else {
      logger.log(LogLevel.INFO, 'Great! KCC config has a valid signature and will be processed');
    }
  }

  const ncosInfo: NcoInfo[] = body_validated.nco_ids_with_modified_at.map((nco) => ({
    pk_new_car_order_id: nco.pk_new_car_order_id,
    modified_at: nco.modified_at,
    sub_transaction_id: uuidv4(),
  }));
  const transformedRequestPayload: CorePayload = {
    payload: body_validated.payload,
    ncos_info: ncosInfo,
    event_type: OneVmsEventKey.UPDATE_NCO,
  };

  const dispatcherEvent: InboundEventDispatcherEvent = EventApiContext.buildDispatcherEvent(
    transformedRequestPayload,
    userAttributes,
  );
  return await EventApiContext.handleDispatcherEvent(dispatcherEvent, event.headers);
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGWHandlerWithInitLogger(EventApiContext.logger)(event, context, updateNcoFunc);
