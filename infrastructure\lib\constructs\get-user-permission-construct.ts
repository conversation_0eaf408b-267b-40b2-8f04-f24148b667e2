import { Construct } from 'constructs';

import { RestApi, RequestAuthorizer, LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { RemovalPolicy, aws_ssm, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { IFunction } from 'aws-cdk-lib/aws-lambda';

import { Constants } from '../utils/constants';
import { KasDynamodbTable, KasKmsKey, KasNodejsFunction, KasStage } from '@kas-resources/constructs';
import { ConstantsCdk, LambdaDefaultBundlingExternalModules } from '../utils/constants_cdk';

interface GetUserPermissionConstructProps {
  api: RestApi;
  authorizer: RequestAuthorizer;
  stage: KasStage;
  logGroupKey: KasKmsKey;
  applicationNameToAuthorize: string;
  corsDomain: string;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
}

export class GetUserPermissionConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: GetUserPermissionConstructProps) {
    super(scope, id);

    const rbamPermissionTableName = `cora-md-${props.stage}-RBAM-role-permissions`;

    const rbamTableKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      `/${props.stage}/cora-md-${props.stage}-RBAM/rbam-table-key-arn`,
    );

    const rbamTableKmsKey = KasKmsKey.fromKeyArn(this, 'rbamTableKmsKey', rbamTableKmsKeyArn);

    const rbamPermissionTable = KasDynamodbTable.fromTableAttributes(this, 'QuotaTable', {
      tableName: rbamPermissionTableName,
      encryptionKey: rbamTableKmsKey,
    });

    const apiResource = props.api.root.addResource('rbam-permissions');

    // Lambda for fetching all quotas
    const getRolePermissionForUser = new KasNodejsFunction(this, 'getRolePermissionForUser', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/rbam/fetch-role-permission-for-user/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-get-role-permission-for-user`,
      environment: {
        RBAM_TABLE_NAME: rbamPermissionTable.tableName,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
        CORS_DOMAIN: props.corsDomain,
      },
      description: 'Tries to get the permissions for the role of the user from rbam table',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    rbamPermissionTable.grantReadData(getRolePermissionForUser);

    const integration = new LambdaIntegration(getRolePermissionForUser);

    apiResource.addMethod('GET', integration, {
      authorizer: props.authorizer,
    });
  }
}
