/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DataSource, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
} from '../../../../../lib/entities/new-car-order-model';
import {
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  buildLambdaArn,
  initDataSourceForIntTest,
  createSqsEvent,
  invokeGenericLambda,
  pollNotifications,
  createInboundEvent,
} from '../../../../utils/integration-test-helpers';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.PVMS_UPDATE_NCO}`);
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const testNcoId = 'ITPVMS01';

const testNco: NewCarOrderModel = {
  pk_new_car_order_id: testNcoId,
  dealer_number: '4500940',
  cnr: 'C00',
  importer_code: 'USM',
  importer_number: '4500000',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'LF',
  quota_month: '2025-01',
  requested_dealer_delivery_date: '2025-06-01',
  shipping_code: 'SHIP01',
  receiving_port_code: 'IntPortCode',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  modified_at: new Date().toISOString(),
  order_status_onevms_code: 'PP2000',
  order_status_onevms_error_code: 'null',
  order_invoice_onevms_code: 'null',
  order_status_onevms_timestamp_last_change: new Date().toISOString(),
  changed_by_system: OneVmsSourceSystemKey.PVMS,
  configuration: { ordered_options: [], created_by: 'IntegrationTester', modified_by: 'IntegrationTester' },
  configuration_expire: null,
};

const defaultSqsEvent = {
  event_type: OneVmsEventKey.PVMS_UPDATE_NCO,
  nco_id: testNcoId,
  modified_at: testNco.modified_at,
  source_system: OneVmsSourceSystemKey.PVMS,
  payload: {
    mapInvoice: false,
    key: testNcoId,
    value: {
      ids: {
        new_car_order_id: testNcoId,
        vguid_pvms_DEPRECATED: 'VGUID1230',
        business_partner_id: 'BP123456789',
      },
      model_info: {
        model_year: 2025,
        model_type: 'MT0001',
        country_code: 'C00',
      },
      order_info: {
        base_info: {
          quota_month: '2025-01',
          order_type: 'LF',
        },
        trading_partner: {
          importer_code: 'USM',
          importer_number: '4500000',
          dealer_sold_to_number: '04500940',
          dealer_ship_to_number: '04500940',
        },
        status_info: {
          vehicle_status_pvms_code: 'V150',
          vehicle_status_pvms_timestamp: '2025-05-13T08:48:38.670Z',
          order_status_pvms_code: 'O070',
          order_status_pvms_timestamp: '2025-05-13T08:48:38.670Z',
        },
        sales_info: {
          sales_person_id: 'TESTID',
        },
      },
      logistics_info: {
        shipping_code: 'SHIP02',
        receiving_port_code: 'IntPortCode',
      },
    },
    timestamp: Date.now(),
    numOfTries: 0,
  },
};

let dataSource: DataSource;
let repository: Repository<NewCarOrderModel>;

describe('PVMS Update NCO Event Handler Integration Test', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
    ]);
    repository = dataSource.getRepository(NewCarOrderModel);

    const firstOrderRes = await repository.save(testNco);
    testNco.modified_at = firstOrderRes.modified_at!;
    defaultSqsEvent.modified_at = firstOrderRes.modified_at!;
  });

  afterAll(async () => {
    await repository.delete({ pk_new_car_order_id: testNcoId });
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({ pk_new_car_order_id: testNcoId });
    await dataSource.destroy();
  });

  afterEach(async () => {
    await repository.save(testNco);
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({ pk_new_car_order_id: testNcoId });
  });

  it('Should NOT update NCO and return fail if input is missing required props', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, nco_id: undefined }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Failed to parse/validate event');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: testNco.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.shipping_code).not.toEqual(defaultSqsEvent.payload.value.logistics_info.shipping_code);
  });

  it('Should NOT update NCO and return failure if NCO not found', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, nco_id: 'NOT_VALID_NCOID' }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Referenced NCO not found');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: testNco.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.shipping_code).not.toEqual(defaultSqsEvent.payload.value.logistics_info.shipping_code);
  });

  it('Should NOT update NCO and return failure for modified_at mismatch (racing condition)', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Nco was changed by someone else since the event was created');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: testNco.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.shipping_code).not.toEqual(defaultSqsEvent.payload.value.logistics_info.shipping_code);
  });

  it('Should NOT update NCO and return failure if PVMS status mapping is missing', async () => {
    const eventPayload = createInboundEvent({
      ...defaultSqsEvent,
      payload: {
        ...defaultSqsEvent.payload,
        value: {
          ...defaultSqsEvent.payload.value,
          order_info: {
            ...defaultSqsEvent.payload.value.order_info,
            status_info: {
              ...defaultSqsEvent.payload.value.order_info.status_info,
              vehicle_status_pvms_code: 'INVALID',
            },
          },
        },
      },
    });
    const event = createSqsEvent([{ ...eventPayload }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('No mapping for provided PVMS status/lock reason could be found');

    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: testNco.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.shipping_code).not.toEqual(defaultSqsEvent.payload.value.logistics_info.shipping_code);
  });

  it('Should update NCO successfully', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([eventPayload]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const ncoDb = await repository.findOneBy({ pk_new_car_order_id: testNco.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.shipping_code).toEqual(defaultSqsEvent.payload.value.logistics_info.shipping_code);
  });
});
