import { P<PERSON>utton, PButtonGroup, PInlineNotification, PModal, PSpinner } from '@porsche-design-system/components-react';
import { JSX, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../../app/hooks';
import { selectKccFocused, setKccFocused } from '../../../store/slices/KccConfigSlice';
import './OrderFlowModal.css';

interface OrderFlowModalProps {
  e2eId: string;
  heading: string;
  confirmButtonText: string;
  onConfirm: () => void;
  onBackToKcc: () => void;
  body: JSX.Element;
  error?: string;
  mutationRequestInProgress?: boolean;
}
export const OrderFlowModal: React.FC<OrderFlowModalProps> = ({
  e2eId,
  heading,
  onConfirm,
  onBackToKcc,
  body,
  confirmButtonText,
  error,
  mutationRequestInProgress,
}) => {
  const isKccFocused = useAppSelector(selectKccFocused); // Retrieves the status of the car order submit action
  const dispatch = useDispatch();
  const onBackToKccClick = () => {
    dispatch(setKccFocused(true));
    onBackToKcc();
  };
  const { t } = useTranslation();
  const [showInfo, setShowInfo] = useState<boolean>(true);
  const onAction = useCallback(() => setShowInfo((prevValue) => !prevValue), []);
  const [frontendError, setFrontendError] = useState('');
  const [backendError, setBackendError] = useState('');
  useEffect(() => {
    if (error) {
      const errorParts = error.split(/:/);
      // setting parts of the error
      setFrontendError(errorParts[0]); // 'fetch_error' or 'serialization_error'
      setBackendError(errorParts[1]); // Status code
    }
  }, [error]);

  return (
    <div>
      <PModal data-e2e={e2eId} open={!isKccFocused} heading={heading} onDismiss={onBackToKccClick}>
        <div style={{ position: 'relative', zIndex: showInfo ? 100 : 'auto' }}>
          {error && !backendError && <PInlineNotification state="error" description={error} dismissButton={false} />}
          {error && backendError && (
            <PInlineNotification
              state="error"
              description={showInfo ? frontendError : error}
              dismissButton={false}
              actionLabel={showInfo ? 'Show more info' : 'Show less info'}
              actionIcon={showInfo ? 'arrow-head-down' : 'arrow-head-up'}
              onAction={onAction}
            />
          )}
        </div>

        {mutationRequestInProgress ? <PSpinner /> : <div className="view-container">{body}</div>}
        <br />
        <br />
        <PButtonGroup className="footer">
          <PButton data-e2e="submit_order" onClick={onConfirm} disabled={mutationRequestInProgress}>
            {t(confirmButtonText)}
          </PButton>
          <PButton onClick={onBackToKccClick} disabled={mutationRequestInProgress} variant="secondary">
            {t('cancel_to_kcc')}
          </PButton>
        </PButtonGroup>
      </PModal>
    </div>
  );
};
