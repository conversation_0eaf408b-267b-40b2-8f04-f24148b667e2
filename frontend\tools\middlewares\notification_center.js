const { v4: uuidv4 } = require('uuid');

module.exports = function notificationByIdMiddleware(req, res, next) {
  if (req.method !== 'GET') return next();

  const match = req.url.match(/^\/notification-center\/transaction\/([^/?#]+)(?:\?(.*))?$/);
  if (!match) return next();

  const transactionId = decodeURIComponent(match[1]);

  // control simplu prin query: ?terminal=io | nio | both | none
  const qs = new URLSearchParams(match[2] || '');
  const terminal = (qs.get('terminal') || 'both').toLowerCase(); // implicit both

  const now = new Date();
  const iso = (d) => d.toISOString();
  const minus = (ms) => new Date(now.getTime() - ms);

  // terminale unice
  const subTxIO = {
    sub_transaction_id: uuidv4(),
    last_update: iso(minus(13 * 60 * 1000)),
    status: 'event_handler_io',
    obj_id: 'POP123567',
    uuid: uuidv4(),
  };

  const subTxNIO = {
    sub_transaction_id: uuidv4(),
    last_update: iso(minus(12 * 60 * 1000)),
    status: 'event_handler_nio',
    // de obicei NIO nu are obj_id util, îl lăsăm fără sau cu details
    uuid: uuidv4(),
    details: { error: 'Something went sideways' },
  };

  // alte stări non-terminale
  const subTxOthers = [
    {
      sub_transaction_id: uuidv4(),
      last_update: iso(minus(15 * 60 * 1000)),
      status: 'accepted',
      obj_id: 'POP302987',
      uuid: uuidv4(),
    },
    {
      sub_transaction_id: uuidv4(),
      last_update: iso(minus(14 * 60 * 1000)),
      status: 'dispatched',
      obj_id: 'POP302987',
      uuid: uuidv4(),
    },
    {
      sub_transaction_id: uuidv4(),
      last_update: iso(minus(10 * 60 * 1000)),
      status: 'exported',
      obj_id: 'POP302987',
      uuid: uuidv4(),
      details: { message: 'Exported successfully' },
    },
  ];

  // compune lista în funcție de ce terminal vrei
  const sub_transactions = [
    ...subTxOthers,
    ...(terminal === 'io' || terminal === 'both' ? [subTxIO] : []),
    ...(terminal === 'nio' || terminal === 'both' ? [subTxNIO] : []),
  ];

  const responseItem = {
    transaction_id: transactionId, // 🔒 EXACT ca în path
    action_by: 'someppnid',
    action_at: iso(minus(16 * 60 * 1000)),
    hidden: false,
    event_type: 'create',
    amount_of_objects: 2,
    last_update: iso(now),
    sub_transactions, // Omit<SubTransaction, 'transaction_id'>[]
  };

  return res.status(200).json({
    message: 'OK',
    data: responseItem,
  });
};
