import {
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
} from '../../../../../lib/types/process-steering-types';
import { auroraUnitTestSecret, dummyKafkaSecret, mockContext, setupMocks } from '../../../../utils/test-utils';
import oldNewCarOrderJson from '../../../../../test/data/old_new_car_order.json';
import * as utilsTypeOrm from '../../../../utils/utils-typeorm';
import { v4 as uuidv4 } from 'uuid';

const mocks = setupMocks({
  mockGetExistingNco: { returnNco: oldNewCarOrderJson as NewCarOrderModel },
});
jest.mock('../../../../utils/kafka');

import { handler } from './index';
import { GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { MSKEvent, MSKRecord } from 'aws-lambda';
import { EventImportContext } from '../event-import-context';
import { NewCarOrderModel } from '../../../../../lib/entities/new-car-order-model';
import { PvmsOrderDataDTO } from '../../../../../lib/types/pvms-types';
import { createKafkaRecord, createKafkaEvent } from '../../../../utils/integration-test-helpers';
import { Constants } from '../../../../../lib/utils/constants';
import { SendMessageCommand } from '@aws-sdk/client-sqs';
EventImportContext.sendToDispatcherSqs = jest.fn().mockResolvedValue(undefined);

beforeEach(() => {
  mocks.smMock!.reset();
  jest.resetModules();
  (EventImportContext.sendToDispatcherSqs as jest.Mock).mockClear();
});
describe('PVMS Import handler', () => {
  beforeEach(() => {
    mocks
      .smMock!.on(GetSecretValueCommand, { SecretId: process.env.KAFKA_SECRET_ARN })
      .resolves({ SecretString: JSON.stringify(dummyKafkaSecret) });
    mocks
      .smMock!.on(GetSecretValueCommand, { SecretId: process.env.AURORA_SECRET_ARN })
      .resolves({ SecretString: JSON.stringify(auroraUnitTestSecret) });
    mocks.sqsMock!.on(SendMessageCommand).resolves({ MessageId: uuidv4() });
  });

  const buildMSKEvent = (payload: Partial<PvmsOrderDataDTO>): MSKEvent => {
    const record: MSKRecord = createKafkaRecord(OneVmsSourceSystemKey.PVMS, 'PVMS_topic', payload);
    return createKafkaEvent([record]);
  };

  const completeTestValue: PvmsOrderDataDTO = {
    ids: { new_car_order_id: 'DCSUECQN', vguid_pvms_DEPRECATED: 'VGUID_12', business_partner_id: 'BP123' },
    model_info: {
      model_year: 2023,
      model_year_code: '23',
      model_type: 'MT0001',
      country_code: 'C00',
    },
    order_info: {
      base_info: {
        quota_month: '2023-10',
        order_type: 'OT',
        is_locator_visible: true,
      },
      trading_partner: {
        importer_code: 'IMP001',
        importer_number: '2700000',
        dealer_ship_to_number: 'D1001',
        dealer_sold_to_number: 'D1001',
      },
      status_info: {
        vehicle_status_pvms_code: 'V215',
        vehicle_status_pvms_timestamp: '2023-10-01T12:00:00Z',
        order_status_pvms_code: 'OS001',
        order_status_pvms_timestamp: '2023-10-01T12:00:00Z',
      },
      sales_info: {
        sales_person_id: 'SP123',
      },
    },
    logistics_info: {
      shipping_code: 'SC001',
      shipping_block: 'Block-A',
    },
    production_info: {
      production_plant: 'Plant-1',
    },
  };

  const sendToDispatcher = jest.spyOn(EventImportContext, 'sendToDispatcherSqs');
  const publishKafkaNotifications = jest.spyOn(EventImportContext, 'publishKafkaNotifications');

  it('should dispatch the event if the NewCarOrder exists', async () => {
    const event: MSKEvent = buildMSKEvent(completeTestValue);

    await handler(event, mockContext, () => {});
    expect(sendToDispatcher).toHaveBeenCalled();

    const dispatchedEvent = sendToDispatcher.mock.calls[0][0];
    expect(dispatchedEvent.event_type).toEqual(OneVmsEventKey.PVMS_UPDATE_NCO);
    expect(dispatchedEvent.source_system).toEqual(OneVmsSourceSystemKey.PVMS);
    expect(dispatchedEvent.ncos_info?.[0].pk_new_car_order_id).toEqual('DCSUECQN');
  });

  it('should send to PI SQS if Purchase Intention is detected', async () => {
    const piTestValue: Partial<PvmsOrderDataDTO> = {
      ...completeTestValue,
      order_info: {
        ...completeTestValue.order_info,
        status_info: {
          ...completeTestValue.order_info.status_info,
          vehicle_status_pvms_code: Constants.PVMS_PURCHASE_INTENTION_STATUS_FOR_LIST[0],
        },
      },
    };
    const event: MSKEvent = buildMSKEvent(piTestValue);
    await handler(event, mockContext, () => {});

    expect(publishKafkaNotifications).toHaveBeenCalled();
    const publishedNoti = publishKafkaNotifications.mock.calls[0][0];
    expect(publishedNoti[0].event_type).toEqual(OneVmsEventKey.PVMS_IMPORT);
    expect(publishedNoti[0].source_system).toEqual(OneVmsSourceSystemKey.PVMS);
    expect(publishedNoti[0].status).toEqual(NotificationStatus.ACCEPTED);
    expect(sendToDispatcher).not.toHaveBeenCalled();
  });

  it('should not dispatch event if schema is not valid', async () => {
    const event: MSKEvent = buildMSKEvent({
      ...completeTestValue,
      order_info: undefined,
    });

    await handler(event, mockContext, () => {});
    expect(sendToDispatcher).not.toHaveBeenCalled();
  });

  it('should not dispatch event if Importer is not relevant', async () => {
    const event: MSKEvent = buildMSKEvent({
      ...completeTestValue,
      order_info: {
        ...completeTestValue.order_info,
        trading_partner: {
          ...completeTestValue.order_info.trading_partner,
          importer_number: 'RandomImporter',
        },
      },
    });

    await handler(event, mockContext, () => {});
    expect(sendToDispatcher).not.toHaveBeenCalled();
  });

  it('should not dispatch if NewCarOrder does not exist', async () => {
    jest.spyOn(utilsTypeOrm, 'getExistingNco').mockResolvedValueOnce(Promise.resolve(null));

    const event: MSKEvent = buildMSKEvent({
      ...completeTestValue,
      ids: {
        ...completeTestValue.ids,
        new_car_order_id: 'NON_EXISTING_ID',
      },
    });

    await handler(event, mockContext, () => {});
    expect(sendToDispatcher).not.toHaveBeenCalled();
  });

  it('should throw error if SQS send in PI branch does not return MessageId', async () => {
    EventImportContext.sqsClient.send = jest.fn().mockResolvedValue({});
    const piTestValue: Partial<PvmsOrderDataDTO> = {
      ...completeTestValue,
      order_info: {
        ...completeTestValue.order_info,
        status_info: {
          ...completeTestValue.order_info.status_info,
          vehicle_status_pvms_code: Constants.PVMS_PURCHASE_INTENTION_STATUS_FOR_LIST[0],
        },
      },
    };
    const event: MSKEvent = buildMSKEvent(piTestValue);
    await expect(handler(event, mockContext, () => {})).rejects.toThrow('Failed to send Purchase Intention to SQS.');
  });
});
