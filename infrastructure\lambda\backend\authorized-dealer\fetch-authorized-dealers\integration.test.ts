/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { AuthorizedDealer, CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { CoraMdImporter } from '../../../../lib/types/masterdata-types';
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';

const lambdaArn = buildLambdaArn('fetch-authorized-dealers');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const impTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;

const importerOrgId = '2cf61c69-1f5f-4c23-9bf5-306917a7f07d';
const importerOrgId2 = '6ef61c69-1f5f-4c23-9bf5-306917a7f07d';
const dealerGroupOrgId = 'e34f9e2c-0c16-451d-a7f9-e9d27ae6d808';
const dealerOrgId1 = 'e58ec5fd-bbd7-4ba7-9cbd-b29146b29623';
const dealerOrgId2 = '52ef9bab-5da4-4159-ab49-4429fdf49340';
const dealerOrgId3 = '11ef9bab-5da4-4159-ab49-4429fdf49340';
const dealerOrgIdInactive = '0d1d679f-75bf-4948-a532-1f170e4dc22f';
const dealerOrgIdPlanned = '1bdcc700-3542-45e8-b142-a747f6da2357';
const unrelatedImpOrgId = '372f9e1b-c24f-46c1-bf42-3bc6503dd1d5';

const importers: CoraMdImporter[] = [
  {
    pk_importer_number: 'ItImpFAD',
    code: '123',
    display_name: 'IntegrationTest - Importer',
    ppn_status: 'OPERATIVE',
  },
  {
    pk_importer_number: 'ItUnrelatedImp',
    code: '345',
    display_name: 'Unrelated - Importer',
    ppn_status: 'OPERATIVE',
  },
  {
    pk_importer_number: 'ItImpFAD2',
    code: '678',
    display_name: 'IntegrationTest - Importer2',
    ppn_status: 'OPERATIVE',
  },
];

const orgRels: CoraOrgRelModel[] = [
  //importer relation to itself
  {
    pk_ppn_id: importerOrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItImpFAD',
    display_name: 'IntegrationTest - Importer',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  {
    pk_ppn_id: importerOrgId2,
    parent_ppn_id: importerOrgId2,
    dealer_number: 'ItImpFAD2',
    display_name: 'IntegrationTest - Importer2',
    importer_number: 'ItImpFAD2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to parent importer
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: importerOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to itself
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: dealerGroupOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItDlrFAD1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItDlrFAD1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: 'ItDlrFAD1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItDlrFAD2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItDlrFAD2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: 'ItDlrFAD2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  // dealer 3 relation to parent importer
  {
    pk_ppn_id: dealerOrgId3,
    parent_ppn_id: importerOrgId2,
    dealer_number: 'ItDlrFAD3',
    display_name: 'IntegrationTest - Dealer 3',
    importer_number: 'ItImpFAD2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent dealer group
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItDlrFADInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImpFAD',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent importer
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItDlrFADInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImpFAD',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to itself
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerOrgIdInactive,
    dealer_number: 'ItDlrFADInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImpFAD',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //planned dealer relation to parent dealer group
  {
    pk_ppn_id: dealerOrgIdPlanned,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItDlrFADPlanned',
    display_name: 'IntegrationTest - Dealer Planned',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'PLANNED',
    is_relevant_for_order_create: true,
  },
  //planned dealer relation to parent importer
  {
    pk_ppn_id: dealerOrgIdPlanned,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItDlrFADPlanned',
    display_name: 'IntegrationTest - Dealer Planned',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'PLANNED',
    is_relevant_for_order_create: true,
  },
  //planned dealer relation to itself
  {
    pk_ppn_id: dealerOrgIdPlanned,
    parent_ppn_id: dealerOrgIdPlanned,
    dealer_number: 'ItDlrFADPlanned',
    display_name: 'IntegrationTest - Dealer Planned',
    importer_number: 'ItImpFAD',
    is_deactivated: false,
    ppn_status: 'PLANNED',
    is_relevant_for_order_create: true,
  },
  //unrelated importer relation to itself
  {
    pk_ppn_id: unrelatedImpOrgId,
    parent_ppn_id: unrelatedImpOrgId,
    dealer_number: 'ItUnrelatedImp',
    display_name: 'IntegrationTest - Unrelated Imp',
    importer_number: 'ItUnrelatedImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const impPks = importers.map((imp) => ({
  pk_importer_number: imp.pk_importer_number,
}));

beforeAll(async () => {
  await prepareDynamodb([{ tableName: orgRelTableName, objs: orgRels }]);
  await prepareDynamodb([{ tableName: impTableName, objs: importers }]);
});

afterAll(async () => {
  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
  ]);

  await cleanupDynamodb([
    {
      tableName: impTableName,
      pks: impPks,
    },
  ]);
});

it('should return 401 if org is missing in auth context', async () => {
  const event = createApiGwEvent({});
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  expect(res.body).toContain('message');
});

it('should return 200 with the list of allowed and active dealers for an importer user', async () => {
  const event = createApiGwEvent({ userOrgId: importerOrgId });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  expect(res.body).not.toContain('message');
  const body = JSON.parse(res.body) as AuthorizedDealer[];
  expect(body).not.toBeNull();
  expect(body.length).toEqual(3);
  expect(body.find((org) => org.dealer_number === 'ItDlrFAD1')).toEqual({
    dealer_number: 'ItDlrFAD1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Importer',
    dealer_ppn_status: 'OPERATIVE',
  });
  expect(body.find((org) => org.dealer_number === 'ItDlrFAD2')).toEqual({
    dealer_number: 'ItDlrFAD2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Importer',
    dealer_ppn_status: 'OPERATIVE',
  });
  expect(body.find((org) => org.dealer_number === 'ItDlrFADPlanned')).toEqual({
    dealer_number: 'ItDlrFADPlanned',
    display_name: 'IntegrationTest - Dealer Planned',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Importer',
    dealer_ppn_status: 'PLANNED',
  });
});

it('should return 200 with the list of allowed and active dealers for an importer user with included importer name', async () => {
  const event = createApiGwEvent({ userOrgId: importerOrgId2 });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  expect(res.body).not.toContain('message');
  const body = JSON.parse(res.body) as AuthorizedDealer[];
  expect(body).not.toBeNull();
  expect(body.length).toEqual(1);
  expect(body.find((org) => org.dealer_number === 'ItDlrFAD3')).toEqual({
    dealer_number: 'ItDlrFAD3',
    display_name: 'IntegrationTest - Dealer 3',
    importer_number: 'ItImpFAD2',
    importer_display_name: 'IntegrationTest - Importer2',
    role: 'Importer',
    dealer_ppn_status: 'OPERATIVE',
  });
});

it('should return 200 with a single dealer for a dealer user', async () => {
  const event = createApiGwEvent({ userOrgId: dealerOrgId1 });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  expect(res.body).not.toContain('message');
  const body = JSON.parse(res.body) as AuthorizedDealer[];
  expect(body).not.toBeNull();
  expect(body.length).toEqual(1);
  expect(body[0]).toEqual({
    dealer_number: 'ItDlrFAD1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Dealer',
    dealer_ppn_status: 'OPERATIVE',
  });
});

it('should return 200 with empty list for unrelated Importer', async () => {
  const event = createApiGwEvent({ userOrgId: unrelatedImpOrgId });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  expect(res.body).not.toContain('message');
  const body = JSON.parse(res.body) as AuthorizedDealer[];
  expect(body).not.toBeNull();
  expect(body.length).toEqual(0);
});

it('should return 200 with the list of all dealers including deactivated for an importer user', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    queryStringParameters: { include_deactivated: 'true' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  expect(res.body).not.toContain('message');
  const body = JSON.parse(res.body) as AuthorizedDealer[];
  expect(body).not.toBeNull();
  expect(body.length).toEqual(4);
  expect(body.find((org) => org.dealer_number === 'ItDlrFAD1')).toEqual({
    dealer_number: 'ItDlrFAD1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Importer',
    dealer_ppn_status: 'OPERATIVE',
  });
  expect(body.find((org) => org.dealer_number === 'ItDlrFAD2')).toEqual({
    dealer_number: 'ItDlrFAD2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Importer',
    dealer_ppn_status: 'OPERATIVE',
  });
  expect(body.find((org) => org.dealer_number === 'ItDlrFADInactive')).toEqual({
    dealer_number: 'ItDlrFADInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Importer',
    dealer_ppn_status: 'OPERATIVE',
  });
  expect(body.find((org) => org.dealer_number === 'ItDlrFADPlanned')).toEqual({
    dealer_number: 'ItDlrFADPlanned',
    display_name: 'IntegrationTest - Dealer Planned',
    importer_number: 'ItImpFAD',
    importer_display_name: 'IntegrationTest - Importer',
    role: 'Importer',
    dealer_ppn_status: 'PLANNED',
  });
});
