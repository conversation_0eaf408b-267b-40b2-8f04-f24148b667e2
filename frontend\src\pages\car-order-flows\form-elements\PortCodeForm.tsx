import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PSpinner } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraMdPortCodeResponseItem } from '../../../../../infrastructure/lib/types/masterdata-types';
import { useGetPortCodesForDealerQuery } from '../../../store/api/MasterdataApi';
import { FetchError } from '../../errors/FetchErrors';

interface PortCodeFormProps {
  dealer_number: string;
  selectedReceivingPortCode?: string;
  handleReceivingPortCodeSelect: (receiving_port_code: string) => void;
  setError: (area: string, err?: string) => void;
  setHasAccess?: (access: boolean) => void;
}
export const PortCodeForm: React.FC<PortCodeFormProps> = ({
  dealer_number,
  selectedReceivingPortCode,
  handleReceivingPortCodeSelect,
  setError,
  setHasAccess,
}) => {
  const { t } = useTranslation();

  const { data: portCodes, error, isLoading } = useGetPortCodesForDealerQuery(dealer_number);
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);
  const [standardPortCode, setStandardPortCode] = useState<CoraMdPortCodeResponseItem | undefined>(undefined);
  const [alternativePortCodes, setAlternativePortCodes] = useState<CoraMdPortCodeResponseItem[] | undefined>(undefined);

  useEffect(() => {
    setError('port_code_form', errMessage);
  }, [errMessage]);

  useEffect(() => {
    if (portCodes?.length === 0) {
      return;
    } else {
      setStandardPortCode(portCodes?.find((p) => p.standard));
      setAlternativePortCodes(portCodes?.filter((p) => !p.standard));
    }
  }, [portCodes]);

  if (isLoading) {
    return <PSpinner />;
  }

  if (!portCodes) {
    return <FetchError custom_error={error} error_area="port_code" />;
  }
  function renderPortCodeOption(p: CoraMdPortCodeResponseItem) {
    return (
      <option key={p.pk_port_code} value={p.pk_port_code}>
        {`${p.pk_port_code} - ${p.display_name}`}
      </option>
    );
  }
  const initialPortCode =
    selectedReceivingPortCode ??
    standardPortCode?.pk_port_code ??
    (alternativePortCodes && alternativePortCodes.length > 0 ? alternativePortCodes[0].pk_port_code : '');

  setHasAccess?.(
    Boolean(selectedReceivingPortCode) || Boolean(standardPortCode) || (alternativePortCodes?.length ?? 0) > 0,
  );

  return (
    <>
      {(selectedReceivingPortCode || standardPortCode || (alternativePortCodes?.length ?? 0) > 0) && (
        <PSelectWrapper
          data-e2e="SelectPortCode"
          style={{ flexBasis: 'calc(50% - 10px)' }}
          label={t('port_code_prompt')}
          state={errMessage ? 'error' : 'none'}
          message={errMessage}
        >
          <select
            value={initialPortCode ?? ''}
            onChange={(e) => {
              handleReceivingPortCodeSelect(e.target.value);
            }}
            disabled={!portCodes.some((p) => p.editable)}
          >
            {!selectedReceivingPortCode && <option disabled={true}>{t('port_code_selection')}</option>}
            <optgroup label={t('port_code_standard')}>
              {standardPortCode && renderPortCodeOption(standardPortCode)}
            </optgroup>
            {alternativePortCodes && alternativePortCodes.length > 0 && (
              <optgroup label={t('port_code_alternative')}>
                {alternativePortCodes.map((portCode) => renderPortCodeOption(portCode))}
              </optgroup>
            )}
          </select>
          {!portCodes.some((p) => p.editable) && <div style={{ color: 'gray' }}>{t('port_code_not_editable')}</div>}
        </PSelectWrapper>
      )}
    </>
  );
};
