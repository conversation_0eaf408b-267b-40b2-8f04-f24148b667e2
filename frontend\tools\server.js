const jsonServer = require('json-server');
const path = require('path');
const mw_copy_order = require('./middlewares/copy_order');
const mw_entity_get = require('./middlewares/entity_get');
// const mw_fetch_orders_with_param = require('./middlewares/fetch_orders_with_param');
const mw_fetch_orders_with_param_aggrid_ssrm_post = require('./middlewares/fetch_orders_with_param_aggrid_ssrm_post');
const mw_refresh_token = require('./middlewares/refreshToken');
const mw_remove_api_path = require('./middlewares/removeApiPath');
const mw_return_error_dealer = require('./middlewares/returnErrorDealer');
const mw_static_files_mock = require('./middlewares/static_files_mock');
const mw_audit_result = require('./middlewares/audit_result');
const mw_total_loss = require('./middlewares/total_loss');
const routes_json = require('./routes.json');
const audit_result = require('./middlewares/audit_result');
const mw_importer_transfer = require('./middlewares/importer_transfer');
const mw_buy_sell_transfer = require('./middlewares/buy_sell_transfer');
const mw_fetch_ids = require('./middlewares/fetch_order_ids');
const mw_process_steering_api = require('./middlewares/process_steering_api');
const mw_notification_center = require('./middlewares/notification_center');
const server = jsonServer.create();
const router = jsonServer.router(path.join(__dirname, 'db.json'));
const middlewares = jsonServer.defaults();

// Add this before server.use(router)
server.use(jsonServer.rewriter(routes_json));

// Use custom ids
server.use((req, res, next) => {
  if (req.url.includes('/new-car-order')) {
    router.db._.id = 'pk_new_car_order_id';
  } else if (req.url.includes('/purchase-intention')) {
    router.db._.id = 'purchase_intention_id';
  } else if (req.url.includes('/port-code')) {
    router.db._.id = 'pk_port_code';
  } else if (req.url.includes('/order-type')) {
    router.db._.id = 'pk_order_type';
  } else if (req.url.includes('/dealer')) {
    router.db._.id = 'sk_dealer_number';
  } else if (req.url.includes('/shipping-code')) {
    router.db._.id = 'pk_shipping_code';
  } else if (req.url.includes('/importer')) {
    router.db._.id = 'pk_importer_number';
  }
  next();
});

// Delay for all routes
server.use(function (req, res, next) {
  setTimeout(next, 500);
});
server.use(jsonServer.bodyParser);

server.use([
  ...middlewares,
  mw_process_steering_api,
  mw_entity_get,
  mw_fetch_orders_with_param_aggrid_ssrm_post,
  mw_refresh_token,
  mw_remove_api_path,
  mw_return_error_dealer,
  mw_static_files_mock,
  mw_total_loss,
  mw_copy_order,
  audit_result,
  mw_importer_transfer,
  mw_buy_sell_transfer,
  mw_fetch_ids,
  mw_importer_transfer,
  mw_notification_center,
]);
server.use(router);
server.listen(3001, () => {
  console.log('JSON Server is running');
});
