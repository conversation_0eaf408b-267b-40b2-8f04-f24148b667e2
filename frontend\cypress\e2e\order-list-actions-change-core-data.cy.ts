import { NotificationStatus, OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import { NOTIFICATION_CENTER_TRANSACTION_BASE_URL, USERNAME_WRITE } from '../support/constants';
import { generateMtvs, generateNewCarOrders } from '../support/order-lists-test-data';
import { checkTransactionResponse } from '../support/utils';

const displayOrdersTab = '[data-e2e="display_orders"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';
const updateOrderCoreDataButton = '[data-e2e="update_order_core_data"]';
const updateOrderCoreDataMultiButton = '[data-e2e="update_orders_core_data"]';
const updateOrderCoreDataModal = `[data-e2e="${OneVmsEventKey.UPDATE_CORE_DATA}_order_modal"]`;
const multiDetailsContainer = `[data-e2e="${OneVmsEventKey.UPDATE_CORE_DATA}_multi_details"]`;
const orderTypeLocator: string = '[data-e2e="SelectOrderType"]';
const submitOrderLocator: string = '[data-e2e="accept"]';
const closeBtn = '[data-e2e="close"]';

const changeToOrderType = 'VF';

const ordersEndpointURL = '**/new-car-order**';

const ncoPrefixThisTest = 'ICD';
const preproductionOrders = generateNewCarOrders(ncoPrefixThisTest, 'PP0000', 3, {
  order_invoice_onevms_code: 'PI0000',
  order_type: 'MF',
});
const preproductionOrderMtvs = generateMtvs(preproductionOrders);

describe('Order List Actions Update CoreData', () => {
  beforeEach(() => {
    cy.login(USERNAME_WRITE);
    cy.task('prepareMtvRds', { objs: preproductionOrderMtvs }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: preproductionOrderMtvs });
  });

  it('Update order core data from list - success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //Click on Edit on first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 10000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(updateOrderCoreDataButton, { timeout: 3000 }).should('be.visible').click();

    //Check Modal view, select some props and save
    cy.get(updateOrderCoreDataModal, { timeout: 20000 }).should('exist').and('not.be.empty');
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, changeToOrderType);
    cy.intercept('PATCH', `${ordersEndpointURL}/update-core-data`).as('updateCoreData');
    cy.get(submitOrderLocator).click({ force: true });
    cy.wait('@updateCoreData')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });
    cy.get(updateOrderCoreDataModal).find(closeBtn).click();
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="order_type"]').contains(changeToOrderType, { timeout: 2000 });
  });

  it('Update order core data from single order from list, error case invalid data sent (mocked request)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"]').should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 10000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(updateOrderCoreDataButton, { timeout: 3000 }).should('be.visible').click();

    //Check Modal view, select some props and save
    cy.get(updateOrderCoreDataModal, { timeout: 20000 }).should('exist').and('not.be.empty');
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, changeToOrderType);

    //confirm and intercept cora api call to be able to let it fail
    cy.intercept('PATCH', '**/update-core-data', (req) => {
      req.body.nco_ids_with_modified_at = [];
      req.continue();
    });
    cy.get(submitOrderLocator).click({ force: true });
    cy.get(updateOrderCoreDataModal)
      .find(`.header-error-${OneVmsEventKey.UPDATE_CORE_DATA}`, { timeout: 10000 })
      .should('be.visible');
  });

  it('Update order core data from single order from list, error case order changed (racecondition, mocked request)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"]').should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 10000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(updateOrderCoreDataButton, { timeout: 3000 }).should('be.visible').click();

    //Check Modal view, select some props and save
    cy.get(updateOrderCoreDataModal, { timeout: 20000 }).should('exist').and('not.be.empty');
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, changeToOrderType);

    //confirm and intercept cora api call to be able to let it fail
    cy.intercept('PATCH', '**/update-core-data', (req) => {
      req.body.nco_ids_with_modified_at[0].modified_at = new Date(0).toISOString();
      req.continue();
    }).as('updateCoreData');
    cy.get(submitOrderLocator).click({ force: true });
    cy.wait('@updateCoreData')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.ERROR),
        );
      });

    //close the dialog (since call was mocked, do not check if table was updated)
    cy.get(updateOrderCoreDataModal).find(closeBtn).click();
  });

  it('Update order core data multi from list - success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //check table items, mark first 2 items and click multi cancel
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="2"]', { timeout: 10000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="2"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[2].pk_new_car_order_id);
    cy.get(updateOrderCoreDataMultiButton).should('not.exist');
    cy.wait(1000);

    cy.get('[row-index="0"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.get('[row-index="2"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.get(updateOrderCoreDataMultiButton, { timeout: 1000 }).should('exist');
    cy.get(updateOrderCoreDataMultiButton).click({ force: true });

    //Check Modal view, select some props and save. Adjust when new features like shipping code are implemented
    cy.get(updateOrderCoreDataModal).should('exist').and('not.be.empty');
    cy.get(updateOrderCoreDataModal).find(multiDetailsContainer).should('be.visible');
    cy.get(updateOrderCoreDataModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .should('have.length', 2);
    cy.get(updateOrderCoreDataModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(0)
      .find('p-text')
      .should('have.length', 1);
    cy.get(updateOrderCoreDataModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(1)
      .find('p-text')
      .should('have.length', 1);
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, changeToOrderType);

    cy.intercept('PATCH', `${ordersEndpointURL}/update-core-data`).as('updateCoreData');
    cy.get(submitOrderLocator).click({ force: true });
    cy.wait('@updateCoreData')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });
    cy.get(updateOrderCoreDataModal).find(closeBtn).click();
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="2"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[2].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="order_type"]').contains(changeToOrderType, { timeout: 2000 });
    cy.get('[row-index="2"] [col-id="order_type"]').contains(changeToOrderType, { timeout: 2000 });
  });
});
