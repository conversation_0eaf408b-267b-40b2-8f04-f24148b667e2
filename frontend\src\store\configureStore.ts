import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';
import notificationReducer from './slices/NotificationSlice';
import userMenuReducer from './slices/UserMenuSlice';
import kccConfigReducer from './slices/KccConfigSlice';
import correlationReducer from './slices/CorrelationSlice';
import { setupListeners } from '@reduxjs/toolkit/query';
import { baseApi } from './api/BaseApi';

export const store = configureStore({
  reducer: {
    notifications: notificationReducer,
    user: userMenuReducer,
    kccConfig: kccConfigReducer,
    correlation: correlationReducer,
    [baseApi.reducerPath]: baseApi.reducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(baseApi.middleware),
});

setupListeners(store.dispatch);
export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
export type AppThunk<ReturnType = void> = ThunkAction<ReturnType, RootState, unknown, Action<string>>;
