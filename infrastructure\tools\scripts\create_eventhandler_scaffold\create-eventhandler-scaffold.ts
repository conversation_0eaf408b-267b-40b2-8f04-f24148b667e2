import { OneVmsEventHandler<PERSON>ey } from '../../../lib/utils/constants';

import { promises as fs } from 'fs';
import * as path from 'path';

for (const [enumKey, handlerKey] of Object.entries(OneVmsEventHandlerKey)) {
  ensureDirAndFile(
    `lambda/backend/process-steering/event-handler/${handlerKey}`,
    'index.ts',
    getScaffold(enumKey, handlerKey),
  ).catch(console.error);
}

async function ensureDirAndFile(dirPath: string, fileName: string, defaultContent = ''): Promise<void> {
  try {
    // Create the directory if it doesn't exist
    await fs.mkdir(dirPath, { recursive: true });

    const filePath = path.join(dirPath, fileName);

    // Check if the file exists
    try {
      await fs.access(filePath);
      console.log('File already exists:', filePath);
    } catch {
      // File doesn't exist, create it
      await fs.writeFile(filePath, defaultContent);
      console.log('File created:', filePath);
    }
  } catch (error) {
    console.error('Error ensuring file and directory:', error);
  }
}

function toCamelCase(input: string): string {
  return input.replace(/-([a-z])/g, (_, char: string) => char.toUpperCase());
}

function getScaffold(eventHandlerEnumKey: string, eventHandlerKey: OneVmsEventHandlerKey): string {
  return `import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';

import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { SQSBatchResponseWithError } from '../../../../../lib/types/process-steering-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { EventHandlerContext } from '../event-handler-context';
import { KasLambdaLogger } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

EventHandlerContext.init(OneVmsEventHandlerKey.${eventHandlerEnumKey}, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

const ${toCamelCase(eventHandlerKey)}Func = async (
  event: SQSEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  /**
   * @deprecated use EventHandlerContext.logger instead
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  throw new Error('Not Implemented');

  return sqsBatchResponse;
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, ${toCamelCase(eventHandlerKey)}Func);
`;
}
