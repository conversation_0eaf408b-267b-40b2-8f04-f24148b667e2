import {
  NotificationTransactionGetAllQueryParams,
  NotificationTransactionResponseItem,
} from '../../../../infrastructure/lib/types/notification-center-types';
import { baseApi } from './BaseApi';

const apiPrefix = 'notification-center';
const notificationCenterApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // GET /notification-center/transaction/:id
    getTransactionById: builder.query<NotificationTransactionResponseItem, { transaction_id: string }>({
      query: ({ transaction_id }) => ({
        url: `${apiPrefix}/transaction/${transaction_id}`,
      }),
    }),

    // GET /notification-center/transaction
    getAllTransactions: builder.query<NotificationTransactionResponseItem[], NotificationTransactionGetAllQueryParams>({
      query: (params) => ({
        url: `${apiPrefix}/transaction`,
        params, // fetchBaseQuery va face query string direct din obiect
      }),
    }),
  }),
});

export const { useGetTransactionByIdQuery, useGetAllTransactionsQuery } = notificationCenterApi;
