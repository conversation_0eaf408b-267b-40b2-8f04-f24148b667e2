import { OrderActionType } from '../../src/store/types';
import { USERNAME_WRITE } from '../support/constants';
import { preproductionOrderMtvs, preproductionOrders } from '../support/order-lists-test-data';
import { retryableBefore } from '../support/retry';

const displayOrdersTab = '[data-e2e="display_orders"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';
const copyConfirmBtn = '[data-e2e="accept"]';

describe('Order List Actions Copy', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task('prepareMtvRds', { objs: preproductionOrderMtvs }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: preproductionOrderMtvs });
  });

  it('Copy order successful', () => {
    const copyOrderButton = '[data-e2e="copy_order"]';
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //Sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    //copy first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 20000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(copyOrderButton, { timeout: 3000 }).should('be.visible').click();

    const copyOrderModal = `[data-e2e="${OrderActionType.COPY}_order_modal"]`;

    cy.get(copyOrderModal, { timeout: 20000 }).should('exist').and('not.be.empty');
    cy.wait(1000);
    for (let i = 0; i < 3; i++) {
      const inputSelector = `[data-e2e="quota-month-${i}"]`;
      cy.get(inputSelector).should('exist').and('not.be.disabled');
      //The default 0 could not be cleared, use {selectall} to override the 0
      cy.get(inputSelector).type('{selectall}2');
      cy.get(inputSelector).should('have.value', '2');
    }

    cy.get(copyConfirmBtn).click();
    cy.wait(5000);

    const successIconSelector = '[data-e2e="copy_order_success_icon"]';
    const headingSelector = '[data-e2e="copy_order_head"]';
    const copyIdsBtSelector = '[data-e2e="copy_ids"]';

    cy.get(successIconSelector, { timeout: 30000 }).should('be.visible');
    cy.get(headingSelector).should('be.visible');
    cy.get(copyIdsBtSelector).click();

    cy.window()
      .then((win) => win.navigator.clipboard.readText())
      .then((clipboardText) => {
        cy.task('cleanupNcoRds', {
          ids: clipboardText.split(',').map((id) => id.trim()),
        });
      });
  });

  it('Copy order partial successful', () => {
    const copyOrderButton = '[data-e2e="copy_order"]';
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //Sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    //Copy first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 20000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(copyOrderButton, { timeout: 3000 }).should('be.visible').click();

    const copyOrderModal = `[data-e2e="${OrderActionType.COPY}_order_modal"]`;

    cy.get(copyOrderModal, { timeout: 20000 }).should('exist').and('not.be.empty');
    cy.wait(1000);

    for (let i = 0; i < 2; i++) {
      const inputSelector = `[data-e2e="quota-month-${i}"]`;
      cy.get(inputSelector).should('exist').and('not.be.disabled');
      cy.get(inputSelector).type('{selectall}2');
      cy.get(inputSelector).should('have.value', '2');
    }

    //Confirm and intercept cora api cancel call to mock the response
    cy.intercept('POST', '**/copy', (req) => {
      const currentDate = new Date();

      const formatMonth = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        return `${year}-${month}`;
      };

      const responseBody = [
        {
          quota_month: formatMonth(currentDate),
          quota_consumed: 1,
          quota_failed: 1,
          new_car_order_ids: ['CONCO01'],
        },
        {
          quota_month: formatMonth(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1)),
          quota_consumed: 2,
          quota_failed: 0,
          new_car_order_ids: ['CONCO2', 'CONCO03'],
        },
      ];

      req.reply({
        body: responseBody,
      });
    });

    cy.get(copyConfirmBtn).click();

    const notificationSelector = '[data-e2e="copy_order_fail_noti"]';
    const headingSelector = '[data-e2e="copy_order_fail_head"]';

    cy.get(notificationSelector, { timeout: 10000 })
      .should('be.visible')
      .and('contain.text', 'Unfortunately not all orders could be copied');

    cy.get(headingSelector).should('be.visible').and('contain.text', '3/4 orders created from copy order');
  });

  it('Copy order complete fail', () => {
    const copyOrderButton = '[data-e2e="copy_order"]';
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //Sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    //Copy first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 20000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(copyOrderButton, { timeout: 3000 }).should('be.visible').click();

    const copyOrderModal = `[data-e2e="${OrderActionType.COPY}_order_modal"]`;

    cy.get(copyOrderModal, { timeout: 20000 }).should('exist').and('not.be.empty');
    cy.wait(1000);

    for (let i = 0; i < 2; i++) {
      const inputSelector = `[data-e2e="quota-month-${i}"]`;
      cy.get(inputSelector).should('exist').and('not.be.disabled');
      cy.get(inputSelector).type('{selectall}2');
      cy.get(inputSelector).should('have.value', '2');
    }

    //Confirm and intercept cora api cancel call to mock the response
    cy.intercept('POST', '**/copy', (req) => {
      const currentDate = new Date();

      const formatMonth = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        return `${year}-${month}`;
      };

      const responseBody = [
        {
          quota_month: formatMonth(currentDate),
          quota_consumed: 0,
          quota_failed: 2,
          new_car_order_ids: [],
        },
        {
          quota_month: formatMonth(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1)),
          quota_consumed: 0,
          quota_failed: 2,
          new_car_order_ids: [],
        },
      ];

      req.reply({
        body: responseBody,
      });
    });

    cy.get(copyConfirmBtn).click();

    const notificationSelector = '[data-e2e="copy_order_fail_noti"]';
    const headingSelector = '[data-e2e="copy_order_fail_head"]';

    cy.get(notificationSelector, { timeout: 10000 })
      .should('be.visible')
      .and('contain.text', 'Unfortunately not all orders could be copied');

    cy.get(headingSelector).should('be.visible').and('contain.text', '0/4 orders created from copy order');
  });
});
