import { CoraNCOConfiguration } from './new-car-order-types';

// Main DTO interface
export interface CssSaleEvent {
  //common props
  saleId: string;
  dealer: {
    partnerNumber: string;
    entryDn: string;
    importerPartnerNumber: string;
  };
  customer: {
    businessPartnerId: string;
    type: 'PRIVATE' | 'COMMERCIAL' | 'COMMERCIAL_RESELLER';
    leadId?: string;
  };
  meta: {
    eventType: CssSaleEventType;
    /**
     * @format date-time
     */
    eventTime: string; //iso date string
    userType: 'PPN' | 'M2M';
    userId: string; // id of the user that triggered the event
  };
  //only for 2.x events
  vehicle?: {
    newCarOrderId?: string;
    inventoryId?: string;
  };
  //event specific props, hence optional
  configuration?: CoraNCOConfiguration; //optimistic until further notice
  updatedConfiguration?: CoraNCOConfiguration; //lets hope they do NOT send a partial update here
  /**
   * @pattern ^\d\d\d\d-\d\d$
   */
  quotaMonth?: string; //syntax TBD
  /**
   * @format date-time
   */
  desiredDeliveryDate?: string; //iso date string
  salesPerson?: string;
  newCarOrderId?: string;
  //these 3 fields are actually supposed to be in "configuration", which does not make sense
  //lets see what CSS makes of that later, put them here for now
  orderType?: string; //this is actually the modelType
  modelYear?: string;
  cNr?: string;
}

//only relevant types for Cora are defined here
export enum CssSaleEventType {
  ConfiguredVehicleSold = 'ConfiguredVehicleSold',
  NewStockVehicleSold = 'NewStockVehicleSold',
  ConfigurationChanged = 'ConfigurationChanged',
}

export type CssCreateSaleEvent = CssSaleEvent &
  Required<Pick<CssSaleEvent, 'orderType' | 'modelYear' | 'cNr' | 'quotaMonth' | 'salesPerson' | 'configuration'>> & {
    transaction_id: string;
  };

export type CssUpdateSaleEvent = CssSaleEvent &
  Required<Pick<CssSaleEvent, 'updatedConfiguration'>> & {
    transaction_id: string;
  };
