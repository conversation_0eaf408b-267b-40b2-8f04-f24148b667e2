import { APIGatewayProxyEvent, Context } from 'aws-lambda';

export const event: APIGatewayProxyEvent = {
  body: null,
  headers: {},
  multiValueHeaders: {},
  httpMethod: '',
  path: '',
  pathParameters: {},
  queryStringParameters: {},
  multiValueQueryStringParameters: {},
  stageVariables: {},
  resource: '',
  requestContext: {
    authorizer: undefined,
    protocol: '',
    httpMethod: '',
    identity: {
      accessKey: null,
      accountId: null,
      apiKey: null,
      apiKeyId: null,
      caller: null,
      clientCert: null,
      cognitoAuthenticationProvider: null,
      cognitoAuthenticationType: null,
      cognitoIdentityId: null,
      cognitoIdentityPoolId: null,
      principalOrgId: null,
      sourceIp: '',
      user: null,
      userAgent: null,
      userArn: null,
    },
    path: '',
    requestTimeEpoch: 0,
    resourceId: '',
    resourcePath: '',
    domainName: '',
    requestId: '',
    routeKey: '',
    stage: '',
    accountId: '',
    apiId: '',
    domainPrefix: '',
  },
  isBase64Encoded: false,
};

export const context: Context = {
  callbackWaitsForEmptyEventLoop: false,
  functionName: '',
  functionVersion: '',
  invokedFunctionArn: '',
  memoryLimitInMB: '',
  awsRequestId: '',
  logGroupName: '',
  logStreamName: '',
  getRemainingTimeInMillis: function (): number {
    throw new Error('Function not implemented.');
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
  done: function (error?: Error | undefined, result?: any): void {
    throw new Error('Function not implemented.');
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  fail: function (error: string | Error): void {
    throw new Error('Function not implemented.');
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
  succeed: function (messageOrObject: any): void {
    throw new Error('Function not implemented.');
  },
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const callback = (error: any, result: any): void => {
  if (error) {
    console.error(error);
  } else {
    console.log(result);
  }
};
