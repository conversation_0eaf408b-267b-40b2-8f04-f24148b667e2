import { PTextFieldWrapper } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraNCOCopyApiRequest, CoraNCOGetApiResponse } from '../../../../infrastructure/lib/types/new-car-order-types';
import { CoraQuota } from '../../../../infrastructure/lib/types/quota-api-types';
import { useGetQuotaForDealerQuery } from '../../store/api/QuotaApi';
import { getNextSixMonths } from '../../utils/date';
interface MultiQuotaFormProps {
  dealer_number: string;
  importer_number: string;
  model_type: string;
  model_year: string;
  errorInput: string | undefined;
  selectedQuotaMonth?: string | null;
  existingNewCarOrder?: CoraNCOGetApiResponse; // Only for existing NewCarOrder (edit workflow)
  isDisabled?: boolean;
  //handleQuotaSelect: (quota_month: string, amount: number, index: number) => void;
  setError: (message: string | undefined) => void;
  setIsModified: (status: boolean) => void;
  setModifiedQuotas: (quota: CoraNCOCopyApiRequest[]) => void;
  modifiedQuotas: CoraNCOCopyApiRequest[];
  nextSixMonthsQuotas: CoraQuota[];
  setNextSixMonthsQuotas: (quotas: CoraQuota[]) => void;
}

const MAX_SELECTABLE_QUOTA = 100;

export const QuotaTable: React.FC<MultiQuotaFormProps> = ({
  dealer_number,
  importer_number,
  model_type,
  model_year,
  errorInput,
  setError,
  setIsModified,
  setModifiedQuotas,
  modifiedQuotas,
  nextSixMonthsQuotas,
  setNextSixMonthsQuotas,
}) => {
  const { t } = useTranslation();
  const {
    data: quota_months,
    error,
    isLoading,
    isFetching,
  } = useGetQuotaForDealerQuery({ dealer_number, model_type, model_year, importer_number });

  useEffect(() => {
    const nextsixmonths = getNextSixMonths();
    const quotasForNextSixMonths: CoraQuota[] = nextsixmonths
      .map((month) => quota_months?.filter((quota) => quota.quota_month === month))
      .flat()
      .filter((quota): quota is CoraQuota => quota !== undefined && quota.quota_open > 0);
    if (quotasForNextSixMonths.length === 0 && !isLoading) {
      setNextSixMonthsQuotas([]);
      setError(t('copy_order_warning_quota'));
    } else {
      setError(undefined);
      setNextSixMonthsQuotas(quotasForNextSixMonths);
    }

    const initialRawInputs = Object.fromEntries(quotasForNextSixMonths.map((entry) => [entry.quota_month, 0]));
    setRawInputs(initialRawInputs);
  }, [quota_months, isLoading, dealer_number, setNextSixMonthsQuotas, t]);

  const [tempQuota, setTempQuota] = useState<CoraNCOCopyApiRequest | undefined>(undefined);
  useEffect(() => {
    if (!tempQuota) return;
    const updatedQuotas: CoraNCOCopyApiRequest[] = modifiedQuotas.map((quota) => {
      if (quota[Object.keys(tempQuota)[0]] !== undefined) {
        return {
          ...quota,
          [Object.keys(tempQuota)[0]]: tempQuota[Object.keys(tempQuota)[0]],
        };
      }
      return quota;
    });
    const quotaExists = updatedQuotas.some((quota) => Object.keys(quota).includes(Object.keys(tempQuota)[0]));
    const finalQuotas = quotaExists ? updatedQuotas : [...updatedQuotas, tempQuota];
    setModifiedQuotas(finalQuotas);
    setTempQuota(undefined);
  }, [tempQuota]);

  const [rawInputs, setRawInputs] = useState<Record<string, number>>({});
  const handleEntryChange = (updatedEntry: CoraQuota, newValue: number) => {
    const maxQuota = updatedEntry.quota_open;
    const key = updatedEntry.quota_month; // Unique identifier

    const updatedInputs = {
      ...rawInputs,
      [key]: newValue,
    };
    const totalQuota = Object.values(updatedInputs).reduce((sum, value) => sum + value, 0);

    // Check if total quota exceeds the limit of selectable quota
    if (totalQuota > MAX_SELECTABLE_QUOTA) {
      setError(t('copy_order_error_quota_limit'));
      return;
    }
    setRawInputs(updatedInputs);

    if (newValue <= maxQuota) {
      const areAllInputsValid = Object.keys(rawInputs).every((monthKey) => {
        if (monthKey === key) return true; // Skip the current input being checked

        // Find the matching quota entry for the current month key
        const matchingQuota = nextSixMonthsQuotas.find((entry) => entry.quota_month === monthKey);

        // Check if the matchingQuota is defined before comparing
        if (matchingQuota) {
          return rawInputs[monthKey] <= matchingQuota.quota_open;
        }

        // Return false if no matching quota is found, to indicate an invalid state
        return false;
      });
      if (areAllInputsValid) {
        setError(undefined);
      } else {
        setError(t('copy_order_error_quota'));
      }
      setIsModified(true);
      const tempQuota: CoraNCOCopyApiRequest = {};
      tempQuota[updatedEntry.quota_month] = newValue;
      setTempQuota(tempQuota);
    } else {
      setError(t('copy_order_error_quota'));
    }
  };

  return (
    <>
      <div className="form-container1">
        {isLoading || isFetching ? (
          <div className="loading-row">
            <PTextFieldWrapper label={t('loading_quota')}>
              <input type="text" value={t('loading')} disabled={true} />
            </PTextFieldWrapper>
          </div>
        ) : (
          nextSixMonthsQuotas.map((entry, index) => (
            <div className="field-row1" key={index}>
              <div className="field-col1">
                <PTextFieldWrapper label={t('copy_order_quota_month')}>
                  <input type="text" placeholder="test" value={entry.quota_month} disabled={true} />
                </PTextFieldWrapper>
              </div>
              <div className="field-col1">
                <PTextFieldWrapper label={t('copy_order_available_month')}>
                  <input type="number" placeholder={t('amount')} value={entry.quota_open} disabled={true} />
                </PTextFieldWrapper>
              </div>
              <div className="field-col2">
                <PTextFieldWrapper label={t('copy_order_input_month')}>
                  <input
                    data-e2e={`quota-month-${index}`}
                    type="text"
                    value={rawInputs[entry.quota_month] ?? ''}
                    disabled={false}
                    onChange={(e) => {
                      const inputValue = e.target.value;
                      if (inputValue === '') {
                        handleEntryChange(entry, 0);
                      } else {
                        handleEntryChange(entry, parseInt(inputValue, 10));
                      }
                    }}
                    max={entry.quota_open}
                  />
                </PTextFieldWrapper>
              </div>
            </div>
          ))
        )}
      </div>
    </>
  );
};
