import { DataSource, Repository } from 'typeorm';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createSqsEvent,
  initDataSourceForIntTest,
  invokeGenericLambda,
  prepareDynamodb,
} from '../../../../../utils/integration-test-helpers';
import { v4 as uuidv4 } from 'uuid';
import { CoraPurchaseIntentionModel } from '../../../../../../lib/entities/purchase-intention-model';
import { OneVmsSourceSystemKey, SQSBatchResponseWithError } from '../../../../../../lib/types/process-steering-types';
import { CssCreateSaleEvent, CssSaleEventType, CssUpdateSaleEvent } from '../../../../../../lib/types/css-types';
import { <PERSON><PERSON>d<PERSON><PERSON><PERSON>, CoraMdImporter } from '../../../../../../lib/types/masterdata-types';
import { Constants } from '../../../../../../lib/utils/constants';

const lambdaArn = buildLambdaArn('upsert-css-pi-data');
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;

// Test data
const mdImporter: CoraMdImporter = {
  pk_importer_number: 'CSS_IT_IMP',
  code: 'CS',
  display_name: 'IntegrationTest - Importer',
  port_codes: ['CSS_PC_IMP'],
  modified_at: '2023-10-04T14:16:30.079Z',
  modified_by: 'IntegrationTest',
};

const mdDealer: CoraMdDealer = {
  pk_importer_number: mdImporter.pk_importer_number,
  sk_dealer_number: 'CSS_IT_DLR1',
  display_name: 'IntegrationTest - DEALER1',
  standard_port_code: 'CSS_PC_DLR',
  modified_at: '2023-10-04T14:16:30.079Z',
  modified_by: 'IntegrationTest',
};

const mdDealerWithoutPort: CoraMdDealer = {
  pk_importer_number: mdImporter.pk_importer_number,
  sk_dealer_number: 'CSS_IT_DLR2',
  display_name: 'IntegrationTest - DEALER2',
  modified_at: '2023-10-04T14:16:30.079Z',
  modified_by: 'IntegrationTest',
};

const unconvertedPi: CoraPurchaseIntentionModel = {
  purchase_intention_id: uuidv4(),
  dealer_number: mdDealer.sk_dealer_number,
  importer_number: mdImporter.pk_importer_number,
  importer_code: mdImporter.code,
  model_type: 'EXISTING_PI_MT',
  model_year: '2024',
  cnr: 'C23',
  quota_month: '2024-01',
  order_type: 'KF',
  shipping_code: null,
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTest',
  modified_by: 'IntegrationTest',
  vehicle_status_code: 'V070',
  is_converted: false,
  vehicle_configuration_pvmsnext: null,
  vehicle_configuration_onevms: { ordered_options: [{ option_id: 'unconvertedOption' }] },
  seller: '123456',
  business_partner_id: '0020000866',
};

const convertedPi: CoraPurchaseIntentionModel = {
  ...unconvertedPi,
  purchase_intention_id: uuidv4(),
  model_type: 'MTDUMMY2',
  is_converted: true,
  vehicle_configuration_onevms: { ordered_options: [{ option_id: 'convertedOption' }] },
};

// SQS Test events
const createSaleCssEvent: CssCreateSaleEvent = {
  transaction_id: uuidv4(),
  saleId: uuidv4(),
  dealer: {
    partnerNumber: mdDealer.sk_dealer_number,
    entryDn: '123456',
    importerPartnerNumber: mdImporter.pk_importer_number,
  },
  customer: {
    businessPartnerId: '123456',
    type: 'PRIVATE',
  },
  meta: {
    eventType: CssSaleEventType.ConfiguredVehicleSold,
    eventTime: new Date().toISOString(),
    userType: 'PPN',
    userId: 'IntegrationTest',
  },
  configuration: { ordered_options: [{ option_id: 'newSaleOption' }] },
  quotaMonth: '2024-01',
  salesPerson: '1500005711',
  orderType: 'MTDUMMY1',
  modelYear: '2023',
  cNr: 'C23',
};

const updateSaleCssEvent: CssUpdateSaleEvent = {
  ...createSaleCssEvent,
  transaction_id: uuidv4(),
  saleId: unconvertedPi.purchase_intention_id,
  meta: {
    ...createSaleCssEvent.meta,
    eventType: CssSaleEventType.ConfigurationChanged,
  },
  updatedConfiguration: { ordered_options: [{ option_id: 'updateSaleOption' }] },
  orderType: 'UPDATE_PI_MT',
};

const otChangeSaleCssEvent: CssUpdateSaleEvent = {
  ...updateSaleCssEvent,
  transaction_id: uuidv4(),
  saleId: unconvertedPi.purchase_intention_id,
  meta: {
    ...updateSaleCssEvent.meta,
    eventType: CssSaleEventType.NewStockVehicleSold,
  },
  updatedConfiguration: { ordered_options: [{ option_id: 'otChangeSaleOption' }] },
};

const piIds = [unconvertedPi.purchase_intention_id, convertedPi.purchase_intention_id, createSaleCssEvent.saleId];

let dataSource: DataSource;
let piRepo: Repository<CoraPurchaseIntentionModel>;

describe('CSS PI Upsert Event Handler Integration Test', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([CoraPurchaseIntentionModel]);
    piRepo = dataSource.getRepository(CoraPurchaseIntentionModel);

    await prepareDynamodb([
      {
        tableName: mdDlrTableName,
        objs: [mdDealer, mdDealerWithoutPort],
      },
      { tableName: mdImpTableName, objs: [mdImporter] },
    ]);
  });

  afterAll(async () => {
    await dataSource.destroy();
    await cleanupDynamodb([
      {
        tableName: mdDlrTableName,
        pks: [
          {
            pk_importer_number: mdDealer.pk_importer_number,
            sk_dealer_number: mdDealer.sk_dealer_number,
          },
          {
            pk_importer_number: mdDealerWithoutPort.pk_importer_number,
            sk_dealer_number: mdDealerWithoutPort.sk_dealer_number,
          },
        ],
      },
      {
        tableName: mdImpTableName,
        pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
      },
    ]);
  });

  beforeEach(async () => {
    // Save/reset test data
    await piRepo.save([unconvertedPi, convertedPi]);
  });

  afterEach(async () => {
    // Cleanup test data
    await piRepo.delete(piIds);
  });

  it('Should fail if SQS event is faulty', async () => {
    const event = createSqsEvent([
      {
        ...createSaleCssEvent,
        meta: undefined, // Make event invalid
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was NOT created
    const pi = await piRepo.findOneBy({ purchase_intention_id: createSaleCssEvent.saleId });
    expect(pi).toBeNull();
  });

  it('Should fail if Create Sale event does not include a configuration', async () => {
    const event = createSqsEvent([
      {
        ...createSaleCssEvent,
        configuration: undefined,
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was NOT created
    const pi = await piRepo.findOneBy({ purchase_intention_id: createSaleCssEvent.saleId });
    expect(pi).toBeNull();
  });

  it('Should fail if Create Sale event does not include a valid importer number', async () => {
    const event = createSqsEvent([
      {
        ...createSaleCssEvent,
        dealer: {
          ...createSaleCssEvent.dealer,
          importerPartnerNumber: 'invalid_importer',
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was NOT created
    const pi = await piRepo.findOneBy({ purchase_intention_id: createSaleCssEvent.saleId });
    expect(pi).toBeNull();
  });

  it('Should fail if Update Sale event does not include a configuration', async () => {
    const event = createSqsEvent([
      {
        ...updateSaleCssEvent,
        updatedConfiguration: undefined,
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was NOT updated
    const pi = await piRepo.findOneBy({ purchase_intention_id: unconvertedPi.purchase_intention_id });
    expect(pi).toBeDefined();
    expect(pi?.vehicle_configuration_onevms).toEqual(unconvertedPi.vehicle_configuration_onevms);
  });

  it('Should fail if Update Sale event is for converted PI', async () => {
    const event = createSqsEvent([
      {
        ...updateSaleCssEvent,
        saleId: convertedPi.purchase_intention_id,
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI wasn't changed
    const pi = await piRepo.findOneBy({ purchase_intention_id: convertedPi.purchase_intention_id });
    expect(pi).toBeDefined();
    expect(pi?.is_converted).toBe(true);
    expect(pi?.vehicle_configuration_onevms).toEqual(convertedPi.vehicle_configuration_onevms);
  });

  it('Should fail if Sale event is of unsupported type', async () => {
    const event = createSqsEvent([otChangeSaleCssEvent]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI wasn't changed
    const pi = await piRepo.findOneBy({ purchase_intention_id: unconvertedPi.purchase_intention_id });
    expect(pi).toBeDefined();
    expect(pi?.is_converted).toBe(false);
    expect(pi?.vehicle_configuration_pvmsnext).toBeNull();
    expect(pi?.vehicle_configuration_onevms).toEqual(unconvertedPi.vehicle_configuration_onevms);
  });

  it('Should successfully create new PI and take port code from Dealer', async () => {
    const event = createSqsEvent([createSaleCssEvent]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was created
    const pi = await piRepo.findOneBy({ purchase_intention_id: createSaleCssEvent.saleId });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toBe(false);
    expect(pi?.created_by).toBe(OneVmsSourceSystemKey.CSS);
    expect(pi?.modified_by).toBe(OneVmsSourceSystemKey.CSS);
    expect(pi?.model_type).toBe(createSaleCssEvent.orderType);
    expect(pi?.receiving_port_code).toBe(mdDealer.standard_port_code);
    expect(pi?.vehicle_configuration_pvmsnext).toBeNull();
    expect(pi?.vehicle_configuration_onevms).toEqual(createSaleCssEvent.configuration);
  });

  it('Should successfully create new PI and take port code from Importer', async () => {
    const event = createSqsEvent([
      {
        ...createSaleCssEvent,
        dealer: { ...createSaleCssEvent.dealer, partnerNumber: mdDealerWithoutPort.sk_dealer_number },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was created
    const pi = await piRepo.findOneBy({ purchase_intention_id: createSaleCssEvent.saleId });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toBe(false);
    expect(pi?.created_by).toBe(OneVmsSourceSystemKey.CSS);
    expect(pi?.modified_by).toBe(OneVmsSourceSystemKey.CSS);
    expect(pi?.model_type).toBe(createSaleCssEvent.orderType);
    expect(pi?.receiving_port_code).toBe(mdImporter.port_codes?.[0]);
    expect(pi?.vehicle_configuration_pvmsnext).toBeNull();
    expect(pi?.vehicle_configuration_onevms).toEqual(createSaleCssEvent.configuration);
  });

  it('Should successfully update (only) config of existing PI that is not converted', async () => {
    const event = createSqsEvent([updateSaleCssEvent]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    // Check response
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Verify PI was updated
    const pi = await piRepo.findOneBy({ purchase_intention_id: unconvertedPi.purchase_intention_id });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toBe(false);
    expect(pi?.modified_by).toBe(OneVmsSourceSystemKey.CSS);
    expect(pi?.model_type).toBe(unconvertedPi.model_type);
    expect(pi?.receiving_port_code).toBe(unconvertedPi.receiving_port_code);
    expect(pi?.vehicle_configuration_pvmsnext).toBeNull();
    expect(pi?.vehicle_configuration_onevms).toEqual(updateSaleCssEvent.updatedConfiguration);
  });
});
