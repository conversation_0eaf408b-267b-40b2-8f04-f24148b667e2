import {
  buildLambdaArn,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
} from '../../../utils/integration-test-helpers';
import { DataSource, Repository } from 'typeorm';
import { FailedStatusMappingOrderDTO } from '../../../../lib/types/failed-status-mapping-orders';
import { FailedStatusMappingOrdersModel } from '../../../../lib/entities/failed-status-mapping-orders-model';

// Set up test object data
const lambdaArn = buildLambdaArn('fetch-failed-status-order-data');
export const testFailedOrderListObject: FailedStatusMappingOrdersModel = {
  vehicle_status_pvms_code: 'test',
  order_status_pvms_code: 'test',
  key: 'order-67890',
  num_of_tries: 3,
  timestamp: '2004-10-19 10:23:54+02',
  value: {
    ids: {
      new_car_order_id: 'NC12345',
      vguid_pvms_DEPRECATED: 'VGUID123',
      internal_vehicle_number_pvms_DEPRECATED: 'IVN123',
      commission_id: 'CID123',
      production_number_porsche: 'PNP123',
      production_number_vw: 'PNV123',
      vehicle_identification_number: 'VIN123456789',
      vbto_id: 'VBT123',
      kate_id: 'KATE123',
      lead_id: 'LEAD123',
      business_partner_id: 'BP123',
      tequipment_id: 'TEQ123',
      order_reservation_id: 'OR123',
      porsche_code: 'PC123',
    },
    model_info: {
      model_year_code: 'MYC2024',
      model_year: 2024,
      model_type: 'Model X',
      material_sap_DEPRECATED: 'MSAP123',
      country_code: 'US',
    },
    order_info: {
      base_info: {
        fiscal_year: 2024,
        quota_month: '2024-01',
        order_type: 'new',
        is_ordered_by_dealer: true,
        is_locator_visible: false,
        locator_transfer_status: 'pending',
      },
      planning: {
        blocking_reason_planning: '',
      },
      trading_partner: {
        importer_code: 'IMP123',
        importer_number: 'IMP456',
        dealer_sold_to_number: 'DST789',
        dealer_ship_to_number: 'DST012',
      },
      status_info: {
        vehicle_status_pvms_code: 'V180',
        vehicle_status_pvms_timestamp: '2024-01-01T00:00:00Z',
        order_status_pvms_code: 'OSP123',
        order_status_pvms_timestamp: '2024-01-02T00:00:00Z',
      },
      sales_info: {
        sales_person_id: 'SP123',
        sales_person_last_name: 'Doe',
        sales_person_first_name: 'John',
        cancellation_reason: 'Customer request',
        customer_cancellation_reason: 'Changed mind',
      },
      delivery_info: {
        mileage: 100,
        mileage_unit: 'miles',
        homologation_number: 'HN123',
        license_plate_number: 'LPN456',
        has_follow_up_warranty: true,
        has_roadside_assistance: false,
      },
    },
    logistics_info: {
      shipping_code: 'SC123',
      shipping_block: 'SB001',
      incoterms: 'DAP',
      current_location_code: 'LOC001',
      next_location_code: 'LOC002',
      receiving_port_code: 'RPC123',
      vessel_imo_number: 'IMO1234567',
      vessel_name: 'Vessel X',
      bill_of_lading_number: 'BLN123',
    },
    production_info: {
      production_plant: 'Plant 1',
    },
    appointment_date_info: {
      production_logistic_dates: {
        order_creation_date: '2024-01-01',
        change_freeze_point_date: '2024-01-05',
        expected_cp_80_date: '2024-01-10',
        expected_dealer_delivery_date: '2024-01-15',
        requested_dealer_delivery_date: '2024-01-20',
        actual_dealer_delivery_date: '2024-01-25',
      },
      customer_order_dates: {
        customer_order_intake_cancellation_date: '2024-01-03',
      },
      process_specific: {
        factory_pickup_status: 'Ready',
      },
    },
    vehicle_technical_info: {
      general: {
        combustion_engine_number: 'ENG123',
        engine_order_type: 'V8',
        tire_code: 'TIRE123',
        key_code: 'KEY123',
        transmission_number: 'TRANS123',
        transmission_order_type: 'Automatic',
        weight_front_axle: '500kg',
        weight_rear_axle: '600kg',
        emission_standard: 'Euro 6',
      },
    },
    ordered_config_options: [
      {
        option_code: 'OP123',
        option_id: 'OID456',
        option_type_short: 'TypeShort',
        option_type_long: 'TypeLong',
        reference_package: 'RP123',
        package_option_type: 'POT123',
        option_text: 'Sunroof',
      },
      {
        option_code: 'OP789',
        option_id: 'OID101',
        option_type_short: 'TypeShort2',
        option_type_long: 'TypeLong2',
        reference_package: 'RP456',
        package_option_type: 'POT456',
        option_text: 'Leather seats',
      },
    ],
  },
};

describe('Insert and retrieve FailedOrderListObject', () => {
  let dataSource: DataSource;
  let repository: Repository<FailedStatusMappingOrdersModel>;

  beforeAll(async () => {
    try {
      dataSource = await initDataSourceForIntTest([FailedStatusMappingOrdersModel]);
      repository = dataSource.getRepository(FailedStatusMappingOrdersModel);
    } catch (error) {
      console.error('Error during beforeAll:', error);
    }
  });
  afterAll(async () => {
    await dataSource.destroy();
  });
  const testKey = 'order-67890';

  beforeEach(async () => {
    await repository.save(testFailedOrderListObject);
  });

  afterEach(async () => {
    await repository.delete({ key: testKey });
  });

  test('should save and retrieve FailedOrderListObject', async () => {
    const event = createApiGwEvent({});
    const res = await invokeApiGwLambda(lambdaArn, event);
    expect(res.statusCode).toStrictEqual(200);

    const body = JSON.parse(res.body) as FailedStatusMappingOrderDTO[];
    expect(body).not.toBeNull();
    expect(body.length).toBeGreaterThan(0);
  }, 20000);
});
