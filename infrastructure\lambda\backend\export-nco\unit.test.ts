import { GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { mockContext, quotaApiSecret, setupMocks } from '../../utils/test-utils';
import audit_trail_test_data from '../../../test/data/new-car-order-audit-models.json';
import { KafkaAdapter } from '../../utils/kafka';
const pushObjsToTopicMock = jest.fn();
const mocks = setupMocks();
jest.mock('../../utils/kafka');
(KafkaAdapter as jest.MockedClass<typeof KafkaAdapter>).mockImplementation(() => {
  return {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    pushObjsToTopic: pushObjsToTopicMock,
  } as unknown as KafkaAdapter;
});

import { handler } from '.';
import { SQSEvent } from 'aws-lambda';
import { KafkaObjs<PERSON>rapper, NewCarOrderKafkaObject } from './types';

beforeEach(() => {
  mocks.ddbMock!.reset();
  mocks.smMock!.reset();
  jest.resetModules();
  process.env.STAGE = 'dev';
});

describe('When called with correct input and data in db it should be io', () => {
  beforeEach(() => {
    mocks.smMock!.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(quotaApiSecret) });
  });
  it('audit trail io happy flow', async () => {
    await handler(
      {
        Records: [
          {
            messageId: '1f34366b-34a1-43df-83c7-df12dd7d3f54',
            receiptHandle: 'AQEBJQ+2P...==',
            body: JSON.stringify({
              pk_new_car_order_id: audit_trail_test_data[0].pk_new_car_order_id,
              action_at: audit_trail_test_data[0].action_at,
              action_correlation_id: audit_trail_test_data[0].action_correlation_id,
            }),
            attributes: {
              ApproximateReceiveCount: '1',
              SentTimestamp: '1609459200000',
              SenderId: 'AIDAINPONIXYEXAMPLE',
              ApproximateFirstReceiveTimestamp: '1609459200001',
            },
            messageAttributes: {},
            md5OfBody: '9bb58f26192e4ba00f01e2e7b136bbd8',
            eventSource: 'aws:sqs',
            eventSourceARN: 'arn:aws:sqs:us-east-1:123456789012:my-queue',
            awsRegion: 'us-east-1',
          },
        ],
      } satisfies SQSEvent,
      mockContext,
    );
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const calls = pushObjsToTopicMock.mock.calls.flat() as {
      kWrapper: KafkaObjsWrapper<NewCarOrderKafkaObject>;
      topic: string;
      correlationid: string;
    }[];
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const pvms_call = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_PVMS);
    const onevms_call = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_ONE_VMS);
    expect(pvms_call?.kWrapper.kafkaObjs[0].obj.configuration_expire).toBeDefined();
    expect(onevms_call?.kWrapper.kafkaObjs[0].obj.configuration_expire).not.toBeDefined();
  });
});
