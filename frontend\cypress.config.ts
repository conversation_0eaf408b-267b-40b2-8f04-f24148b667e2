import { defineConfig } from 'cypress';
import { tasks } from './cypress/support/tasks';

export default defineConfig({
  e2e: {
    setupNodeEvents(on, config) {
      require('cypress-terminal-report/src/installLogsPrinter')(on);
      on('task', {
        ...tasks,
      });
    },
    testIsolation: false,
    retries: 2,
    baseUrl: 'https://cora-dev.dpp.porsche.com',
    video: true,
    viewportHeight: 1080,
    viewportWidth: 1920,
    chromeWebSecurity: true,
    scrollBehavior: 'center',
  },
});
