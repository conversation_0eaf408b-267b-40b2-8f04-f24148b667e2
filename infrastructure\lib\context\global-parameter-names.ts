import { Constants } from '../utils/constants';

export interface GlobalParameterNames {
  newCarOrderTableStreamArnPName: string;
  bossOrgTableStreamArnPName: string;
  kafkaSecretArnPName: string;
  quotaApiSecretArnPName: string;
  configApiSecretArnPName: string;
  purchaseIntentionsTableStreamArnPName: string;
  healthMonitoringTopicArnPName: string;
  globalDynamoKmsKeyArnPName: string;
  logGroupKmsKeyArnPName: string;
  globalDynamoMdKmsKeyArnPName: string;
  globalS3MdKmsKeyArnPName: string;
  globalS3KmsKeyArnPName: string;
  typeORMLambdaLayerArnPName: string;
  globalSecretKmsKeyArnPName: string;
  globalMdSecretKmsKeyArnPName: string;
  healthMonitoringTopicKmsKeyArnPName: string;
  syncPVMSOrderDataQueueArnPName: string;
  globalSqsKmsKeyArnPName: string;
  globalSessionManagerKmsKeyArnPname: string;
  auroraSecurityGroupPName: string;
  auroraAccessSecurityGroupPName: string;
  externalAccessSecurityGroupPName: string;
  kafkaSecurityGroupPName: string;
  vpcEndpointsSecurityGroupPName: string;
  auroraAdminSecretArnPName: string;
  coraAuroraWriterSecretArnPName: string;
  coraAuroraReaderSecretArnPName: string;
  coraMdAuroraWriterSecretArnPName: string;
  coraMdAuroraReaderSecretArnPName: string;
  auroraClusterArnPName: string;
  globalNcoExportQueueArnPName: string;
  eventDispatcherInboundQueueArnPName: string;
}

export const getGlobalParameterNames = (stage: string): GlobalParameterNames => ({
  newCarOrderTableStreamArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Dynamo/ncoStreamArn`,
  bossOrgTableStreamArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Dynamo/bossOrgStreamArn`,
  kafkaSecretArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Kafka/secretArn`,
  quotaApiSecretArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Quota/secretArn`,
  configApiSecretArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/OmConfig/secretArn`,
  purchaseIntentionsTableStreamArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Dynamo/purchaseIntentionsTableStreamArn`,
  healthMonitoringTopicArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/healthMonitoringTopicArn`,
  globalDynamoKmsKeyArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/dynamoKmsKeyArn`,
  logGroupKmsKeyArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/logGroupKmsKeyArn`,
  globalDynamoMdKmsKeyArnPName: `/cora-md/${stage}/Global/globalDynamoKmsKeyArn`,
  globalS3MdKmsKeyArnPName: `/cora-md/${stage}/Global/globalS3KmsKeyArn`,
  globalS3KmsKeyArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/globalCoraS3KmsKeyArn`,
  typeORMLambdaLayerArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Lambda/typeORMLambdaLayerArn`,
  globalSecretKmsKeyArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/secretKmsKeyArn`,
  globalMdSecretKmsKeyArnPName: `/${Constants.CORA_MD_APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/globalSecretKmsKeyArn`,
  healthMonitoringTopicKmsKeyArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/healthMonitoringTopicKmsKeyArn`,
  syncPVMSOrderDataQueueArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/syncPVMSOrderDataQueueArn`,
  globalSqsKmsKeyArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/globalSqsKmsKeyArn`,
  globalSessionManagerKmsKeyArnPname: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/sessionManagerKmsKeyArn`,
  auroraSecurityGroupPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/auroraSecurityGroupID`,
  auroraAccessSecurityGroupPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/auroraAccessSecurityGroupID`,
  externalAccessSecurityGroupPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/externalAccessSecurityGroupID`,
  kafkaSecurityGroupPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/kafkaSecurityGroupID`,
  vpcEndpointsSecurityGroupPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/vpcEndpointsSecurityGroupID`,
  auroraAdminSecretArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/auroraAdminSecretArn`,
  coraAuroraWriterSecretArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/coraAuroraWriterSecretArn`,
  coraAuroraReaderSecretArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/coraAuroraReaderSecretArn`,
  coraMdAuroraWriterSecretArnPName: `/${Constants.CORA_MD_APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/mdAuroraWriterSecretArn`,
  coraMdAuroraReaderSecretArnPName: `/${Constants.CORA_MD_APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/mdAuroraReaderSecretArn`,
  auroraClusterArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Aurora/auroraClusterArn`,
  globalNcoExportQueueArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/Global/globalNcoExportQueueArn`,
  eventDispatcherInboundQueueArnPName: `/${Constants.APPLICATION_SHORT_NAME.toLocaleLowerCase()}/${stage}/ProcSteer/eventDispatcherInboundQueueArn`,
});
