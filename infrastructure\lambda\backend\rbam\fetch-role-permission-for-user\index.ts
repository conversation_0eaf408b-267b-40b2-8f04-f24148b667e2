import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { HttpError } from '../../../utils/http-client';
import { getPpnId, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { Constants } from '../../../../lib/utils/constants';
import { QueryCommand, QueryCommandInput } from '@aws-sdk/lib-dynamodb';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

const dynamoDBClient = new DynamoDBClient({});

const rbamTableName = getEnvVarWithAssert('RBAM_TABLE_NAME');

const fetchRolePermissionForUserFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  logger.log(LogLevel.TRACE, 'lambda execution started', { data: event });
  const ppnUserId = getPpnId({ event }, logger);
  logger.log(LogLevel.INFO, `Got request for ppnUserId ${ppnUserId ?? 'undefined'}`);

  // logger.setObjectId(ppnUserId);

  const userData = JSON.parse(event.requestContext.authorizer?.userAttributes as string) as KasAuthEndpointResponse;
  logger.log(LogLevel.DEBUG, 'User data', { data: userData });

  const applicationRole = userData.kasApplications[Constants.APPLICATION_NAME_TO_AUTHORIZE][0].role;

  if (!applicationRole) {
    logger.log(LogLevel.WARN, 'Could not find application role in user data', { data: userData });
    return sendFail({ message: 'Missing user Data', status: 500, reqHeaders: event.headers }, logger);
  }
  logger.log(LogLevel.DEBUG, `Application role for user is ${applicationRole}`);

  if (!ppnUserId) {
    //return 401 if ppnUserId is not present
    logger.log(LogLevel.WARN, 'Could not find ppnUserId in request context', { data: event.requestContext });
    return sendFail({ message: 'Missing user Data', status: 500, reqHeaders: event.headers }, logger);
  }

  try {
    const queryParams: QueryCommandInput = {
      TableName: rbamTableName,
      KeyConditionExpression: 'applicationRole = :appRole AND applicationName = :appName',
      FilterExpression: 'attribute_not_exists(is_deactivated) OR is_deactivated = :is_deactivated',
      ExpressionAttributeValues: {
        ':appRole': applicationRole,
        ':appName': Constants.APPLICATION_NAME_TO_AUTHORIZE,
        ':is_deactivated': false,
      },
    };
    const result = await dynamoDBClient.send(new QueryCommand(queryParams));

    logger.log(LogLevel.DEBUG, 'Got result from dynamodb', { data: result });

    if (!result.Items || result.Items.length === 0) {
      logger.log(LogLevel.WARN, 'No data found for the user');
      return sendFail({ message: 'No data found for the user', status: 404, reqHeaders: event.headers }, logger);
    }
    if (result.Items.length > 1) {
      logger.log(LogLevel.WARN, 'More than one entry returned for the user role. This should not happen');
      return sendFail(
        { message: 'More than one role found for the user', status: 500, reqHeaders: event.headers },
        logger,
      );
    }
    return sendSuccess({ body: { data: result.Items }, reqHeaders: event.headers }, logger);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error occurred while getting data for the user', { data: error });
    if (error instanceof HttpError) {
      return sendFail({ message: error.message, status: 500, reqHeaders: event.headers }, logger);
    }
    logger.log(LogLevel.FATAL, 'Unexpected error occurred', { data: error });
    throw error;
  }
};
export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-role-permission-for-user', LogLevel.TRACE)(event, context, fetchRolePermissionForUserFunc);
