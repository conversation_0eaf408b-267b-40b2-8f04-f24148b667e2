import { CoraStackProps } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { getGlobalParameterNames } from './global-parameter-names';

const stage = 'prod';

export const prodProps: CoraStackProps = {
  stage,
  hostedZoneName: 'cora.dpp.porsche.com',
  paddockDomainName: 'paddock.dpp.porsche.com',
  mail: Constants.MAIL,
  owner: Constants.OWNER,
  useCase: Constants.USE_CASE,
  env: {
    account: '************',
    region: Constants.DEFAULT_REGION,
  },
  idp: {
    domain: 'https://ppn.porsche.com',
    loginUrl: 'https://ppn.porsche.com/as/authorization.oauth2',
    tokenUrl: 'https://ppn.porsche.com/as/token.oauth2',
    clientId: '63c10520-6533-4a4f-8a36-f048fce0a09f',
    publicKeyUrl: 'https://ppn.porsche.com/pf/JWKS',
    issuer: 'https://ppn.porsche.com',
  },
  applicationNameToAuthorize: Constants.APPLICATION_NAME_TO_AUTHORIZE,
  ppnRolesWrite: [
    'ppn_approle_pag_operations',
    //'ppn_approle_boss_dev_basic_application_role',
    //'ppn_approle_kas_importer_dev',
    //'ppn_approle_kas_dealer_dev',
  ],
  kasAuthEndpointUrl: 'https://kasauth.kas.dpp.porsche.com',
  globalParameterNames: getGlobalParameterNames(stage),
  kafkaParameters: {
    brokers: ['pkc-zxm13.eu-west-1.aws.confluent.cloud:9092'],
    newCarOrderTopicOneVms: 'FRA_one_vms_cora_new_car_order',
    newCarOrderTopicPvms: 'FRA_one_vms_cora_new_car_order_pvms',
    newCarOrderTopicP06: 'FRA_one_vms_cora_new_car_order_p06',
    newCarOrderTopicConfigRequest: 'FRA_one_vms_kcc_translate_request',
    newCarOrderTopicConfigResponse: 'FRA_one_vms_kcc_translate_response',
    bossOrgTopic: 'FRA_kas_hub_boss_kas_organizations',
    hubModelTypeVisibilityTopic: 'FRA_kas_hub_boss_model_type_visibilites',
    quotaTopic: 'FRA_one_vms_quota_dealer_quotas',
    pvmsOrderDataTopic: 'FRA_kas_hub_new_car_order_pvms',
    p06InboundTopic: 'FRA_kas_hub_new_car_order_pia',
    pccdModelTypeTextTopic: 'FRA_one_vms_pccd_inbound_model_type_text',
    ncoNotificationTopic: 'FRA_one_vms_cora_notifications_new_car_order',
  },
  alerting: {
    alertingEmails: ['<EMAIL>', Constants.MONITORING_EMAIL],
  },
  featureFlags: {
    copyOrder: true,
    updateNcoCoreData: false,
    reportRevokeTotalLoss: false,
    deallocateQuota: false,
    importerTransfer: false,
    handleDealerInventory: false,
    buySellTransfer: false,
    ncoInvoiceMapping: false,
    p06DataSync: false,
  },
};
