import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getPpnId, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert, scanAllFromTable } from '../../../utils/utils';
import { getAllAuthorizedOrgs, getPermissionForDealer } from '../../../utils/validation-helpers';
import { CoraMdShippingCode, CoraMdShippingCodeResponseItem } from '../../../../lib/types/masterdata-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const shippingCodesTableName = getEnvVarWithAssert('TABLE_NAME_SHIPPING_CODES');
const orgRelsTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');

const fetchAllShippingCodesFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  try {
    const dealerNumber = event.queryStringParameters ? event.queryStringParameters['dealer_number'] : undefined;
    if (!dealerNumber) {
      return sendFail({ message: 'Missing DealerNumber', status: 400, reqHeaders: event.headers }, logger);
    }
    //get the org id of the user from auth context
    const ppnId = getPpnId({ event }, logger);
    if (!ppnId) {
      logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: sanitizeApiGwEvent({ event }, logger) });
      return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
    }
    const authorizedOrgs = await getAllAuthorizedOrgs({ dynamoDb, orgRelsTableName, ppnId }, logger);
    const correspondingOrg = authorizedOrgs.find((org) => org.importer_number && org.dealer_number === dealerNumber);
    if (!correspondingOrg) {
      return sendFail(
        { message: `No Permissions or Org does not exists: ${dealerNumber}`, status: 400, reqHeaders: event.headers },
        logger,
      );
    }
    let allowed_shipping_codes: CoraMdShippingCodeResponseItem[] = [];
    const shippingCodes = await scanAllFromTable<CoraMdShippingCode>(
      { tableName: shippingCodesTableName, dynamoDb: dynamoDb },
      logger,
    );
    switch (getPermissionForDealer({ dealerNumber, authorizedOrgs }, logger)) {
      case 'Dealer':
        allowed_shipping_codes = shippingCodes.map((shippingCode) => {
          if (shippingCode.is_deactivated) {
            return { ...shippingCode, editable: false };
          }
          const importerIsIncluded = shippingCode.importers.includes(correspondingOrg.importer_number!);
          if (shippingCode.imp_is_blacklist && importerIsIncluded) {
            return { ...shippingCode, editable: false };
          }
          if (!shippingCode.imp_is_blacklist && !importerIsIncluded) {
            return { ...shippingCode, editable: false };
          }
          const dealerIsIncluded = shippingCode.dealers.includes(correspondingOrg.dealer_number!);
          if (shippingCode.dlr_is_blacklist && dealerIsIncluded) {
            return { ...shippingCode, editable: false };
          }
          if (!shippingCode.dlr_is_blacklist && !dealerIsIncluded) {
            return { ...shippingCode, editable: false };
          }
          return { ...shippingCode, editable: true };
        });
        break;
      case 'Importer':
        allowed_shipping_codes = shippingCodes.map((shippingCode) => {
          if (shippingCode.is_deactivated) {
            return { ...shippingCode, editable: false };
          }
          const importerIsIncluded = shippingCode.importers.includes(correspondingOrg.importer_number!);
          if (shippingCode.imp_is_blacklist && importerIsIncluded) {
            return { ...shippingCode, editable: false };
          }
          if (!shippingCode.imp_is_blacklist && !importerIsIncluded) {
            return { ...shippingCode, editable: false };
          }
          return { ...shippingCode, editable: true };
        });
        break;
      default:
        logger.log(LogLevel.INFO, `No Permissions or Org does not exists: ${dealerNumber}`);
        return sendFail(
          { message: `No Permissions or Org does not exists: ${dealerNumber}`, status: 400, reqHeaders: event.headers },
          logger,
        );
    }
    return sendSuccess({ body: allowed_shipping_codes, reqHeaders: event.headers }, logger);
  } catch (err) {
    logger.log(LogLevel.ERROR, 'Error getting shippingCodes', { data: err });
    return sendFail({ message: 'Error loading shippingCodes', status: 500, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-shipping-codes', LogLevel.TRACE)(event, context, fetchAllShippingCodesFunc);
