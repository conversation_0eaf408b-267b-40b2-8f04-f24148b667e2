{
  "ids": {
    "new_car_order_id": "DE12P75X",
    "business_partner_id": "202040670" // Optionales Datenfeld. Kommt über den PVMS purchase intention - nehmen wir mit und speichern den ab.
  },
  "order_info": {
    "base_info": {
      "quota_month": "2024-12",
      "order_type": "LF",
      "created_by": "P35839X",
      "last_modified_by": "P35839X", // Optionales Datenfeld
      "cancellation_reason": "A1" // Optionales Datenfeld
    },
    "trading_partner": {
      "importer_number": "9690000",
      "importer_code": "DE",
      "dealer_sold_to_number": "9691234",
      "dealer_ship_to_number": "9691234" // Kann ein abweichender dealer zum dealer_sold_to_number sein
    }
  },
  "model_info": {
    "model_type": "992850",
    "model_year": 2025,
    "country_code": "C05"
  },
  "logistics_info": {
    "shipping_code": "1",
    "receiving_port_code": "HFX" // Optionales Datenfeld
  },
  "appointment_date_info": {
    "production_logistic_dates": {
      "order_creation_timestamp": "2024-03-21T08:55:47.911Z",
      "order_last_modification_timestamp": "2024-03-21T08:55:47.911Z", // Optionales Datenfeld
      "requested_dealer_delivery_date": "2024-03-21" // Optionales Datenfeld
    }
  },
  "status_info": {
    "order_status_code": "PP2000", // Muss Datenfeld wird bei Auftragsanlage initial gefüllt durch CORA
    "order_status_timestamp": "2024-03-21T08:55:47.911Z", // Muss Datenfeld wird bei Auftragsanlage initial gefüllt durch CORA
    "order_error_status_code": "EBP100", // Optionales Datenfeld
    "order_error_status_timestamp": "2024-03-21T08:55:47.911Z", // wenn error code befüllt, dann muss Feld
    "order_invoice_status_code": "IV1000", // Optionales Datenfeld
    "order_invoice_status_timestamp": "2024-03-21T08:55:47.911Z" // wenn invoice code befüllt, dann muss Feld
  },
  "configuration": {
    // F-PVA, L-PVA Kennzeichnungen werden nicht mehr benoetigt.
    "ordered_options": [
      // alternative "ordered_options" / "ordered_configuration" ?
      {
        "option_id": "0I2",
        "option_type": "Standard" // enum: EXC, CXX, etc. analog PCCD --> Wie kommts den eigentlich aus der PCCD?
      },
      {
        "option_id": "Z1C",
        "option_type": "Local"
      },
      {
        "option_id": "480",
        "option_type": "EXC",
        "option_subtype": "color_list",
        "option_subtype_value": "4711" // Mambagruen
      },
      {
        "option_id": "PA1",
        "option_type": "Package",
        "content": [
          {
            "option_id": "4A3",
            "option_type": "Standard",
            "package_content_sort_order": 1
          },
          {
            "option_id": "3FE",
            "option_type": "EXC", // beispielhaft
            "package_content_sort_order": 2
          }
        ]
      },
      {
        "option_id": "480",
        "option_type": "EXC", // TBD. enum: EXC, cxx, etc.
        "referenced_package": "PA1",
        "referenced_package_type": "content", // enum: content, upgrade
        "referenced_package_sort_order": 3
      },
      {
        "option_id": "435",
        "option_type": "EXC", // TBD. enum: EXC, cxx, etc.
        "referenced_package": "PA1",
        "referenced_package_type": "upgrade", // enum: content, upgrade
        "referenced_package_sort_order": 4
      },
      {
        "option_id": "KA2",
        "option_type": "EXC", // TBD. enum: EXC, cxx, etc.
        "referenced_package": "PA1",
        "referenced_package_type": "upgrade", // enum: content, upgrade
        "referenced_package_sort_order": 5
      },
      {
        "option_id": "BYE",
        "option_type": "EXC", // TBD. enum: EXC, cxx, etc.
        "option_subtype": "TWIKIT", // enum: LENA, TWIKIT, etc.
        "option_subtype_value": "long_id_text"
      },
      {
        "option_id": "27054",
        "option_type": "Standard", // TBD. enum: exclusive, cxx, etc.
        "option_subtype": "LENA", // TBD: gibt's diese Info ueberhaupt?
        "option_subtype_value": "001/250"
      },
      {
        "option_id": "27013",
        "option_type": "Z-Antrag", // TBD. enum: exclusive, cxx, etc.
        "option_type_value": "lila-blass-blau-gestreift" // TBD wo ist die Aussenfarbe - config oder uebergeordnet --> Würden wir gerne in der COnfig belassen
      },
      {
        "option_id": "99",
        "option_type": "Exterior Color" // TBD. enum: exclusive, cxx, etc.
      },
      {
        "option_id": "A1",
        "option_type": "Interior Color" // TBD. enum: exclusive, cxx, etc.
      },
      {
        "option_id": "BF",
        "option_type": "Top Color" // TBD. enum: exclusive, cxx, etc.
      }
    ],
    "technical_options": [
      // future concepts ggf. noch anderen Namen definieren? resolved_configuration
      {
        "option_id": "911",
        "option_type": "S-PDM" // TBD welche Bezeichnung. enum: exc, cxx, "c-pdm", "g-pdm"
      }
    ]
  },
  "configuration_expire": {
    "ordered_configuration": [
      // Ablage der PVMS Next Struktur
    ],
    "resolved_configuration": [
      // Ablage der PVMS Next Struktur
    ]
  }
}
