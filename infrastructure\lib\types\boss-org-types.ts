type OrgStatus = 'CLOSED' | 'OPERATIVE' | 'PLANNED' | 'WHITE_SPOT' | 'ARCHIVED' | 'RAMP_UP' | null;

export interface BossOriginOrgModel {
  ppn_parent_id: string | null;
  ppn_business_type: string | null;
  ppn_importer_code: string | null;
  ppn_facility_type: string | null;
  ppn_investor_type: string | null;
  ppn_status: OrgStatus;
  ppn_company_name: string | null;
  ppn_display_name: string | null;
  ppn_porsche_partner_no: string | null;
  ppn_address_street: string | null;
  ppn_address_postalcode: string | null;
  ppn_address_city: string | null;
  ppn_address_state: string | null;
  ppn_address_country: string | null;
  ppn_pos_lat: number | null;
  ppn_pos_long: number | null;
  ppn_pos_region: string | null;
  ppn_pos_area: string | null;
  ppn_pos_market: string | null;
  ppn_loc_company_name: string | null;
  ppn_loc_display_name: string | null;
  ppn_loc_address_street: string | null;
  ppn_loc_address_city: string | null;
  ppn_loc_address_state: string | null;
  boss_start_of_operation: string | null;
  boss_end_of_operation: string | null;
  boss_modified_at: string;
  boss_predecessors: string[];
  boss_successors: string[];
  boss_is_deleted: boolean;
  boss_additional_children: string[];
  boss_org_children: string[] | null;
  ppn_importer_partner_number: string | null;
  ppn_importer_org_id: string | null;
  ppn_id: string;
  is_relevant_for_order_create: IsRelevantForOrderCreateOptions;
}

export type IsRelevantForOrderCreateOptions = 'true' | 'false' | 'undefined';
export interface BossOrgModel {
  pk_ppn_id: string;
  parent_ppn_id: string | null;
  additional_children: string[] | null;
  ppn_importer_partner_number: string | null;
  ppn_porsche_partner_no: string | null;
  ppn_status: OrgStatus;
  is_deactivated: boolean;
  display_name: string;
  is_relevant_for_order_create: IsRelevantForOrderCreateOptions;
}

export interface BossOrgModelKey {
  pk_ppn_id: string;
}

export interface BossOrgModelWithTime extends BossOrgModel {
  kafka_timestamp: number;
}

export interface BasisCOR {
  pk_ppn_id: string;
  importer_number?: string;
  dealer_number?: string;
  is_deactivated: boolean;
  display_name: string;
  ppn_status: OrgStatus;
  is_relevant_for_order_create: boolean;
}

export interface CoraOrgRelModel extends BasisCOR {
  parent_ppn_id: string;
}

export interface CORModelKey {
  ppn_id: string;
  parent_ppn_id: string;
}

export interface AuthorizedDealer {
  importer_display_name: string;
  dealer_number: string;
  display_name: string;
  importer_number: string;
  role: 'Dealer' | 'Importer';
  dealer_ppn_status: OrgStatus;
}
