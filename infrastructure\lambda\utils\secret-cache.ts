import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { GeneralError } from './errors';
import { Constants } from '../../lib/utils/constants';

export interface OAuthCredentials {
  base_url: string;
  token_url: string;
  client_id: string;
  client_secret: string;
  add_basic_auth_on_token_request?: boolean;
  scope?: string[];
  body?: Record<string, string>;
  headers: Record<string, string>;
}

export interface KafkaSecret {
  username: string;
  password: string;
}

export interface RdsSecret {
  host: string;
  port: string;
  username: string;
  password: string;
  dbname: string;
}

type Secret = KafkaSecret | OAuthCredentials | RdsSecret | Uint8Array;

class SecretCache {
  private readonly secretsCache = new Map<string, Promise<Secret>>();
  private readonly sm = new SecretsManagerClient({ region: Constants.DEFAULT_REGION });

  /**
   * Clears the cache and loads the given secrets
   * @param secretsArns the ARNs of the secrets to load
   */
  public initCache(...secretsArns: string[]): void {
    this.secretsCache.clear();
    secretsArns.forEach((secretArn) => {
      this.loadSecret(secretArn);
    });
  }

  /**
   * Returns the secret from the cache if available
   * @param secretArn the name of the secret
   * @param load true if the cache should load the secret if it does not exist. Otherwise, it will throw an error (default).
   */
  public async getSecret<T extends Secret>(secretArn: string, load = false): Promise<T> {
    if (!this.secretsCache.has(secretArn)) {
      if (load) {
        this.loadSecret(secretArn);
      } else {
        throw new GeneralError({
          message: `Secret '${secretArn}' was not fetched during initialization`,
        });
      }
    }
    return this.secretsCache.get(secretArn) as Promise<T>;
  }

  /**
   * Store a secret(request) in the cache
   * @param secretArn the name of the secret
   */
  private loadSecret(secretArn: string): void {
    if (!secretArn) return;
    const secret = this.fetchSecret(secretArn);
    this.secretsCache.set(secretArn, secret);
  }

  /**
   * Request a secret
   * @param secretArn the name of the secret
   */
  private async fetchSecret(secretArn: string): Promise<Secret> {
    try {
      const secretValue = await this.sm.send(
        new GetSecretValueCommand({
          SecretId: secretArn,
        }),
      );
      return secretValue.SecretString
        ? (JSON.parse(this.escape_newlines_in_data(secretValue.SecretString)) as Secret)
        : (secretValue.SecretBinary as Secret);
    } catch (error) {
      throw new GeneralError({
        message: `Fetching secret '${secretArn}' failed`,
        causedBy: error,
      });
    }
  }

  /**
   * Escape newlines in json data fields, e.g. in Certificate Secret used for AWS Lambda triggers
   * @param json_str json in stringformat with potentially malformed data (containing \n)
   */
  private escape_newlines_in_data(json_str: string): string {
    let _tmp_string = json_str.split('"');
    _tmp_string = _tmp_string.map((s, index) => {
      if (index % 2 !== 0) {
        s = s.replace(/\n/g, '\\\\n');
      }
      return s;
    });
    return _tmp_string.join('"');
  }
}

export const secretCache = new SecretCache();
