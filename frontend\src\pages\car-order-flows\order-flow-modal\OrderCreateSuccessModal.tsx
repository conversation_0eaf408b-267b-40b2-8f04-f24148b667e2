import { <PERSON><PERSON><PERSON><PERSON>, PButtonGroup, PLink } from '@porsche-design-system/components-react';
import './OrderFlowModal.css';
import { useTranslation } from 'react-i18next';
import { OrderFlowSuccessModal } from './OrderFlowSuccessModal';
import { CoraNCOBaseApiResponse } from '../../../../../infrastructure/lib/types/new-car-order-types';
import { useNavigate } from 'react-router-dom';
import { routes } from '../../../Constants';
import { clearConfig, setKccFocused } from '../../../store/slices/KccConfigSlice';
import { useDispatch } from 'react-redux';
import { useInIframe } from '../../../utils/useInIframe';
import { useGetStageConfigQuery } from '../../../store/api/StaticJsonApi';

interface OrderCreateSuccessModalProps {
  open?: boolean;
  createdOrder: CoraNCOBaseApiResponse;
}
export const OrderCreateSuccessModal: React.FC<OrderCreateSuccessModalProps> = ({ open, createdOrder }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const heading = t('order_created');
  const navigate = useNavigate();
  const { data: stageConfig } = useGetStageConfigQuery(undefined);
  const isInIframe = useInIframe();

  function goToOrderList() {
    dispatch(clearConfig(true));
    dispatch(setKccFocused(true));
    navigate(routes.lists.preProductionOrders);
  }

  const getQuotaDashboardUrl = (): string => {
    const quotaDashboardAppPath = isInIframe
      ? `${document.referrer}${routes.external_urls.quotaDashboardRelativePaddockPath}`
      : `${routes.external_urls.quotaDashboardFullPath(stageConfig?.stage)}`;
    return quotaDashboardAppPath;
  };

  const getDoriUrl = (ncoId: string): string => {
    const doriOrderDetailsPath = routes.external_urls.doriOrderDetailsRelativePath(ncoId);
    const doriAppPath = isInIframe
      ? `${document.referrer}${routes.external_urls.doriRelativePaddockPath}`
      : `${routes.external_urls.doriFullPath(stageConfig?.stage)}`;
    return doriAppPath + doriOrderDetailsPath;
  };

  const navigationButtonGroup = (
    <PButtonGroup className="footer">
      <PLink data-e2e="go_to_quota_btn" href={getQuotaDashboardUrl()} target={'_top'}>
        {t('navigate_to_quota_dashboard')}
      </PLink>
      <PButton data-e2e="go_to_orders_btn" onClick={goToOrderList} variant="secondary">
        {t('navigate_to_order_list')}
      </PButton>
      <PLink
        data-e2e="go_to_dori_btn"
        variant="secondary"
        href={getDoriUrl(createdOrder.pk_new_car_order_id)}
        target={'_top'}
      >
        {t('navigate_to_dori')}
      </PLink>
    </PButtonGroup>
  );

  return (
    <OrderFlowSuccessModal
      heading={heading}
      createdOrder={createdOrder}
      open={open}
      navigationButtonGroup={navigationButtonGroup}
      action="create"
    />
  );
};
