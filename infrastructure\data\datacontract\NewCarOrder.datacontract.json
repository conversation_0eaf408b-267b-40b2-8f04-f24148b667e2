{"dataContractSpecification": "artkas-0.10.0", "id": "NewCarOrder", "info": {"title": "NewCarOrder", "version": "1.0.0", "status": "active", "description": "NewCarOrder export object", "owner": "<PERSON>", "contact": {"name": "Team Heartbeat", "url": "https://skyway.porsche.com/confluence/display/ARTKAST/Team+HEARTBEAT", "email": "<EMAIL>", "department": "PSD"}, "community": "Kundenauftragssteuerung", "domain": "Auftragsmanagement", "datausage": "external"}, "servers": {"dev": {"type": "kafka", "host": "pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092", "topic": "FRA_kas_hub_new_car_order_DEV", "format": "json"}, "int": {"type": "kafka", "host": "pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092", "topic": "FRA_kas_hub_new_car_order", "format": "json"}, "prod": {"type": "kafka", "host": "pkc-zxm13.eu-west-1.aws.confluent.cloud:9092", "topic": "FRA_kas_hub_new_car_order", "format": "json"}}, "models": {"NewCarOrder": {"type": "object", "securityclassification": "external", "properties": {"ids": {"type": "object", "properties": {"new_car_order_id": {"type": "string", "description": "The id of the NewCarOrder", "required": true}, "commission_id": {"type": "string", "description": "The commission id from PVMS without an importer prefix.", "required": false}, "business_partner_id": {"type": "string", "description": "The id of the end customer from CRM.", "required": false}}}, "model_info": {"type": "object", "description": "Model information of the ordered vehicle.", "properties": {"model_type": {"type": "string", "description": "The code which describes the model type of the ordered vehicle.", "required": true}, "model_year": {"type": "number", "description": "The model year of the vehicle which the order was placed on.", "required": true}, "country_code": {"type": "string", "description": "The encryption code for the country specific configuration of the NewCarOrder.", "required": true}}}, "order_info": {"type": "object", "description": "Core information of the NewCarOrder.", "properties": {"base_info": {"type": "object", "properties": {"order_type": {"type": "string", "description": "The order type of the vehicle, wether it's customer related or not and how it will be in use.", "required": true}, "quota_month": {"type": ["string", "null"], "description": "Selected production month of the order.", "required": true}, "created_by": {"type": "string", "description": "The ppn id of the user who created this NewCarOrder.", "required": true}, "last_modified_by": {"type": "string", "description": "The ppn id of the user who lastly modified this NewCarOrder.", "required": false}, "cancellation_reason": {"type": "string", "description": "The selected cancellation reason, if the order was cancelled.", "required": false}}}, "trading_partner": {"type": "object", "properties": {"importer_code": {"type": "string", "description": "Identifier for the specific importer.", "required": true}, "importer_number": {"type": "string", "description": "The number of the importer of this order.", "required": true}, "dealer_sold_to_number": {"type": "string", "description": "The dealer number to whom the order was sold to.", "required": true}, "dealer_ship_to_number": {"type": "string", "description": "The dealer number to whom the order was shipped to.", "required": true}}}}}, "logistics_info": {"type": "object", "description": "Logistics information of the NewCarOrder.", "properties": {"shipping_code": {"type": "string", "description": "The distribution method/mode of the vehicle.", "required": true}, "receiving_port_code": {"type": "string", "description": "A short identifier for the port where the order is shipped to.", "required": false}}}, "status_info": {"type": "object", "description": "All relevant order status information.", "required": true, "properties": {"order_status_code": {"type": "string", "description": "Describes the state of the order status.", "required": true}, "order_status_timestamp": {"type": "string", "description": "The timestamp for the latest order status code change.", "required": true}, "order_error_status_code": {"type": "string", "description": "Describes the state of the order error status.", "required": false}, "order_error_status_timestamp": {"type": "string", "description": "The timestamp for the latest order error code change.", "required": false}, "order_invoice_status_code": {"type": "string", "description": "Describes the state of the order invoice status.", "required": false}, "order_invoice_status_timestamp": {"type": "string", "description": "The timestamp for the latest order invoice code change.", "required": false}}}, "appointment_date_info": {"type": "object", "description": "All relevant dates of the order.", "required": true, "properties": {"production_logistic_dates": {"type": "object", "properties": {"order_creation_timestamp": {"type": "string", "description": "The timestamp for the order creation date.", "required": true}, "order_last_modification_timestamp": {"type": "string", "description": "The timestamp for the latest modification of the order.", "required": false}, "requested_dealer_delivery_date": {"type": "string", "description": "The selected date on which the order should be delivered to the dealer.", "required": false}}}}}, "configuration": {"description": "The ordered configuration of the vehicle.", "required": false, "$ref": "#/CoraNCOConfiguration"}}}, "CoraNCOConfiguration": {"type": "object", "description": "The unique vehicle configuration that results from the combination of ordered options and basic technical equipment.", "properties": {"ordered_options": {"type": "array", "items": {"$ref": "#/CoraNCOConfigOrderedOptions"}, "description": "A list of all individual options which are selected through the ordered configuration of the vehicle and corresponding limitations.", "required": true}, "technical_options": {"description": "Addition of technically necessary components to the sales-ordered configuration.", "required": false}}}, "CoraNCOConfigOrderedOptions": {"type": "object", "properties": {"option_id": {"type": "string", "description": "The unique id of the specific option.", "required": true}, "option_type": {"type": "string", "description": "The classification of the option.", "required": false, "examples": "EXC, CXX, TWIKIT, ..."}, "option_validity": {"type": "object", "description": "The details for the validity of the specific option.", "required": false, "properties": {"valid_until": {"type": "string", "description": "The expiration date of the option's validity.", "pattern": "^(\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$"}, "valid_from": {"type": "string", "description": "The starting date of the option's validity.", "pattern": "^(\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$"}, "serial_to": {"type": "string", "description": "The serial number range end for which the option is valid.", "pattern": "^(\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$"}, "serial_from": {"type": "string", "description": "The serial number range start for which the option is valid.", "pattern": "^(\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$"}, "offer_period_start": {"type": "string", "description": "The start date for when the option was available for ordering.", "pattern": "^(\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$"}, "offer_period_end": {"type": "string", "description": "The end date for when the option was available for ordering.", "pattern": "^(\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$"}, "material_lead_time": {"type": "string", "description": "The lead time (in days) required to procure materials for this option.", "pattern": "^(\\d+|)$"}, "added_to_order_timestamp": {"type": "string", "description": "The timestamp indicating when the option was added to the order.", "format": "date-time"}}}, "referenced_package": {"type": "string", "description": "The identifier of the package to which this option belongs."}, "referenced_package_type": {"type": "string", "description": "The type of package this option is part of."}, "referenced_package_sort_order": {"type": "number", "description": "The sorting order of the referenced package within the configuration."}, "package_content_sort_order": {"type": "number", "description": "The sorting order of this option within the package content."}, "option_subtype": {"type": "string", "description": "A more specific classification of the option within its type."}, "option_subtype_value": {"type": "string", "description": "The specific value assigned to the option subtype."}, "content": {"type": "array", "description": "A list of sub-options that are included as part of this option.", "items": {"$ref": "#/CoraNCOConfigOrderedOptions"}}}}}, "examples": [{"ids": {"new_car_order_id": "NCO12345", "commission_id": "COM987654", "business_partner_id": "BP56789"}, "model_info": {"model_type": "95BAN1", "model_year": 2025, "country_code": "C00"}, "order_info": {"base_info": {"order_type": "KF", "quota_month": "2025-06", "created_by": "USERXX", "last_modified_by": "USERXX", "cancellation_reason": "A5 - quality issues"}, "trading_partner": {"importer_code": "DE", "importer_number": "1234567", "dealer_sold_to_number": "1234566", "dealer_ship_to_number": "1234566"}}, "logistics_info": {"shipping_code": "2", "receiving_port_code": "HAM"}, "status_info": {"order_status_code": "PP2000", "order_status_timestamp": "2024-03-14T10:30:00Z"}, "appointment_date_info": {"production_logistic_dates": {"order_creation_timestamp": "2024-02-28T14:00:00Z", "order_last_modification_timestamp": "2024-03-10T09:45:00Z", "requested_dealer_delivery_date": "2025-07-01"}}, "configuration": {"ordered_options": [{"option_id": "1D3", "option_type": "EXC", "option_validity": {"valid_until": "2025-12-31", "valid_from": "2023-01-01", "serial_to": "", "serial_from": "", "offer_period_start": "2023-01-01", "offer_period_end": "2025-12-31", "material_lead_time": "30", "added_to_order_timestamp": "2024-03-14T10:30:00Z"}, "referenced_package": "FT1", "referenced_package_type": "Individual", "referenced_package_sort_order": 1, "package_content_sort_order": 2, "option_subtype": "COLOR", "option_subtype_value": "LZ", "content": []}]}}]}