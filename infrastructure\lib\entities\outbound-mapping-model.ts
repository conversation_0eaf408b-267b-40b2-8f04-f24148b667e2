import { Column, <PERSON>tity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { BaseModel } from './base-model';
import { Constants, OneVmsEventHandlerKey } from '../utils/constants';

@Entity({ name: 'outbound_process_rules', schema: Constants.CORA_MD_AURORA_SCHEMA })
@Unique(['source_event_handler', 'event_result'])
export class OutboundProcessMappingModel extends BaseModel {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'text', nullable: false })
  public source_event_handler: OneVmsEventHandlerKey;

  @Column({ type: 'text', nullable: false })
  public event_result: string; // Handler result (e.g., i.O., n.i.O., FA90, etc.)

  @Column({ type: 'text', nullable: false })
  public order_status_code: string;

  @Column({ type: 'text', nullable: false })
  public error_status_code: string;

  @Column({ type: 'text', nullable: false })
  public invoice_status_code: string;
}
