import React from 'react';
import { PText } from '@porsche-design-system/components-react';

export interface OrderRowsProps {
  rows: string[][];
}

export const OrderRows: React.FC<OrderRowsProps> = ({ rows }) => {
  return (
    <>
      {rows.map((row) => {
        const rowKey = row.join('-');
        return (
          <div key={rowKey} style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
            {row.map((id) => (
              <PText size="x-small" weight="semi-bold" key={id}>
                {id}
              </PText>
            ))}
          </div>
        );
      })}
    </>
  );
};
