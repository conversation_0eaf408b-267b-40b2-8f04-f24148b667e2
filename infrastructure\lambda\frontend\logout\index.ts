import { APIGatewayProxyResult } from 'aws-lambda';
import { getEnvVarWithAssert } from '../../utils/utils';

const stage = getEnvVarWithAssert('STAGE');

// Lambda function handler
// eslint-disable-next-line @typescript-eslint/require-await
export const handler = async (): Promise<APIGatewayProxyResult> => {
  const redirectURL = getEnvVarWithAssert('REDIRECT_URL');
  const cookieDomain = getEnvVarWithAssert('COOKIE_DOMAIN');

  return {
    statusCode: 302,
    multiValueHeaders: {
      Location: [`${redirectURL}`],
      'Set-Cookie': [
        `KasAuthorization-${stage}=deleted; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`,
        `KasUserAttributes-${stage}=deleted; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`,
        `KasSessionExp-${stage}=deleted; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`,
      ],
    },
    body: 'Logout successful',
  };
};
