import { DatePickerWrapper } from '@kas-resources/react-components';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import './RequestedDeliveryDateForm.css';

interface RequestedDeliveryDateFormProps {
  selectedQuotaMonth?: string;
  selectedDesiredDeliveryDate?: string;
  handleSelect: (requested_delivery_date?: string) => void;
  setError: (area: string, err?: string) => void;
}
function todayAsDate() {
  return new Date().toISOString().slice(0, 10);
}
export const RequestedDeliveryDateForm: React.FC<RequestedDeliveryDateFormProps> = ({
  selectedQuotaMonth,
  selectedDesiredDeliveryDate,
  handleSelect,
  setError,
}) => {
  const { t } = useTranslation();
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);
  const [minQuotaMonth, setMinQuotaMonth] = useState<string | undefined>(undefined);
  const [minDate, setMinDate] = useState<string>(todayAsDate());

  useEffect(() => {
    setError('requested_delivery_date_form', errMessage);
  }, [errMessage]);

  useEffect(() => {
    if (selectedQuotaMonth) {
      const _minQuotaDate = selectedQuotaMonth + '-01';
      setMinQuotaMonth(_minQuotaDate);
    }
  }, [selectedQuotaMonth]);

  useEffect(() => {
    if (minQuotaMonth && minQuotaMonth > todayAsDate()) {
      setMinDate(minQuotaMonth);
    } else {
      setMinDate(todayAsDate());
    }
  }, [minDate, minQuotaMonth]);

  // Error Text if selected Date is before Minimum Date
  useEffect(() => {
    if (minDate && selectedDesiredDeliveryDate) {
      if (minDate > selectedDesiredDeliveryDate) {
        setErrMessage(t('desired_date_must_be_after_quota_month_and_after_today'));
      } else {
        setErrMessage(undefined);
      }
    } else {
      setErrMessage(undefined);
    }
  }, [selectedDesiredDeliveryDate, minDate, t]);

  return (
    <DatePickerWrapper
      label={t('desired_delivery_date_prompt')}
      className="delivery-date-picker"
      state={errMessage ? 'error' : 'none'}
      message={errMessage}
    >
      <input
        data-e2e="DeliveryDate"
        type="date"
        value={selectedDesiredDeliveryDate}
        name="desired_delivery_date"
        min={minDate}
        onChange={(event) => handleSelect(event.target.value || undefined)} // Must not be empty string ""
      />
    </DatePickerWrapper>
  );
};
