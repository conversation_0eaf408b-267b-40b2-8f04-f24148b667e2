import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';

import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { Constants, GRACE_PERIODS_TOTAL_LOSS, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  InboundEventHandlerEvent,
  InboundEventHandlerUnparsableEvent,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  TotalLossHandlerResult,
  UserOrderActionEventHandlerEvent,
} from '../../../../../lib/types/process-steering-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { EventHandlerContext } from '../event-handler-context';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { saveNcosWithAuditTrail } from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { EntityManager } from 'typeorm';
import { EventHandlerValidationContext } from '../validation-context';
import { EventHandlerEventbridgeSchedule, EventHandlerEventSchedulerContext } from '../event-scheduler-context';
import { v4 as uuidv4 } from 'uuid';

EventHandlerContext.init(OneVmsEventHandlerKey.TOTAL_LOSS_REPORT, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

const sqsEventValidator = new ObjectValidator<UserOrderActionEventHandlerEvent>('UserOrderActionEventHandlerEvent');

const totalLossReportFunc = async (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  event: SQSEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  /**
   * @deprecated use EventHandlerContext.logger instead
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  const outboundStatusUpdateStatement = await EventHandlerContext.getOutboundStatusUpdateStatement(
    TotalLossHandlerResult.SUCCESS,
    true,
  );

  const { unParseableEvents, parsedEvents: parsedEvents } = EventHandlerContext.commonEventInputValidation(
    event,
    sqsEventValidator,
  );
  const expectedFailedEvents: InboundEventHandlerEvent[] = [];
  const unexpectedFailedEvents: InboundEventHandlerUnparsableEvent[] = [];
  const successfulEvents: (InboundEventHandlerEvent & { messageId: string })[] = [];

  /**
   * 1. Validate if user is allowed on orders
   * 2. Start DB Transaction
   * 3. Change Status
   * 4. Create Eventbridge event
   * 5. Send Result
   */
  const ncoRepo = (await EventHandlerContext.getDataSource()).getRepository(NewCarOrderModel);
  const updatedNcos: NewCarOrderModel[] = [];
  for (const reportRequest of parsedEvents) {
    try {
      // Fetch source nco from the database
      const sourceNco = await ncoRepo.findOneBy({
        pk_new_car_order_id: reportRequest.nco_id,
      });

      // Validate Request
      if (!sourceNco) {
        const message = 'Failed to find order with id: ' + reportRequest.nco_id;
        EventHandlerContext.logger.log(LogLevel.FATAL, message, { data: reportRequest });
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      if (new Date(sourceNco.modified_at ?? 0).toISOString() !== reportRequest.modified_at) {
        const message = 'Nco was changed by someone else since the event was created';
        EventHandlerContext.logger.log(LogLevel.WARN, message, { data: { sourceNco, reportRequest } });
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      if (
        !(await EventHandlerValidationContext.authorizedForOrder(
          reportRequest.user_auth_context.organizationId,
          sourceNco,
        ))
      ) {
        const message = 'User is not authorized for provided dealer or importer number';
        EventHandlerContext.logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      // Save to DB
      const res = await saveNcosWithAuditTrail(
        await EventHandlerContext.getDataSource(),
        [sourceNco.pk_new_car_order_id],
        NcoExportActionType.REPORT_LOSS,
        async (transactionManager: EntityManager) => {
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: sourceNco.pk_new_car_order_id },
            {
              changed_by_system: OneVmsSourceSystemKey.CORA,
              order_status_onevms_timestamp_last_change: new Date().toISOString(),
              modified_by: reportRequest.user_auth_context.username,
              ...outboundStatusUpdateStatement,
            },
          );

          // Reload and return updated orders to ensure all orders were changed correctly
          return transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: sourceNco.pk_new_car_order_id,
              modified_by: reportRequest.user_auth_context.username,
            },
          });
        },
        EventHandlerContext.logger,
        false,
      );

      EventHandlerContext.logger.log(
        LogLevel.INFO,
        `Action of NewCarOrder with NCO id ${sourceNco.pk_new_car_order_id} was executed successfully.`,
      );
      updatedNcos.push(...res);
      successfulEvents.push(reportRequest);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }
      const message = 'Unexpected database error occurred';
      EventHandlerContext.logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...reportRequest, errorMessage: message });
      continue;
    }
  }

  const schedules: EventHandlerEventbridgeSchedule[] = [];
  for (const e of successfulEvents) {
    const nco = updatedNcos.find((_nco) => _nco.pk_new_car_order_id === e.nco_id);
    if (!nco) {
      EventHandlerContext.logger.log(LogLevel.FATAL, 'Unknown fail after update in DB', { data: { nco, e } });
      unexpectedFailedEvents.push({ ...e, errorMessage: 'Unknown fail after update in DB' });
      continue;
    }
    schedules.push({
      name: Constants.buildTotalLossScheduleName(EventHandlerContext.stage, e.nco_id!),
      incomingEvent: e,
      event: {
        event_type: OneVmsEventKey.TOTAL_LOSS_PERSIST,
        transaction_id: e.transaction_id,
        ncos_info: [
          {
            pk_new_car_order_id: e.nco_id!,
            modified_at: nco.modified_at!,
            sub_transaction_id: uuidv4(),
          },
        ],
        action_at: new Date().toISOString(),
        source_system: OneVmsSourceSystemKey.CORA_SYSTEM,
      },
    });
  }
  const actualSuccessfulEvents = await EventHandlerEventSchedulerContext.upsertEventBridgeSchedules(
    Constants.buildTotalLossSchedulePrefix(EventHandlerContext.stage),
    schedules,
    GRACE_PERIODS_TOTAL_LOSS[EventHandlerContext.stage],
  );

  return await EventHandlerContext.sendNotifications({
    eventKey: OneVmsEventKey.TOTAL_LOSS_REPORT,
    expectedFailedEvents,
    unexpectedFailedEvents,
    unParseableEvents,
    successfulEvents: actualSuccessfulEvents,
  });
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, totalLossReportFunc);
