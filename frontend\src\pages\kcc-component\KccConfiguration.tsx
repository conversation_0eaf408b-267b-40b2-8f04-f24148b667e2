import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  PCheckboxWrapper,
  PDivider,
  PLink,
  PModal,
  PSpinner,
  PText,
} from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { saveConfig, selectKccConfig, setKccFocused } from '../../store/slices/KccConfigSlice';
import {
  KccComponentInitialRequestParams,
  KccSaveConfigWindowMessage,
  createKccInitParamObject,
  getKccDefaultUrl,
  getKccSearchParams,
} from './kccUtils';
import { useAppSelector } from '../../app/hooks';
import { useSearchParams } from 'react-router-dom';
import { useSendConfigToKccMutation } from '../../store/api/KccApi';
import { useDispatch } from 'react-redux';
import { useInIframe } from '../../utils/useInIframe';
import { useGetStageConfigQuery } from '../../store/api/StaticJsonApi';

export const kccIFrameId = 'KccConfigurationFrame';

interface KccConfigurationProps {
  kccInitRequestParams: KccComponentInitialRequestParams;
  kccCancelDefaultRedirectUrl: string;
  kccResendConfigOnConfigChange?: boolean;
}

const KccConfiguration: React.FC<KccConfigurationProps> = ({
  /**
   * Parameters to start the Kcc Configuration Workflow
   */
  kccInitRequestParams,
  /**
   * Default URL to redirect to when Kcc Sends a CANCEL WindowPostMessage
   */
  kccCancelDefaultRedirectUrl,
  /**
   * true if KCC should be reopened with the new Configuration after the configuration changed
   * @default false
   */
  kccResendConfigOnConfigChange: kccResendConfigAfterSaveInKcc,
}) => {
  const { t } = useTranslation();
  const [isIframeLoading, setIsIframeLoading] = useState<boolean>(true);
  const [useKccDummy, setUseKccDummy] = useState<boolean>(false);
  const [searchParams, _] = useSearchParams();
  const [showIframe, setShowIframe] = useState<boolean>(false);
  const [showRedirectModal, setShowRedirectModal] = useState<string>();
  const [iframeUrl, setIframeUrl] = useState<string | undefined>(getKccDefaultUrl());
  const [kccUrlParams, setKccUrlParams] = useState<string>('');
  const [isKccCancel, setIsKccCancel] = useState<boolean>(false);
  const [triggerSendConfigToKcc, proxyConfigEndpointResponse] = useSendConfigToKccMutation();
  const kccConfig = useAppSelector(selectKccConfig);
  const dispatch = useDispatch();
  const isInIframe = useInIframe();

  const { data: stageConfig } = useGetStageConfigQuery(undefined);

  useEffect(() => {
    if (proxyConfigEndpointResponse.isUninitialized) {
      return;
    }
    if (proxyConfigEndpointResponse.isLoading) {
      setShowIframe(false);
    } else {
      if (proxyConfigEndpointResponse.isSuccess) {
        setKccUrlParams(getKccSearchParams({ 'session-id': proxyConfigEndpointResponse.data.sessionId }));
        setShowIframe(true);
      } else {
        console.error('Proxy Endpoint done loading but no success', proxyConfigEndpointResponse);
      }
    }
  }, [proxyConfigEndpointResponse.isLoading, proxyConfigEndpointResponse.isSuccess]);

  /**
   * This function sets the top and height values for the iframe based on the current stage and whether the application
   * is being used in an iframe.
   *
   * We need to distinguish between the 'dev' stage and other stages because there is a button present in the 'dev' stage.
   * Additionally, whether Cora is in an iframe makes a difference because when Cora is not in an iframe, there will be a header above the iframe.
   *
   */
  const getIframeTopValue = () => {
    if (stageConfig?.stage === 'dev') {
      if (isInIframe) {
        return {
          top: '15vh',
          height: '80vh',
        };
      } else {
        return {
          top: '25vh',
          height: '70vh',
        };
      }
    } else {
      if (isInIframe) {
        return {
          top: '0vh',
          height: '100vh',
        };
      } else {
        return {
          top: '10vh',
          height: '85vh',
        };
      }
    }
  };

  const style: React.CSSProperties = {
    ...getIframeTopValue(),
    position: 'absolute',
    left: '0',
    width: '100vw',
    border: 0,
    margin: 0,
    padding: 0,
  };

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  const hideKccConfiguration = () => {
    dispatch(setKccFocused(false));
  };

  function triggerNewCallKcc() {
    triggerSendConfigToKcc(
      createKccInitParamObject({
        ...kccInitRequestParams,
        configuration: kccInitRequestParams.configuration,
        configuration_expire: kccInitRequestParams.configuration_expire,
      }),
    );
  }

  useEffect(() => {
    if (kccInitRequestParams) {
      triggerNewCallKcc();
    }
  }, [kccInitRequestParams]);

  useEffect(() => {
    if (kccResendConfigAfterSaveInKcc) {
      triggerSendConfigToKcc(
        createKccInitParamObject({
          ...kccInitRequestParams,
          configuration: kccConfig.config,
          configuration_expire: kccConfig.config_pvms,
        }),
      );
    }
  }, [kccConfig]);

  useEffect(() => {
    console.debug('Iframe Full URL', `${iframeUrl}?${kccUrlParams}`);
  }, [iframeUrl, kccUrlParams]);

  useEffect(() => {
    console.debug('UseKccDummyChange', useKccDummy);
    if (useKccDummy) {
      if (window.location.hostname === 'localhost') {
        setIframeUrl('http://localhost:3005');
        return;
      }
      const url = `https://kcc-dummy.${window.location.host}`;
      setIframeUrl(url);
    } else {
      setIframeUrl(getKccDefaultUrl());
    }
  }, [useKccDummy]);

  useEffect(() => {
    if (stageConfig?.stage === 'dev') {
      setUseKccDummy(true);
    }
  }, [stageConfig]);

  useEffect(() => {
    if (isKccCancel) {
      const redirectTo = decodeURI(searchParams.get('origin_url') ?? '') || kccCancelDefaultRedirectUrl;
      if (!isInIframe) {
        window.location.assign(redirectTo);
      } else {
        // we are redirecting inside our iframe
        if (redirectTo.startsWith(window.location.origin)) {
          window.location.assign(redirectTo);
        } else {
          setShowRedirectModal(redirectTo);
        }
      }
      // Assign is blocked by security policy for cross-domain iframe
      // isInIframe ? window.top?.location.assign(redirectTo) : window.location.assign(redirectTo);
    }
  }, [isKccCancel]);

  function onWindowMessage(event: MessageEvent) {
    switch (event.data.channel) {
      case 'KASCC_NOTIFICATION':
        switch (event.data.type) {
          case 'CONFIG_SAVED':
          case 'CONFIG_TRANSFER':
            console.debug('CONFIG_SAVED message from KCC', event.data);
            const _configSavedMessage = event.data as KccSaveConfigWindowMessage;
            dispatch(saveConfig(_configSavedMessage));
            dispatch(setKccFocused(false));
            break;
          case 'CANCEL':
          case 'PROCESS_CANCEL':
            console.debug('KCC PROCESS CANCELED');
            // KASHEARTBE-1202
            // Found no other way to make the redirect use the newest value from kccRedirectUrl, probably related to eventlistener bullshit
            setIsKccCancel(true);
            break;
          default:
            console.log('Received Windowmessage (KASCC_NOTIFICATION) with unknown type', event.data);
        }
        break;
    }
  }
  // Create/Destroy window Message hook
  useEffect(() => {
    window.addEventListener('message', onWindowMessage);
    return () => {
      window.removeEventListener('message', onWindowMessage);
    };
  }, []);

  return (
    <>
      <PModal open={!!showRedirectModal} dismissButton={false}>
        <PLink data-e2e="back_to_quota_btn" href={showRedirectModal} target={'_top'}>
          {t('navigate_back_to_quota_dashboard')}
        </PLink>
      </PModal>
      {stageConfig?.stage === 'dev' && (
        <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-start' }}>
          <PButton
            data-e2e="openCoraModal"
            variant="primary"
            onClick={hideKccConfiguration}
            style={{ paddingRight: '10px' }}
          >
            {t('open_cora_modal')}
          </PButton>
          <PCheckboxWrapper label={t('use_kcc_dummy')}>
            <input
              type="checkbox"
              checked={useKccDummy}
              onChange={(e) => {
                console.debug('Change useKccDummyForm');
                setUseKccDummy(e.target.checked);
              }}
            ></input>
          </PCheckboxWrapper>
          <PDivider />
        </div>
      )}
      {isIframeLoading && <PSpinner />}
      {showIframe && (
        <iframe
          title="KCC-Iframe"
          src={`${iframeUrl}?${kccUrlParams}`}
          style={style}
          id={kccIFrameId}
          onLoad={() => setIsIframeLoading(false)}
        ></iframe>
      )}
    </>
  );
};
export default KccConfiguration;
