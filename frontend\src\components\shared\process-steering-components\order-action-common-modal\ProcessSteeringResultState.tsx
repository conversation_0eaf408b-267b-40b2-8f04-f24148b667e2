import { PHeading, PInlineNotification, PButton } from '@porsche-design-system/components-react';
import { CarOrderInList } from '../../../../store/types';
import {
  InboundApiEventResponse,
  OneVmsEventKey,
} from '../../../../../../infrastructure/lib/types/process-steering-types';
import { useTranslation } from 'react-i18next';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { JSX, useState } from 'react';
import { TransactionDetails } from '../sub-components/TransactionDetails';

interface ProcessSteeringResultStateComponentProps {
  orders?: CarOrderInList[];
  isMulti: boolean;
  closeModal: () => void;
  error?: FetchBaseQueryError;
  actionType: OneVmsEventKey;
  result?: InboundApiEventResponse;
  navigationButtonGroup?: JSX.Element;
}

export const ProcessSteeringResultStateComponent: React.FC<ProcessSteeringResultStateComponentProps> = (props) => {
  const { t } = useTranslation();
  const isError = !!props.error;
  const [isCopyBannerOpen, setIsCopyBannerOpen] = useState(false);
  const [showAllOrders, setShowAllOrders] = useState(false);
  // Maximum number of IDs per row and maximum visible rows when collapsed
  const MAX_IDS_PER_ROW = 3;
  const MAX_VISIBLE_ROWS = 1;

  function handleCopyTxId() {
    if (props.result) {
      navigator.clipboard.writeText(props.result.data.transaction_id);
      setIsCopyBannerOpen(true);
    }
  }

  function handleCopyOrderIds() {
    if (props.orders) {
      navigator.clipboard.writeText(props.orders.map((order) => order.pk_new_car_order_id).join(', '));
      setIsCopyBannerOpen(true);
    }
  }

  function handleToggleOrders() {
    setShowAllOrders(!showAllOrders);
  }

  function handleTransitionEnd() {
    setTimeout(function () {
      setIsCopyBannerOpen(false);
    }, 2000);
  }

  // Prepare the order IDs as rows for multi order action:
  const orderRows: string[][] = [];
  if (props.orders) {
    const orderIds = props.orders.map((o) => o.pk_new_car_order_id);
    for (let i = 0; i < orderIds.length; i += MAX_IDS_PER_ROW) {
      orderRows.push(orderIds.slice(i, i + MAX_IDS_PER_ROW));
    }
  }
  const visibleRows = showAllOrders ? orderRows : orderRows.slice(0, MAX_VISIBLE_ROWS);

  return (
    <>
      <div
        className={isError ? `header-error-${props.actionType}` : `header-success-${props.actionType}`}
        style={{ minWidth: '500px', paddingBottom: '2rem', display: 'flex', alignItems: 'center' }}
      >
        <PHeading size="large">
          {t(isError ? `${props.actionType}_order_failed_header` : `${props.actionType}_order_successful_header`)}
        </PHeading>
      </div>

      <PInlineNotification
        style={{ marginTop: '-20px', marginBottom: '20px' }}
        data-e2e={'backend_message'}
        state={isError ? 'error' : 'info'}
        dismissButton={false}
      >
        {isError
          ? t(`${props.actionType}_order_failed`, {
              message: (props.error?.data as { message: string })?.message ?? t('unknown_error'),
            })
          : t(`${props.actionType}_order_successful`)}
      </PInlineNotification>

      {props.result && (
        <TransactionDetails
          result={props.result}
          orders={props.orders}
          isMulti={props.isMulti}
          isCopyBannerOpen={isCopyBannerOpen}
          visibleRows={visibleRows}
          orderRows={orderRows}
          handleCopyTxId={handleCopyTxId}
          handleCopyOrderIds={handleCopyOrderIds}
          handleToggleOrders={handleToggleOrders}
          handleTransitionEnd={handleTransitionEnd}
          actionType={props.actionType}
        />
      )}
      <br />
      {props.navigationButtonGroup ?? (
        <PButton
          data-e2e="close"
          variant="primary"
          title={t(`${props.actionType}_order_finish`)}
          aria-label={t(`${props.actionType}_order_finish`)}
          onClick={props.closeModal}
        >
          {t(`${props.actionType}_order_finish`)}
        </PButton>
      )}
    </>
  );
};
