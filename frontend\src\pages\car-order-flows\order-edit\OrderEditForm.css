.order-edit-details-single-container {
  display: flex;
  flex-direction: row;
}

.order-edit-details-single-container .field-col {
  flex-grow: 10;
  flex-direction: column;
}

.order-edit-details-single-container .field-row > p-text:nth-child(2) {
  padding-bottom: 7px;
}

.form-container .field-col {
  width: 50%;
  flex-direction: column;
}
.form-container .field-row {
  padding-right: 10px;
}
.form-container {
  display: flex;
}
.form-container .field-col {
  flex-direction: column;
}
.form-container .field-row {
  padding-right: 10px;
  padding-top: 10px;
}
.port-code-form {
  padding-right: 10px;
  padding-top: 10px;
}

.order-create-details-single-container1 {
  display: flex;
  flex-direction: column;
}

.order-create-details-single-container1 .field-row1 {
  display: flex;
  flex-direction: row;
}

.order-create-details-single-container1 .field-col1 {
  flex: 1;
}

.order-create-details-single-container1 .field-row1 > p-text:nth-child(2) {
  padding-bottom: 10px;
}

.form-container1 {
  display: flex;
  flex-direction: column;
}

.form-container1 .field-row1 {
  display: flex;
  flex-direction: row;
  padding-top: 10px;
}

.form-container1 .field-col1 {
  flex: 1;
  padding-right: 10px;
}
