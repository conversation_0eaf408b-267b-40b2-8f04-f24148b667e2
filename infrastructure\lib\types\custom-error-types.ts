import { InboundEventDispatcherEvent } from './process-steering-types';

export class UpdateNCOCoreDataValidationError extends <PERSON>rror {
  public readonly errorCode: number;

  public constructor(message: string, errorCode: number) {
    super(message);
    this.name = 'ValidationError';
    this.errorCode = errorCode;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

export class DealerRelationError extends Error {
  public readonly errorCode: number;

  public constructor(message: string, errorCode: number) {
    super(message);
    this.name = 'DealerRelationError';
    this.errorCode = errorCode;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

export class InboundEventDispatcherError extends Error {
  public readonly inboundEventDispatcherEvent: InboundEventDispatcherEvent;
  public readonly correlationId: string;

  public constructor(message: string, inboundEventDispatcherEvent: InboundEventDispatcherEvent, correlationId: string) {
    super(message);
    this.name = 'InboundEventDispatcherError';
    this.inboundEventDispatcherEvent = inboundEventDispatcherEvent;
    this.correlationId = correlationId;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}
