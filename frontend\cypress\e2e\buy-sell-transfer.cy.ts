import { USERNAME_WRITE } from '../support/constants';
import { retryableBefore } from '../support/retry';
import { preproductionOrders } from '../support/order-lists-test-data';
import {
  buysell_dealer_from,
  buysell_dealer_to,
  buysell_dealerPks,
  buysell_org_rels,
  buysell_orgRelPks,
} from '../support/buy-sell-transfer-test-data';
import { Constants } from '../../../infrastructure/lib/utils/constants';

const openBuySellBtn = '[data-e2e="OpenBuySellModal"]';
const buySellModal = '[data-e2e="BuySellModal"]';
const selectImporter = '[data-e2e="SelectImporter"]';
const selectSourceDealer = '[data-e2e="SelectSourceDealer"]';
const selectDestDealer = '[data-e2e="SelectDestDealer"]';
const acceptBtn = '[data-e2e="accept"]';
const acceptTransferBtn = '[data-e2e="accept_transfer"]';
const buySellSuccessHeading = '[data-e2e="BuySellSuccessHeading"]';
const buySellErrorHeading = '[data-e2e="BuySellErrorHeading"]';
const totalOrdersNr = preproductionOrders.length;

// Function to select dropdown option inside shadow DOM
const selectDropdownOption = (element: string, optionText: string) => {
  cy.getIframeBody()
    .find(element, { timeout: 5000 })
    .shadow()
    .find('p-select-wrapper-dropdown')
    .shadow()
    .find('input')
    .click();

  cy.getIframeBody().find(element, { timeout: 5000 }).find('select').select(optionText, { force: true });
};

describe('Buy sell transfer', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task(
      'prepareNcoRds',
      { objs: preproductionOrders.map((nco) => ({ ...nco, dealer_number: buysell_dealer_from.sk_dealer_number })) },
      { timeout: 10000 },
    );
    cy.task('prepareDynamodb', {
      tableName: `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`,
      objs: [buysell_dealer_to, buysell_dealer_from],
    });
    cy.task('prepareDynamodb', {
      tableName: Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName),
      objs: buysell_org_rels,
    });
    cy.visit('https://paddock-dev.dpp.porsche.com/cora/navigation-system');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupDynamodb', {
      tableName: `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`,
      pks: buysell_dealerPks,
    });
    cy.task('cleanupDynamodb', {
      tableName: Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName),
      pks: buysell_orgRelPks,
    });
  });

  it('Successfully transfer orders from source dealer to dest dealer', () => {
    cy.intercept('GET', `**/authorized-dealer*`).as('authorizedDealer');
    // Open Buy/Sell Modal
    cy.getIframeBody().find(openBuySellBtn, { timeout: 10000 }).should('be.visible').click({ force: true });

    // Select Importer, Source Dealer, and Destination Dealer
    cy.getIframeBody().find(buySellModal, { timeout: 10000 }).should('exist').and('not.be.empty');
    cy.wait('@authorizedDealer');
    selectDropdownOption(selectImporter, buysell_dealer_from.pk_importer_number);
    selectDropdownOption(selectSourceDealer, buysell_dealer_from.sk_dealer_number);
    selectDropdownOption(selectDestDealer, buysell_dealer_to.sk_dealer_number);

    // Click Accept Transfer
    cy.getIframeBody().find(acceptTransferBtn, { timeout: 5000 }).click({ force: true });

    // Verify Total Orders
    cy.getIframeBody()
      .find('[data-e2e="totalOrders"]', { timeout: 5000 })
      .invoke('text')
      .then((text) => {
        const totalValue = text.trim();
        chai.expect(totalValue).to.eq(totalOrdersNr.toString());
      });

    cy.wait(1000);
    cy.getIframeBody().find(acceptTransferBtn, { timeout: 5000 }).click({ force: true });

    cy.wait(1000);
    cy.getIframeBody().find(acceptBtn, { timeout: 5000 }).click({ force: true });

    // Verify Success Message
    cy.getIframeBody()
      .find(buySellSuccessHeading)
      .invoke('text')
      .then((text) => {
        chai.expect(text.trim()).to.contains(`${totalOrdersNr}/${totalOrdersNr}`);
      });
  });

  it('Failed to transfer orders from source dealer to dest dealer', () => {
    cy.intercept('GET', `**/authorized-dealer*`).as('authorizedDealer');
    // Open Buy/Sell Modal
    cy.getIframeBody().find(openBuySellBtn, { timeout: 10000 }).should('be.visible').click({ force: true });

    // Select Importer, Source Dealer, and Destination Dealer
    cy.getIframeBody().find(buySellModal, { timeout: 10000 }).should('exist').and('not.be.empty');
    cy.wait('@authorizedDealer');
    selectDropdownOption(selectImporter, buysell_dealer_from.pk_importer_number);
    selectDropdownOption(selectSourceDealer, buysell_dealer_from.sk_dealer_number);
    selectDropdownOption(selectDestDealer, buysell_dealer_to.sk_dealer_number);

    // Click Accept Transfer
    cy.getIframeBody().find(acceptTransferBtn, { timeout: 5000 }).click({ force: true });

    // Verify Total Orders
    cy.getIframeBody()
      .find('[data-e2e="totalOrders"]', { timeout: 5000 })
      .invoke('text')
      .then((text) => {
        const totalValue = text.trim();
        chai.expect(totalValue).to.eq(totalOrdersNr.toString());
      });

    cy.wait(1000);
    cy.getIframeBody().find(acceptTransferBtn, { timeout: 5000 }).click({ force: true });

    //Intercept buy-sell-transfer
    const errorMsg = 'Could not transfer the orders';
    cy.intercept('POST', '**/buy-sell-transfer*', (req) => {
      req.reply({
        statusCode: 500,
        body: { message: `${errorMsg}` },
      });
    });
    cy.getIframeBody().find(acceptBtn, { timeout: 5000 }).click({ force: true });

    // Verify error Message
    cy.getIframeBody()
      .find(buySellErrorHeading)
      .invoke('text')
      .then((text) => {
        chai.expect(text.trim()).to.contains(`0/${totalOrdersNr}`);
      });
  });

  it('No order found by the source dealer', () => {
    cy.intercept('GET', `**/authorized-dealer*`).as('authorizedDealer');
    // Open Buy/Sell Modal
    cy.getIframeBody().find(openBuySellBtn, { timeout: 10000 }).should('be.visible').click({ force: true });

    //intercept fetch-order-ids and return an empty nco list
    cy.intercept('GET', '**/new-car-order-ids*', (req) => {
      req.reply({
        statusCode: 200,
        body: { new_car_order_ids: [] },
      });
    });

    // Select Importer, Source Dealer, and Destination Dealer
    cy.getIframeBody().find(buySellModal, { timeout: 10000 }).should('exist').and('not.be.empty');
    cy.wait('@authorizedDealer');
    selectDropdownOption(selectImporter, buysell_dealer_from.pk_importer_number);
    selectDropdownOption(selectSourceDealer, buysell_dealer_from.sk_dealer_number);
    selectDropdownOption(selectDestDealer, buysell_dealer_to.sk_dealer_number);

    // Click Accept Transfer
    cy.getIframeBody().find(acceptTransferBtn, { timeout: 5000 }).click({ force: true });

    // Verify Total Orders
    cy.getIframeBody()
      .find('[data-e2e="totalOrders"]', { timeout: 5000 })
      .invoke('text')
      .then((text) => {
        const totalValue = text.trim();
        chai.expect(totalValue).to.eq('0');
      });
  });
});
