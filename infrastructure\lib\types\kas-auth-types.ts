export interface KasAuthEndpointResponse {
  username: string; //'xyzxyz';
  firstName: string; //'Max';
  lastName: string; //'Mustermann';
  porschePartnerNo: string; //'9500090';
  organizationId: string; //'550e8400-e29b-41d4-a716-446655440000'
  displayName: string;
  kasApplications: Partial<
    Record<
      string, //'tyd'
      KasApplication[]
    >
  >;
}

export interface KasApplication {
  role: string; //'power_master_user'
  modelTypeVisibility: string; //'DLR'
}
