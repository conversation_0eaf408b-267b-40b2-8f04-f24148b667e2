import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { CfnWebACL } from 'aws-cdk-lib/aws-wafv2';

export class ConstantsCdk {
  public static readonly NODE_JS_VERSION: Runtime = Runtime.NODEJS_22_X;
}

/**
 * @source aws-cdk implementation v2.165.0
 */
export const LambdaDefaultBundlingExternalModules = ['@aws-sdk/*', '@smithy/*'];

export const LambdaTypeOrmBundlingExternalModules = [
  'pg',
  'typeorm',
  'type-fest',
  'typeorm-aurora-data-api-driver',
].concat(LambdaDefaultBundlingExternalModules);

export const CoraBuildingBlockRules: CfnWebACL.RuleProperty[] = [
  {
    name: 'AWSManagedRulesCommonRuleSet',
    priority: 10,
    visibilityConfig: {
      sampledRequestsEnabled: true,
      cloudWatchMetricsEnabled: true,
      metricName: 'AWSManagedRulesCommonRuleSet',
    },
    overrideAction: {
      none: {},
    },
    statement: {
      managedRuleGroupStatement: {
        vendorName: 'AWS',
        name: 'AWSManagedRulesCommonRuleSet',
        excludedRules: [],
        //custom action overwrite to not prevent bodys > 8KB
        ruleActionOverrides: [
          {
            name: 'SizeRestrictions_BODY',
            actionToUse: { allow: {} },
          },
        ],
      },
    },
  },
  {
    name: 'AWS-AWSManagedRulesAmazonIpReputationList',
    priority: 20,
    statement: {
      managedRuleGroupStatement: {
        vendorName: 'AWS',
        name: 'AWSManagedRulesAmazonIpReputationList',
      },
    },
    overrideAction: {
      none: {},
    },
    visibilityConfig: {
      sampledRequestsEnabled: true,
      cloudWatchMetricsEnabled: true,
      metricName: 'AWSManagedRulesAmazonIpReputationList',
    },
  },
  {
    name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet',
    priority: 30,
    statement: {
      managedRuleGroupStatement: {
        vendorName: 'AWS',
        name: 'AWSManagedRulesKnownBadInputsRuleSet',
      },
    },
    overrideAction: {
      none: {},
    },
    visibilityConfig: {
      sampledRequestsEnabled: true,
      cloudWatchMetricsEnabled: true,
      metricName: 'AWSManagedRulesKnownBadInputsRuleSet',
    },
  },
];
