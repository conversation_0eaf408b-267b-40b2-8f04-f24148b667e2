import { TFunction } from 'i18next';
import { CarOrderInList, PurchaseIntentionInList } from '../store/types';
import { CoraNCOConfiguration } from '../../../infrastructure/lib/types/new-car-order-types';
import { ModelTypeTextModel } from '../../../infrastructure/lib/entities/model-type-text-model';

export const matchMttForOrderOrPurchaseIntention = (
  mtts: ModelTypeTextModel[] | undefined,
  obj: CarOrderInList | PurchaseIntentionInList,
  t: TFunction<'translation', undefined>,
) => {
  //only match model type and year and NOT importer and CNR, since texts all come from ggid1 for now
  const mtt = mtts?.find(
    (mtt) =>
      //mtt.cnr === obj.cnr &&
      //mtt.importer_number === obj.importer_number &&
      mtt.model_type === obj.model_type && mtt.model_year.toString() === obj.model_year.toString(),
  );
  return mtt?.model_text ?? t('missing_translation');
};

export const matchMttForModelTypeAndModelYear = (
  mtts: ModelTypeTextModel[] | undefined,
  model_type: string,
  model_year: string,
  t: TFunction<'translation', undefined>,
) => {
  //only match model type and year and NOT importer and CNR, since texts all come from ggid1 for now
  const mtt = mtts?.find(
    (mtt) =>
      //mtt.cnr === obj.cnr &&
      //mtt.importer_number === obj.importer_number &&
      mtt.model_type === model_type && mtt.model_year.toString() === model_year.toString(),
  );
  return mtt?.model_text ?? t('missing_translation');
};

/**
 * Copied from Backend infrastructure/lambda/utils/validation_helpers.ts
 *
 * Calculates Quotamonth validity for a given KccConfiguration
 *
 * @param config KccConfiguration in KAS Format
 * @returns Obj: {from: "YYYY-MM" (inclusive), until: "YYYY-MM" (inclusive)}
 */
export const getValidQuotaMonthForConfig = (
  config: CoraNCOConfiguration,
  old_config?: CoraNCOConfiguration,
): { from: string; until: string } => {
  const DAY_IN_MS = 1000 * 60 * 60 * 24;
  let maxMaterialLeadTime = new Date(0);
  let minFromOptionValidity = new Date(0);
  let minTillOptionValidity = new Date('9999-12-01'); // Large possible Date
  for (const option of config.ordered_options) {
    if (option.option_validity?.material_lead_time) {
      // We start with the timestamp when an option was added, or now if the option is new
      const _fromCalculationDate = new Date(
        old_config?.ordered_options.find((_o) => _o.option_id === option.option_id)?.option_validity
          ?.added_to_order_timestamp ?? Date.now(),
      );
      const _ml = new Date(
        _fromCalculationDate.getTime() + Number(option.option_validity.material_lead_time) * DAY_IN_MS,
      );
      maxMaterialLeadTime = maxMaterialLeadTime < _ml ? _ml : maxMaterialLeadTime;
    }
    if (option.option_validity?.valid_from) {
      const _fov = new Date(option.option_validity.valid_from);
      minFromOptionValidity = minFromOptionValidity > _fov ? _fov : minFromOptionValidity;
    }
    if (option.option_validity?.valid_until) {
      const _tov = new Date(option.option_validity.valid_until);
      minTillOptionValidity = minTillOptionValidity > _tov ? _tov : minTillOptionValidity;
    }
  }
  let fromQuotaMonth: Date;
  let untilQuotaMonth: Date;
  /**
   * Referenztag für die Auswahl des möglichen Quotenmonats ist der 15 des Monats.
   * Materialbeschaffungszeitpunkt muss kleiner sein wie der 16. 00:00:00 des Monats
   * Kleinstes Optionsgültigkeiten Datum muss größer sein wie 00:00:00 wie der 1. des Monats
   * see https://skyway.porsche.com/jira/browse/ARTKAST-669
   */

  const maxMaterialLeadTimeDate = new Date(maxMaterialLeadTime);
  const maxMaterialLeadTimeDateThreshold = new Date(maxMaterialLeadTimeDate);
  maxMaterialLeadTimeDateThreshold.setUTCDate(16);
  maxMaterialLeadTimeDateThreshold.setUTCHours(0, 0, 0, 0);
  const _minQuotaMonthMaterialLeadTime = new Date(maxMaterialLeadTimeDate);
  // If >= 16. 00:00:00 add one month
  if (maxMaterialLeadTimeDate >= maxMaterialLeadTimeDateThreshold) {
    _minQuotaMonthMaterialLeadTime.setMonth(_minQuotaMonthMaterialLeadTime.getMonth() + 1);
  }
  _minQuotaMonthMaterialLeadTime.setUTCDate(1);
  _minQuotaMonthMaterialLeadTime.setUTCHours(0, 0, 0, 0);

  // OUT OF SCOPE: _minQuotaTillOptionValiditiy, calculation to be added here

  // min(_minQuotaMonthMaterialLeadTime, _minQuotaTillOptionValidity)
  fromQuotaMonth = _minQuotaMonthMaterialLeadTime;

  const _untilMinQuotaMonthTillOptionValidity = new Date(minTillOptionValidity);
  _untilMinQuotaMonthTillOptionValidity.setUTCDate(1);
  _untilMinQuotaMonthTillOptionValidity.setUTCHours(0, 0, 0, 0);
  untilQuotaMonth = _untilMinQuotaMonthTillOptionValidity;

  return { from: fromQuotaMonth.toISOString().slice(0, 7), until: untilQuotaMonth.toISOString().slice(0, 7) };
};
