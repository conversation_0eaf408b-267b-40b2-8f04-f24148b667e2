import { CoraQuota } from '../../../../infrastructure/lib/types/quota-api-types';
import { QuotaApiRequestParams } from '../types';
import { baseApi } from './BaseApi';

const apiPrefix = 'quota';
const quotaApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getQuotaForDealer: builder.query<CoraQuota[], QuotaApiRequestParams>({
      query: (props) => ({ url: `${apiPrefix}`, params: props }),
      providesTags: (result, error, arg) => [{ type: 'quota', id: createQuotaStoreId(arg) }],
    }),
  }),
});

export const createQuotaStoreId = (params: QuotaApiRequestParams) =>
  `${params.model_type}-${params.model_year}-${params.dealer_number}-${params.importer_number}`;

export const { useGetQuotaForDealerQuery, useLazyGetQuotaForDealerQuery } = quotaApi;
