const prefixCopyOrders = '/new-car-order/';

module.exports = function (req, res, next) {
  if (req.url.startsWith(prefixCopyOrders) && req.url.includes('copy') && req.method === 'POST') {
    const copyResult = [];
    for (const [k, v] of Object.entries(req.body)) {
      const regex = /^\d{4}-\d{2}$/;
      console.warn(k, v, regex.test(k), typeof v);
      if (!regex.test(k) || typeof v !== 'number') {
        return res.send.call(res, JSON.stringify({ message: 'TEST' }), 409);
      }
      const failed = Math.floor(Math.random() * 3);
      const quotas = parseInt(v, 10) - failed;

      copyResult.push({
        quota_month: k,
        quota_consumed: quotas,
        quota_failed: failed,
        new_car_order_ids: Array.from({ length: quotas }, (_, i) => `TEST${i.toString().padStart(4, '0')}`),
      });
    }
    res.jsonp(copyResult);
  } else {
    next();
  }
};
