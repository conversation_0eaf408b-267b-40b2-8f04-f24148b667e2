import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Message as MessageFor<PERSON><PERSON><PERSON>, Producer } from 'kafkajs';
import { GeneralError } from './errors';
import { KafkaSecret, secretCache } from './secret-cache';
import {
  CloudEvent,
  emitterFor,
  EmitterFunction,
  Kafka as CEKafka,
  KafkaMessage,
  Message as CEMessage,
} from 'cloudevents';
import { KafkaGenericObj, KafkaObjsWrapper } from '../backend/export-nco/types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

interface KafkaAdapterProps {
  kafka_secret_arn: string;
  kafka_brokers: string[];
  logger: KasLambdaLogger;
}

export class KafkaAdapter {
  private readonly props: KafkaAdapterProps;
  private kafkaProducer?: Producer;
  private kafkaAdmin?: Admin;
  private isConnected: boolean;
  private readonly logger: Kas<PERSON>ambdaLogger;

  public constructor(props: KafkaAdapterProps) {
    this.props = props;
    this.isConnected = false;
    this.logger = props.logger;
  }

  private static assertIsKafkaMessage(message: unknown): asserts message is MessageForKafka {
    if (!message || typeof message !== 'object') {
      throw new Error('The message is no kafka compatible message. It is not even a object.');
    }
    const kafkaMessage = message as Partial<KafkaMessage>;
    if (!(kafkaMessage.key instanceof Buffer || typeof kafkaMessage.key === 'string')) {
      throw new Error('The message is no kafka compatible message. `key` is either missing or not a Buffer or string.');
    }
    if (!(kafkaMessage.value instanceof Buffer || typeof kafkaMessage.value === 'string')) {
      throw new Error(
        'The message is no kafka compatible message. `value` is either missing or not a Buffer or string.',
      );
    }
  }

  public async connectToKafka(clientId?: string): Promise<void> {
    if (!this.kafkaProducer) {
      const cachedSecret = await secretCache.getSecret<KafkaSecret>(this.props.kafka_secret_arn);

      const kafka = new Kafka({
        clientId: clientId ?? 'cora-new-car-order-export',
        brokers: this.props.kafka_brokers,
        sasl: {
          mechanism: 'plain',
          username: cachedSecret.username,
          password: cachedSecret.password,
        },
        ssl: true,
        connectionTimeout: 5000,
        logLevel: 1,
      });
      this.kafkaProducer = kafka.producer({
        allowAutoTopicCreation: false,
      });
      this.kafkaProducer.on(this.kafkaProducer.events.CONNECT, () => {
        this.isConnected = true;
      });
      this.kafkaProducer.on(this.kafkaProducer.events.DISCONNECT, () => {
        this.isConnected = false;
      });
    }
    if (!this.isConnected) {
      return await this.kafkaProducer.connect();
    }
    return;
  }

  public async connectToKafkaAdmin(clientId?: string): Promise<void> {
    if (!this.kafkaAdmin) {
      const cachedSecret = await secretCache.getSecret<KafkaSecret>(this.props.kafka_secret_arn);

      const kafka = new Kafka({
        clientId: clientId ?? 'cora-new-car-order-export',
        brokers: this.props.kafka_brokers,
        sasl: {
          username: cachedSecret.username,
          password: cachedSecret.password,
          mechanism: 'plain',
        },
        ssl: true,
        connectionTimeout: 5000,
        logLevel: 1,
      });
      this.kafkaAdmin = kafka.admin();
    }
    return await this.kafkaAdmin.connect();
  }

  public async disconnectKafkaAdmin(): Promise<void> {
    if (this.kafkaAdmin) {
      return await this.kafkaAdmin.disconnect();
    }
    return;
  }

  public async disconnect(): Promise<void> {
    if (this.kafkaProducer) {
      return await this.kafkaProducer.disconnect();
    }
    return;
  }

  public createEmitterFunction(topic: string): EmitterFunction {
    const kafkaTransport = async (message: CEMessage): Promise<void> => {
      KafkaAdapter.assertIsKafkaMessage(message);
      delete message.body;
      for (const key in message.headers) {
        if (message.headers[key] === undefined) {
          delete message.headers[key];
        }
      }
      await this.kafkaProducer!.send({
        topic: topic,
        messages: [message],
      });
    };
    return emitterFor(kafkaTransport, { binding: CEKafka });
  }

  public async pushObjsToTopic<T extends KafkaGenericObj>(props: {
    kWrapper: KafkaObjsWrapper<T>;
    topic: string;
    correlationid: string;
    additional_ce_headers?: Record<string, string>;
  }): Promise<void> {
    const objs = props.kWrapper.kafkaObjs;
    const kafkaObjTyp = props.kWrapper.kafkaObjTyp; //JobRoleVisibility, Organization, ModelTypeVisibility
    const kafkaActionTyp = props.kWrapper.kafkaActionTyp; // create, update, delete

    this.logger.log(LogLevel.DEBUG, `Sending ${objs.length} objects to Kafka Topic`);
    await this.connectToKafka().catch((err) => {
      this.logger.log(LogLevel.ERROR, 'Failed to connect to Kafka', { data: err as unknown });
      throw new GeneralError({ message: 'Failed to connect to Kafka', causedBy: err });
    });

    const emits = [];
    try {
      for (const obj of objs) {
        const kafkaKey = obj.id;
        const payload = obj.obj ? JSON.stringify(obj.obj) : null;
        const ce = {
          importer: obj.importer ?? 'NO_IMPORTER_IN_OBJ', // For some reason ce does not like explicitly undefined values here
          ...props.additional_ce_headers,
          //https://skyway.porsche.com/confluence/display/ARTKAST/ADR005+-+Formatting+of+events
          source: '/porsche/ppd/kas/cora/' + kafkaObjTyp + '/' + kafkaKey,
          type: kafkaObjTyp + '.' + (obj.kafkaActionTyp ?? kafkaActionTyp),
          id: kafkaKey,
          correlationid: obj.correlationid ?? props.correlationid,
          data: payload,
          time: new Date().toISOString(),
          partitionkey: kafkaKey,
          datacontenttype: 'application/json',
        };

        const emit = this.createEmitterFunction(props.topic);
        emits.push(emit(new CloudEvent(new CloudEvent(ce))));
      }
      await Promise.all(emits);
    } catch (error) {
      this.logger.log(LogLevel.ERROR, `Writing to Kafkatopic ${props.topic} failed`, { data: error });
      throw new GeneralError({
        message: `Writing to Kafka Topic ${props.topic} failed: `,
        causedBy: error,
      });
    }
    // Removed to not disconnect after every send
    // await this.disconnect().catch((err) => {
    //   logger.log(LogLevel.ERROR, 'Failed to disconnect from Kafka', err);
    //   throw new GeneralError({ message: 'Failed to disconnect from Kafka', causedBy: err });
    // });
  }

  public async isConsumerOffsetLagZero(consumerGroupId: string, topic: string): Promise<boolean> {
    this.logger.log(LogLevel.DEBUG, `Fetch consumer lag for ${consumerGroupId}`);
    await this.connectToKafkaAdmin().catch((err) => {
      this.logger.log(LogLevel.ERROR, 'Failed to connect to KafkaAdmin', { data: err as unknown });
      throw new GeneralError({ message: 'Failed to connect to KafkaAdmin', causedBy: err });
    });

    try {
      const topicOffsetRecord: Record<number, number> = {};
      const topicOffsets = await this.kafkaAdmin?.fetchTopicOffsets(topic);
      const groupOffsetRecord: Record<number, number> = {};
      if (topicOffsets && topicOffsets.length > 0) {
        for (const topicOffSet of topicOffsets) {
          this.logger.log(LogLevel.DEBUG, `Partition: ${topicOffSet.partition}, offset: ${topicOffSet.offset}`);
          topicOffsetRecord[topicOffSet.partition] = parseInt(topicOffSet.offset);
        }
      }
      const offsets = await this.kafkaAdmin?.fetchOffsets({
        groupId: consumerGroupId,
        topics: [topic],
      });
      if (offsets && offsets.length > 0) {
        const partitions = offsets[0].partitions;
        for (const partition of partitions) {
          this.logger.log(LogLevel.DEBUG, 'partition', { data: partition });
          groupOffsetRecord[partition.partition] = parseInt(partition.offset);
        }
      }

      let offsetLag = -1;
      if (Object.keys(topicOffsetRecord).length > 0 && Object.keys(groupOffsetRecord).length > 0) {
        offsetLag = 0;
        for (const partition in topicOffsetRecord) {
          if (groupOffsetRecord[partition]) {
            offsetLag += Math.abs(topicOffsetRecord[partition] - groupOffsetRecord[partition]);
          }
        }
        this.logger.log(LogLevel.DEBUG, `Calculated OffsetLag is ${offsetLag}`);
      }
      return offsetLag === 0;
    } catch (error) {
      this.logger.log(
        LogLevel.ERROR,
        `Failed to fetch the consumer offset lag of the groupId:${consumerGroupId} by the topic: ${topic}`,
        { data: error },
      );
      throw new GeneralError({
        message: `Failed to fetch consumer offset lag: `,
        causedBy: error,
      });
    }
    // await this.disconnectKafkaAdmin().catch((err) => {
    //   logger.log(LogLevel.ERROR, 'Failed to disconnect from KafkaAdmin', err);
    //   throw new GeneralError({ message: 'Failed to disconnect from KafkaAdmin', causedBy: err });
    // });
  }
}
