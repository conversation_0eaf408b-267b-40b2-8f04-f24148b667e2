import {
  mappings_to_one_vms_status as mappingsToOneVmsStatusData,
  mappings_to_invoice_status as invoiceStatusMapping,
} from './status_mapping_data.json';

describe('Validation Logic Tests', () => {
  test('each mapping should have a unique combination of pvms_status and lock_reason', (): void => {
    const seenCombinations = new Set();
    mappingsToOneVmsStatusData.forEach((mapping) => {
      const combination = `${mapping.pvms_status}-${mapping.lock_reason ?? 'null'}`;
      expect(seenCombinations.has(combination)).toBe(false);
      seenCombinations.add(combination);
    });
  });

  test('no duplicate pvms invoice status in invoice status mapping', (): void => {
    const keys = invoiceStatusMapping.map((mapping) => mapping.pvms_invoice_status);
    expect(keys.length).toEqual(Array.from(new Set(keys)).length);
  });
});
