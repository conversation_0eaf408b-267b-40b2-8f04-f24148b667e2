#!/bin/bash
# Retrieve the first instance ID (modify if needed for specific instance selection)
target=$(aws ec2 describe-instances --query 'Reservations[].Instances[0].InstanceId' --output text --region eu-west-1)

RDS_CLUSTER_WRITER=$(aws rds describe-db-cluster-endpoints --query 'DBClusterEndpoints[0].Endpoint' --output text  --region eu-west-1)
host=$RDS_CLUSTER_WRITER

# Start the SSM session with correct variable interpolation
aws ssm start-session \
  --target "$target" \
  --document-name AWS-StartPortForwardingSessionToRemoteHost \
  --parameters "{\"host\":[\"$host\"],\"portNumber\":[\"5432\"],\"localPortNumber\":[\"5432\"]}" \
  --region eu-west-1
