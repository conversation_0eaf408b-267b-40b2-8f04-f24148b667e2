import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { getUserName, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { NotificationTransactionPatchRequest, Transaction } from '../../../../lib/types/notification-center-types';

const logger = new KasLambdaLogger('notification-center-put', LogLevel.TRACE);
const dynamoDbClient = new DynamoDBClient({ region: process.env.REGION ?? 'eu-west-1' });
const objectValidator = new ObjectValidator<NotificationTransactionPatchRequest>('NotificationTransactionPatchRequest');

const TRANSACTION_TABLE_NAME = getEnvVarWithAssert('TRANSACTION_TABLE_NAME');
const TRANSACTION_TABLE_PK_NAME = getEnvVarWithAssert('TRANSACTION_TABLE_PK_NAME');
const CORS_DOMAIN = getEnvVarWithAssert('CORS_DOMAIN');
const PADDOCK_CORS_DOMAIN = getEnvVarWithAssert('PADDOCK_CORS_DOMAIN');

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Parse query parameters
    const userName = getUserName({ event: event }, logger);
    if (!userName) {
      logger.log(LogLevel.WARN, 'Failed to get the userName', { data: sanitizeApiGwEvent({ event: event }, logger) });
      return sendFail(
        {
          message: 'No PPN username in request',
          status: 400,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    const transaction_id = event.pathParameters ? event.pathParameters['transactionId'] : undefined;
    if (!transaction_id) {
      return sendFail(
        {
          message: 'No transaction_id in request',
          status: 400,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    // Parse body from event object
    const body = JSON.parse(event.body ?? '{}') as unknown;

    // Validate body against the schema
    const [body_validated, validation_errors] = objectValidator.validate(body);
    if (body_validated === null) {
      logger.log(LogLevel.WARN, 'ajv validation failed', { data: validation_errors });
      return sendFail(
        {
          message: 'Ajv validation failed with: ' + JSON.stringify(validation_errors),
          status: 400,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    const cmd = new QueryCommand({
      TableName: TRANSACTION_TABLE_NAME,
      KeyConditionExpression: `${TRANSACTION_TABLE_PK_NAME} = :value`,
      ExpressionAttributeValues: { ':value': transaction_id },
    });

    const result = await dynamoDbClient.send(cmd);

    if (!result.Items?.length) {
      return sendFail(
        {
          message: 'Not Found',
          status: 404,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    if (result.Items.length > 1) {
      // Should not be possible
      logger.log(LogLevel.ERROR, 'More then 1 transaction with id', { data: { transaction_id, data: result.Items } });
      return sendFail(
        {
          message: 'More than 1 Transaction with this ID, talk to support',
          status: 500,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    const transaction = result.Items[0] as Transaction;
    if (transaction.action_by !== userName) {
      logger.log(
        LogLevel.WARN,
        `User ${userName} not allowed to view transaction ${transaction.transaction_id}. Transaction belongs to another user`,
        { data: transaction },
      );
      return sendFail(
        {
          message: `User ${userName} not allowed to edit transaction ${transaction.transaction_id}. Transaction belongs to another user.`,
          status: 403,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    const newTransaction: Transaction = { ...transaction, hidden: body_validated.hidden ?? false };
    const putCommand = new PutCommand({
      TableName: TRANSACTION_TABLE_NAME,
      Item: { ...transaction, hidden: body_validated.hidden ?? false },
    });
    await dynamoDbClient.send(putCommand);

    return sendSuccess(
      { body: newTransaction, reqHeaders: event.headers, allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN] },
      logger,
    );
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error querying DynamoDB:', { data: { error } });
    return sendFail(
      {
        message: 'Internal Server Error',
        status: 500,
        reqHeaders: event.headers,
        allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
      },
      logger,
    );
  }
};
