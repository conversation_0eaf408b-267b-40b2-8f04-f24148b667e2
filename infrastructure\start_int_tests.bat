@echo off
REM Grant execute permission (not needed in Windows, so we skip chmod)
REM Execute the script
call connectToBastionHostForIntTest.bat

REM Run Jest tests with the specified configuration
npx jest --testPathPattern=integration --config=jest.int.cjs
@echo off
REM Specify the path of the test file (use relative Windows-style path without quotes)
@REM set TEST_FILE_PATH=infrastructure/lambda/backend/masterdata/fetch-all-onevms-status/integration.test.ts

REM Run Jest tests with the specified configuration and test file path
npx jest %TEST_FILE_PATH% --config=jest.int.cjs
