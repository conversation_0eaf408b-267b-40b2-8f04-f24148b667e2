#!/bin/bash
set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

rm -rf $SCRIPT_DIR/postgres-certs
mkdir -p $SCRIPT_DIR/postgres-certs && cd $SCRIPT_DIR/postgres-certs
openssl genpkey -algorithm RSA -out $SCRIPT_DIR/postgres-certs/server.key
openssl req -new -x509 -days 365 -key $SCRIPT_DIR/postgres-certs/server.key -out $SCRIPT_DIR/postgres-certs/server.crt -subj "/C=US/ST=YourState/L=YourCity/O=YourOrg/OU=IT/CN=localhost"
docker-compose -f $SCRIPT_DIR/postgres_docker.yml up -d