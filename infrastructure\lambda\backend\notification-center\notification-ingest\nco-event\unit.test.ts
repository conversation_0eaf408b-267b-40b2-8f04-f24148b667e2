import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { handler } from '.';
import { mockContext } from '../../../../utils/test-utils';
import 'aws-sdk-client-mock-jest';
import { MSKEvent } from 'aws-lambda';
import { BatchWriteCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import jsonTestEvent from './test-event.json';
import { Transaction } from '../../../../../lib/types/notification-center-types';

const ddbMock = mockClient(DynamoDBClient);

beforeEach(() => {
  ddbMock.reset();
  jest.resetModules();
});

describe('Transaction Ingest Lambda', () => {
  it('processes Kafka events and writes transactions/subtransactions, and updates last_updated on transaction if subtransaction exists', async () => {
    ddbMock
      .on(QueryCommand)
      .resolvesOnce({ Items: [] })
      .resolvesOnce({ Items: [{ transaction_id: 'existing-transaction-id' }] })
      .resolvesOnce({ Items: [{ sub_transaction_id: 'subtxn-1' }] });

    // Mock the BatchWriteCommand and UpdateCommand
    ddbMock.on(BatchWriteCommand).resolves({ UnprocessedItems: {} });
    ddbMock.on(UpdateCommand).resolves({});

    await handler(input, mockContext, () => {});

    const ddbWriteCall = ddbMock.commandCalls(BatchWriteCommand)[0]?.lastArg as BatchWriteCommand;
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!ddbWriteCall) throw new Error('BatchWriteCommand was not called!');

    const writeCallInput = ddbWriteCall.input.RequestItems?.[process.env.TRANSACTIONS_TABLE_NAME!] as {
      PutRequest: { Item: Transaction };
    }[];

    console.log('writeCallInput:', writeCallInput);

    expect(writeCallInput.length).toBe(1);
    expect(writeCallInput[0].PutRequest.Item.transaction_id).toBe(jsonTestEvent.Message.transaction_id);

    const subtransactionWriteCall = ddbMock.commandCalls(BatchWriteCommand)[1]?.lastArg as BatchWriteCommand;
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!subtransactionWriteCall) throw new Error('BatchWriteCommand for subtransactions was not called!');

    const subtransactionWriteInput = subtransactionWriteCall.input.RequestItems?.[
      process.env.SUBTRANSACTIONS_TABLE_NAME!
    ] as {
      PutRequest: { Item: { transaction_id: string; uuid: string } };
    }[];

    expect(subtransactionWriteInput.length).toBe(1);
    expect(subtransactionWriteInput[0].PutRequest.Item.transaction_id).toBe(jsonTestEvent.Message.transaction_id);
  });
});

const input: MSKEvent = {
  bootstrapServers: '',
  eventSource: 'aws:kafka',
  eventSourceArn: '',
  records: {
    [process.env.NOTIFICATION_KAFKA_TOPIC!]: [
      {
        headers: [],
        key: '',
        offset: 0,
        partition: 1,
        timestamp: 0,
        timestampType: 'CREATE_TIME',
        topic: '',
        value: Buffer.from(JSON.stringify(jsonTestEvent.Message)).toString('base64'),
      },
    ],
  },
};
