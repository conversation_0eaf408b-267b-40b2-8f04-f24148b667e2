import { DataSource, Repository } from 'typeorm';
import { CoraPurchaseIntentionModel } from '../../lib/entities/purchase-intention-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../lib/entities/new-car-order-model';
import { AttributeValue, DynamoDBClient, ScanCommand, ScanCommandInput } from '@aws-sdk/client-dynamodb';
import { unmarshall } from '@aws-sdk/util-dynamodb';
import { writeFile } from 'fs/promises';
import { dbCredentials } from './constants';
import { CoraNCPurchaseIntentionDatabaseObject } from './old_types/purchase_intention_types';
import { PvmsOrderDataDTOWithConfig } from '../../lib/types/purchase-intention-types';

const TABLE_NAME = 'cora-prod-pvms-purchase-intentions';
const FAILED_DIR = 'failed_pi';

async function main(): Promise<void> {
  const dataSource = await new DataSource({
    type: 'aurora-postgres',
    database: dbCredentials.dbname,
    secretArn: dbCredentials.secretArn,
    resourceArn: dbCredentials.clusterArn,
    region: dbCredentials.region,
    synchronize: false,
    logging: false,
    entities: [CoraPurchaseIntentionModel, NewCarOrderModel, NcoConfigurationModel, NcoConfigOrderedOptionsModel],
  }).initialize();

  const client = new DynamoDBClient({ region: 'eu-west-1' });

  const res = await getAllFromTable(TABLE_NAME, client, dataSource);
  console.log(res[0]);
}

async function getAllFromTable(tableName: string, client: DynamoDBClient, dataSource: DataSource): Promise<string[]> {
  const items: string[] = [];
  let exclusiveStartKey: Record<string, AttributeValue> | undefined;

  try {
    do {
      const params: ScanCommandInput = {
        TableName: tableName,
        ExclusiveStartKey: exclusiveStartKey,
      };

      const command = new ScanCommand(params);
      const response = await client.send(command);

      if (response.Items) {
        for (const item of response.Items) {
          const _item = unmarshall(item) as CoraNCPurchaseIntentionDatabaseObject;
          console.log('Saving', _item.pvms_deal_id);
          if (_item.pvms_deal_id !== _item.ids.new_car_order_id) {
            console.error('Failure, ids do not match', _item.pvms_deal_id);
          }
          await writeToPostgres(_item, dataSource.getRepository(CoraPurchaseIntentionModel));
          items.push(_item.pvms_deal_id);
        }
      }
      exclusiveStartKey = response.LastEvaluatedKey;
      console.warn('LastEvaluatedKey', exclusiveStartKey);
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    } while (exclusiveStartKey);

    return items;
  } catch (error) {
    console.error('Error scanning DynamoDB table:', error);
    throw error;
  }
}

async function writeToPostgres(
  item: CoraNCPurchaseIntentionDatabaseObject,
  repository: Repository<CoraPurchaseIntentionModel>,
): Promise<void> {
  try {
    const pi: CoraPurchaseIntentionModel = pvmsOrderDataToCoraPurchaseIntentionDatabaseObject(item);
    await repository.save(pi);
  } catch (error) {
    console.error(`${item.pvms_deal_id} failed`, error);
    await writeObjectToJsonFile(item);
    return;
  }
  return;
}

async function writeObjectToJsonFile(data: CoraNCPurchaseIntentionDatabaseObject): Promise<void> {
  try {
    const jsonContent = JSON.stringify(data, null, 2);
    await writeFile(`${__dirname}/${FAILED_DIR}/${data.pvms_deal_id}.json`, jsonContent, 'utf-8');
  } catch (error) {
    console.error('Error writing JSON to file:', error);
    throw error;
  }
}

const pvmsOrderDataToCoraPurchaseIntentionDatabaseObject = (
  pvmsOrderData: PvmsOrderDataDTOWithConfig,
): CoraPurchaseIntentionModel => {
  //strip leading 0s from dealer_number and importer_number
  const dealerNumber = pvmsOrderData.order_info.trading_partner.dealer_sold_to_number.replace(/^0+/, '');
  const importerNumber = pvmsOrderData.order_info.trading_partner.importer_number.replace(/^0+/, '');

  return {
    purchase_intention_id: pvmsOrderData.ids.new_car_order_id,
    importer_number: importerNumber,
    importer_code: pvmsOrderData.order_info.trading_partner.importer_code,
    dealer_number: dealerNumber,
    model_type: pvmsOrderData.model_info.model_type,
    model_year: pvmsOrderData.model_info.model_year.toString(),
    cnr: pvmsOrderData.model_info.country_code,
    quota_month: pvmsToCoraQuota(pvmsOrderData.order_info.base_info.quota_month ?? '100001'), // Cannot actually be undefined if it comes from the dynamodb database
    order_type: pvmsOrderData.order_info.base_info.order_type,
    shipping_code: pvmsOrderData.logistics_info.shipping_code,
    receiving_port_code: pvmsOrderData.logistics_info.receiving_port_code,
    requested_dealer_delivery_date:
      pvmsToIsoDate(pvmsOrderData.appointment_date_info?.production_logistic_dates?.requested_dealer_delivery_date) ??
      undefined,
    created_at: new Date(
      pvmsOrderData.appointment_date_info?.production_logistic_dates?.order_creation_date ?? new Date().toISOString(),
    ) as unknown as string,
    created_by: 'PVMS',
    modified_by: 'PVMS',
    modified_at: new Date() as unknown as string,
    vehicle_status_code: pvmsOrderData.order_info.status_info.vehicle_status_pvms_code,
    business_partner_id: pvmsOrderData.ids.business_partner_id,
    seller: pvmsOrderData.order_info.sales_info.sales_person_id,
    vehicle_configuration_pvmsnext: pvmsOrderData.vehicle_configuration_pvmsnext,
    vehicle_configuration_onevms: null,
  };
};

const pvmsToCoraQuota = (pvmsQuota: string): string => {
  return `${pvmsQuota.slice(0, 4)}-${pvmsQuota.slice(4)}`;
};

function pvmsToIsoDate(expected_dealer_delivery_date: string | undefined): string | null {
  if (expected_dealer_delivery_date) {
    const date = new Date(expected_dealer_delivery_date);
    return new Date(date.toISOString().split('T')[0]) as unknown as string;
  } else {
    return null;
  }
}

main().catch((error) => console.error('Error:', error));
