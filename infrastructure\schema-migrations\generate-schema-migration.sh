#!/bin/bash
set -e
AWS_REGION="eu-west-1"
SECRET_NAME="cora-aurora-admin-secret"

# Extract DB credentials from secret
SECRET_VALUE=$(aws secretsmanager get-secret-value --secret-id "$SECRET_NAME" --query 'SecretString' --output text --region "$AWS_REGION")
export DB_USER=$(echo "$SECRET_VALUE" | jq -r '.username')
export DB_PASS=$(echo "$SECRET_VALUE" | jq -r '.password')
export DB_NAME=$(echo "$SECRET_VALUE" | jq -r '.dbname')
export DB_HOST=$(echo "$SECRET_VALUE" | jq -r '.host')
export IS_LOCAL=1

echo "DB credentials received for migration generate."

# Run the migration command
npm run migration:generate

echo "Migration command executed successfully and generated migration files."
