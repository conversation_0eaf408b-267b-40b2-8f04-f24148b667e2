import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { DataSource } from 'typeorm';

import { Constants } from '../../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../../lib/utils/object-validation';
import { createTypeORMDataSource } from '../../../../../config/typeorm-config';
import { secretCache } from '../../../../../utils/secret-cache';
import {
  getEnvVarWithAssert,
  pushNotificationsToKafka,
  pvmsToCoraQuota,
  pvmsToIsoDate,
} from '../../../../../utils/utils';
import {
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../../lib/types/process-steering-types';
import { createSqsEventHandlerWithInitLogger } from '../../../../../utils/sqs-event-handler';
import { PvmsOrderDataDTOTransaction, VehicleConfigurationPvmsNext } from '../../../../../../lib/types/pvms-types';
import { CoraPurchaseIntentionModel } from '../../../../../../lib/entities/purchase-intention-model';
import { OmConfigApiAdapter } from '../../../../../utils/om-config-api';
import { imp2RegionLookup } from './imp2RegionLookup';
import { PvmsOrderDataDTOWithConfig } from '../../../../../../lib/types/purchase-intention-types';
import { KafkaAdapter } from '../../../../../utils/kafka';

type UpsertPiWithMessageId = PvmsOrderDataDTOTransaction & { messageId: string };
type UpsertPiWithError = UpsertPiWithMessageId & { errorMessage: string };

const auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');
const configApiSecretArn = getEnvVarWithAssert('CONFIG_API_SECRET_ARN');
const kafkaSecretArn = getEnvVarWithAssert('KAFKA_SECRET_ARN');
const KAFKA_TOPIC_NOTIFICATION = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
const KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];

const stage = getEnvVarWithAssert('STAGE');
const kasLambdaLogger = new KasLambdaLogger('pvms-import-upsert-pi-handler', LogLevel.TRACE);

//init secret cache
secretCache.initCache(configApiSecretArn, auroraSecretArn, kafkaSecretArn);

//init config api adapter
const configApiAdapter = new OmConfigApiAdapter({
  omConfigApiSecretArn: configApiSecretArn,
  logger: kasLambdaLogger,
});

//init kafka adapter
const kafkaAdapter = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: kafkaSecretArn,
  logger: kasLambdaLogger,
});

const sqsEventValidator = new ObjectValidator<PvmsOrderDataDTOTransaction>('PvmsOrderDataDTOTransaction');

const pvmsOrderDataToCoraPurchaseIntentionDatabaseObject = (
  pvmsOrderData: PvmsOrderDataDTOWithConfig,
): CoraPurchaseIntentionModel => {
  //strip leading 0s from dealer_number and importer_number
  const dealerNumber = pvmsOrderData.order_info.trading_partner.dealer_sold_to_number.replace(/^0+/, '');
  const importerNumber = pvmsOrderData.order_info.trading_partner.importer_number.replace(/^0+/, '');

  return {
    purchase_intention_id: pvmsOrderData.ids.new_car_order_id,
    importer_number: importerNumber,
    importer_code: pvmsOrderData.order_info.trading_partner.importer_code,
    dealer_number: dealerNumber,
    model_type: pvmsOrderData.model_info.model_type,
    model_year: pvmsOrderData.model_info.model_year.toString(),
    cnr: pvmsOrderData.model_info.country_code,
    quota_month: pvmsToCoraQuota(pvmsOrderData.order_info.base_info.quota_month)!,
    order_type: pvmsOrderData.order_info.base_info.order_type,
    shipping_code: pvmsOrderData.logistics_info.shipping_code,
    receiving_port_code: pvmsOrderData.logistics_info.receiving_port_code,
    requested_dealer_delivery_date:
      pvmsToIsoDate(pvmsOrderData.appointment_date_info?.production_logistic_dates?.requested_dealer_delivery_date) ??
      undefined,
    created_at:
      pvmsOrderData.appointment_date_info?.production_logistic_dates?.order_creation_date ?? new Date().toISOString(),
    created_by: OneVmsSourceSystemKey.PVMS,
    modified_by: OneVmsSourceSystemKey.PVMS,
    modified_at: new Date().toISOString(),
    vehicle_status_code: pvmsOrderData.order_info.status_info.vehicle_status_pvms_code,
    business_partner_id: pvmsOrderData.ids.business_partner_id,
    seller: pvmsOrderData.order_info.sales_info.sales_person_id,
    vehicle_configuration_pvmsnext: pvmsOrderData.vehicle_configuration_pvmsnext as unknown,
    vehicle_configuration_onevms: null,
    is_converted: false,
  };
};

const pvmsEventToNotification = (
  pvmsEvent: Partial<UpsertPiWithMessageId>,
  status: NotificationStatus,
  details?: unknown,
): NotificationKafkaEvent => {
  return {
    transaction_id: pvmsEvent.transaction_id ?? 'unknown',
    sub_transaction_id: pvmsEvent.transaction_id ?? 'unknown',
    event_type: OneVmsEventKey.PVMS_IMPORT,
    action_by: OneVmsSourceSystemKey.PVMS,
    action_at: new Date().toISOString(),
    source_system: OneVmsSourceSystemKey.PVMS,
    status: status,
    details,
  };
};

const getPvmsNextConfig = async (
  upsertPiEvent: PvmsOrderDataDTOTransaction,
): Promise<VehicleConfigurationPvmsNext | null> => {
  const region =
    imp2RegionLookup[upsertPiEvent.order_info.trading_partner.importer_number.replace(/^0+/, '')] ?? 'europe';
  const pvmsNextConfig = await configApiAdapter.getConfig(upsertPiEvent.ids.vguid_pvms_DEPRECATED, region);
  return pvmsNextConfig;
};

const upsertPiFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };
  const successfulEvents: UpsertPiWithMessageId[] = [];
  const expectedFailedEvents: UpsertPiWithError[] = [];
  const unexpectedFailedEvents: UpsertPiWithError[] = [];
  const unParseableEvents: Partial<UpsertPiWithError>[] = [];

  const upsertPiEvents = event.Records.map((record) => {
    try {
      // Parse body from event object
      const upsertPiEvent = JSON.parse(record.body) as PvmsOrderDataDTOTransaction | undefined;
      const [body_validated, validation_errors] = sqsEventValidator.validate(upsertPiEvent);
      if (body_validated === null) {
        const message = 'SQS record is not valid, ajv validation failed';
        logger.log(LogLevel.WARN, message, { data: validation_errors });
        throw new Error('Invalid event');
      }

      return { ...upsertPiEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<PvmsOrderDataDTOTransaction>),
        messageId: record.messageId,
        errorMessage: message,
      });
      return undefined;
    }
  }).filter(Boolean) as UpsertPiWithMessageId[];

  let piDataSource: DataSource;

  try {
    piDataSource = await createTypeORMDataSource(logger, auroraSecretArn, stage, [CoraPurchaseIntentionModel]);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await pushNotificationsToKafka(
      upsertPiEvents.map((pvmsEvent) => pvmsEventToNotification(pvmsEvent, NotificationStatus.EVENT_HANDLER_NIO)),
      KAFKA_TOPIC_NOTIFICATION,
      kafkaAdapter,
      kasLambdaLogger,
    );

    // Return fail for all events so that they are retried later
    return {
      batchItemFailures: upsertPiEvents.map((upsertPiEvent) => ({
        itemIdentifier: upsertPiEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  //Process all events one by one
  for (const upsertPiEvent of upsertPiEvents) {
    logger.setObjectId(upsertPiEvent.transaction_id);

    //Check if we actually have a purchase intention
    const isPurchaseIntention = Constants.PVMS_PURCHASE_INTENTION_STATUS_ALL.includes(
      upsertPiEvent.order_info.status_info.vehicle_status_pvms_code,
    );
    if (!isPurchaseIntention) {
      const message = 'Event is not a purchase intention, this should not have happened, skipping';
      logger.log(LogLevel.ERROR, message, { data: upsertPiEvent });
      expectedFailedEvents.push({ ...upsertPiEvent, errorMessage: 'Object is not a purchase intention' });
      continue;
    }

    //Get pvms next config
    const pvmsNextConfig = await getPvmsNextConfig(upsertPiEvent);
    if (!pvmsNextConfig) {
      const message = 'Failed to get pvms next config from config API';
      logger.log(LogLevel.ERROR, message, {
        data: upsertPiEvent,
      });
      unexpectedFailedEvents.push({ ...upsertPiEvent, errorMessage: message });
      //Return as fail so that event will be retried later
      sqsBatchResponse.batchItemFailures.push({
        itemIdentifier: upsertPiEvent.messageId,
        errorMessage: message,
      });
      continue;
    }

    const coraPi = pvmsOrderDataToCoraPurchaseIntentionDatabaseObject({
      ...upsertPiEvent,
      vehicle_configuration_pvmsnext: pvmsNextConfig,
    });

    try {
      await piDataSource.transaction(async (transactionalEntityManager) => {
        // Check if PI exists and is converted
        const existingPi = await transactionalEntityManager.findOneBy(CoraPurchaseIntentionModel, {
          purchase_intention_id: coraPi.purchase_intention_id,
        });

        if (existingPi?.is_converted) {
          logger.log(LogLevel.INFO, 'Skipping update for converted PI', {
            data: { pi_id: coraPi.purchase_intention_id },
          });
          return;
        }

        // Perform upsert if PI doesn't exist or isn't converted
        await transactionalEntityManager.save(CoraPurchaseIntentionModel, coraPi);

        logger.log(
          LogLevel.INFO,
          `Successfully ${existingPi ? 'updated' : 'inserted'} PI with id "${coraPi.purchase_intention_id}" into rds`,
        );

        successfulEvents.push(upsertPiEvent);
      });
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'PI was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...upsertPiEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during update';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...upsertPiEvent, errorMessage: message });
      //Return as fail so that event will be retried later
      sqsBatchResponse.batchItemFailures.push({
        itemIdentifier: upsertPiEvent.messageId,
        errorMessage: message,
      });
      continue;
    }
  }

  const notificationEvents = [
    ...successfulEvents.map((pvmsEvent) => pvmsEventToNotification(pvmsEvent, NotificationStatus.EVENT_HANDLER_IO)),
    ...expectedFailedEvents.map((pvmsEvent) =>
      pvmsEventToNotification(pvmsEvent, NotificationStatus.EVENT_HANDLER_NIO, pvmsEvent.errorMessage),
    ),
    ...unParseableEvents.map((pvmsEvent) =>
      pvmsEventToNotification(pvmsEvent, NotificationStatus.EVENT_HANDLER_NIO, pvmsEvent.errorMessage),
    ),
    ...unexpectedFailedEvents.map((pvmsEvent) =>
      pvmsEventToNotification(pvmsEvent, NotificationStatus.EVENT_HANDLER_NIO, pvmsEvent.errorMessage),
    ),
  ];

  try {
    await pushNotificationsToKafka(notificationEvents, KAFKA_TOPIC_NOTIFICATION, kafkaAdapter, kasLambdaLogger);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications', { data: e });
  }

  //Return fails so that retriable events are tried again
  return sqsBatchResponse;
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(kasLambdaLogger)(event, context, upsertPiFunc);
