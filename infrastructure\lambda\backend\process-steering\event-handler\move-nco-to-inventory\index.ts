import { UserOrderActionEventHandlerEvent, OneVmsEventKey } from '../../../../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { createStatusChangeHandler } from '../../../../utils/status-change-handler';
import { NcoExportActionType } from '../../../export-nco/types';

const sqsEventValidator = new ObjectValidator<UserOrderActionEventHandlerEvent>('UserOrderActionEventHandlerEvent');

export const handler = createStatusChangeHandler<UserOrderActionEventHandlerEvent>({
  sqsEventValidator,
  handlerKey: OneVmsEventHandlerKey.MOVE_TO_INVENTORY,
  exportAction: NcoExportActionType.MOVE_TO_INVENTORY,
  eventKey: OneVmsEventKey.MOVE_TO_INVENTORY,
  successLogMessage: (id) => `NewCarOrder ${id} was successfully moved to dealer inventory.`,
});
