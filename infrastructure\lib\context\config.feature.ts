import { CoraStackProps } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { getGlobalParameterNames } from './global-parameter-names';

//set to dev for easier typings, because we dont use it anyway
const stage = 'dev'; //feature-name-placeholder';

export const featureProps: CoraStackProps = {
  stage,
  hostedZoneName: 'cora-dev.dpp.porsche.com',
  paddockDomainName: 'paddock-dev.dpp.porsche.com',
  mail: Constants.MAIL,
  owner: Constants.OWNER,
  useCase: Constants.USE_CASE,
  env: {
    account: '************',
    region: Constants.DEFAULT_REGION,
  },
  idp: {
    domain: 'https://ppnlite.porsche.com',
    loginUrl: 'https://ppnlite.porsche.com/as/authorization.oauth2',
    tokenUrl: 'https://ppnlite.porsche.com/as/token.oauth2',
    clientId: '907f9a8a-1c88-4ae7-98d8-70b32a05c6e4',
    publicKeyUrl: 'https://ppnlite.porsche.com/pf/JWKS',
    issuer: 'https://ppnlite.porsche.com',
  },
  applicationNameToAuthorize: Constants.APPLICATION_NAME_TO_AUTHORIZE,
  ppnRolesWrite: [
    'ppn_approle_pag_operations',
    //'ppn_approle_boss_dev_basic_application_role',
    //'ppn_approle_kas_importer_dev',
    //'ppn_approle_kas_dealer_dev',
  ],
  kasAuthEndpointUrl: 'https://kasauth.kas-dev.dpp.porsche.com',
  globalParameterNames: getGlobalParameterNames(stage),
  kafkaParameters: {
    brokers: ['pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092'],
    newCarOrderTopicOneVms: 'FRA_one_vms_cora_new_car_order_DEV',
    newCarOrderTopicPvms: 'FRA_one_vms_cora_new_car_order_pvms_DEV',
    newCarOrderTopicP06: 'FRA_one_vms_cora_new_car_order_p06_DEV',
    newCarOrderTopicConfigRequest: 'FRA_one_vms_kcc_translate_request_DEV',
    newCarOrderTopicConfigResponse: 'FRA_one_vms_kcc_translate_response_DEV',
    bossOrgTopic: 'FRA_kas_hub_boss_kas_organizations_DEV',
    hubModelTypeVisibilityTopic: 'FRA_kas_hub_boss_model_type_visibilites_DEV',
    quotaTopic: 'FRA_one_vms_quota_dealer_quotas_DEV',
    pvmsOrderDataTopic: 'FRA_kas_hub_new_car_order_pvms_dev',
    p06InboundTopic: 'FRA_kas_hub_new_car_order_pia_dev',
    pccdModelTypeTextTopic: 'FRA_one_vms_pccd_inbound_model_type_text_DEV',
    ncoNotificationTopic: 'FRA_one_vms_cora_notifications_new_car_order_DEV',
  },
  alerting: {
    alertingEmails: [],
  },
  featureFlags: {
    copyOrder: true,
    updateNcoCoreData: true,
    reportRevokeTotalLoss: true,
    deallocateQuota: true,
    importerTransfer: true,
    handleDealerInventory: true,
    buySellTransfer: true,
    ncoInvoiceMapping: true,
    p06DataSync: true,
  },
};
