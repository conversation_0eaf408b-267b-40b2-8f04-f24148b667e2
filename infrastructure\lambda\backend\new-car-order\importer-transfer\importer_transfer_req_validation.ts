import { APIGatewayProxyEvent } from 'aws-lambda';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { getAuthContext, sanitizeApiGwEvent } from '../../../utils/api-helpers';
import { NewCarOrderModel } from '../../../../lib/entities/new-car-order-model';
import { CoraNCOImporterTransferApiRequest } from '../../../../lib/types/new-car-order-types';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { BatchGetCommand, BatchGetCommandInput } from '@aws-sdk/lib-dynamodb';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CoraMdImporter,
  CoraMdOrderType,
  CoraMdShippingCode,
} from '../../../../lib/types/masterdata-types';
import { validateOrderType, validatePortCode, validateShippingCode } from '../../../utils/validation-helpers';

export class ApiRequestValidationError extends Error {
  public readonly statusCode: number;

  public constructor(statusCode: number, message: string) {
    super(message);
    this.statusCode = statusCode;
    this.name = 'ApiRequestValidationError';

    Object.setPrototypeOf(this, ApiRequestValidationError.prototype);
  }
}

export const validateRequest = <T>(
  event: APIGatewayProxyEvent,
  logger: KasLambdaLogger,
  objectValidator: ObjectValidator<T>,
): { body_validated: T; ppnId: string } => {
  const body = JSON.parse(event.body ?? '{}') as unknown;
  const [body_validated, validation_errors] = objectValidator.validate(body);
  if (body_validated === null) {
    logger.log(LogLevel.WARN, 'ajv validation failed', { data: validation_errors });
    throw new ApiRequestValidationError(400, 'Ajv validation failed with: ' + JSON.stringify(validation_errors));
  }
  const userAttributes = getAuthContext({ event }, logger);
  const ppnId = userAttributes?.organizationId;
  if (!ppnId) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: sanitizeApiGwEvent({ event: event }, logger) });
    throw new ApiRequestValidationError(401, 'Auth context is missing');
  }
  return { body_validated, ppnId };
};
/**
 * if it s cancelled return false if not true
 * if onevms code is cancelled
 * Constants.CORA_NEW_CAR_ORDER_STATUS_CANCELLED
 */
export const isImporterTransferStatusValid = (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  props: Pick<
    NewCarOrderModel,
    'order_status_onevms_code' | 'order_status_onevms_error_code' | 'order_invoice_onevms_code'
  >,
): boolean => {
  /**
   * Validateion Logic based on the Status process control goes here
   */
  return true;
};

export const validateImporterTransferParams = async (props: {
  req: CoraNCOImporterTransferApiRequest;
  importerTableName: string;
  shippingCodeTableName: string;
  orderTypeTableName: string;
  dealerTableName: string;
  dynamoDb: DynamoDBClient;
}): Promise<{ importer: CoraMdImporter }> => {
  //batch get to get all required dynamodb items
  const batchGetParams: BatchGetCommandInput = {
    RequestItems: {},
  };
  batchGetParams.RequestItems![props.importerTableName] = {
    Keys: [{ pk_importer_number: props.req.to_importer_number }],
  };
  batchGetParams.RequestItems![props.dealerTableName] = {
    Keys: [
      {
        pk_importer_number: props.req.to_importer_number,
        sk_dealer_number: props.req.to_dealer_number,
      },
    ],
  };
  if (props.req.new_nco_attributes.shipping_code) {
    batchGetParams.RequestItems![props.shippingCodeTableName] = {
      Keys: [{ pk_shipping_code: props.req.new_nco_attributes.shipping_code }],
    };
  }
  if (props.req.new_nco_attributes.order_type) {
    batchGetParams.RequestItems![props.orderTypeTableName] = {
      Keys: [{ pk_order_type: props.req.new_nco_attributes.order_type }],
    };
  }
  const batchResponse = await props.dynamoDb.send(new BatchGetCommand(batchGetParams));
  const importer = batchResponse.Responses?.[props.importerTableName]?.[0] as CoraMdImporter | undefined;
  const shippingCode = batchResponse.Responses?.[props.shippingCodeTableName]?.[0] as CoraMdShippingCode | undefined;
  const orderType = batchResponse.Responses?.[props.orderTypeTableName]?.[0] as CoraMdOrderType | undefined;
  const dealer = batchResponse.Responses?.[props.dealerTableName]?.[0] as CoraMdDealer | undefined;
  if (props.req.new_nco_attributes.shipping_code) {
    const validSc = validateShippingCode({
      isUserImp: true,
      shippingCode: shippingCode,
      ncoDealerNumber: props.req.to_dealer_number,
      ncoImporterNumber: props.req.to_importer_number,
    });
    if (!validSc.valid) throw new ApiRequestValidationError(400, validSc.error!);
  }

  if (props.req.new_nco_attributes.order_type) {
    const validOt = validateOrderType({
      isUserImp: true,
      orderType: orderType,
      ncoDealerNumber: props.req.to_dealer_number,
      ncoImporterNumber: props.req.to_importer_number,
    });
    if (!validOt.valid) throw new ApiRequestValidationError(400, validOt.error!);
  }

  if (props.req.new_nco_attributes.receiving_port_code) {
    const validPc = validatePortCode({
      isUserImp: true,
      ncoDealerNumber: props.req.to_dealer_number,
      ncoImporterNumber: props.req.to_importer_number,
      dealer: dealer,
      importer: importer,
    });
    if (!validPc.valid) throw new ApiRequestValidationError(400, validPc.error!);
  }
  if (!importer) throw new ApiRequestValidationError(400, 'Importer Code not found');

  return { importer: importer };
};
