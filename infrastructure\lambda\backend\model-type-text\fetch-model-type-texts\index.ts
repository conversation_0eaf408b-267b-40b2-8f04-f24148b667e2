import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { getAuthContext, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { secretCache } from '../../../utils/secret-cache';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { getAllowedImporters } from '../../../utils/validation-helpers';
import { DataSource } from 'typeorm';
import { ModelTypeTextModel } from '../../../../lib/entities/model-type-text-model';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';

const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const stage = getEnvVarWithAssert('STAGE');
let dataSource: DataSource | undefined = undefined;
const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const fetchModelTypeTextsFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const isoLanguageCode = event.pathParameters ? event.pathParameters['isoLanguageCode'] : undefined;
  if (!isoLanguageCode) {
    logger.log(LogLevel.WARN, '"isoLanguageCode" path param is required but missing', { data: event });
    return sendFail(
      { message: 'Missing "isoLanguageCode" path parameter', status: 400, reqHeaders: event.headers },
      logger,
    );
  }

  const userAttributes = getAuthContext({ event }, logger);

  //look at the first entry for the cora application and take the visibility from there
  const visibilityLevel = userAttributes?.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;

  //get the org id of the user from auth context
  const ppnId = userAttributes?.organizationId;

  if (!ppnId || !visibilityLevel) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId or visibility level', { data: event });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  try {
    //get allowed importers
    const allowedImporters = await getAllowedImporters(
      {
        dynamoDb: dynamoDb,
        event: event,
      },
      logger,
    );

    if (allowedImporters.length <= 0) {
      return sendFail(
        {
          message: 'No allowed importers, something is wrong with your permissions',
          status: 400,
          reqHeaders: event.headers,
        },
        logger,
      );
    }

    //init rds datasorce
    if (!dataSource) {
      dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [
        ModelTypeVisibilityModel,
        ModelTypeTextModel,
      ]);
    }

    //always get texts for 'en-DE' and ggid 1 for now
    //get all model type texts that map to allowed model type visibilities
    const modelTypeTexts = await getAllowedMttsForLanguage('en-DE', 1, visibilityLevel, allowedImporters);
    logger.log(
      LogLevel.INFO,
      `Collected ${modelTypeTexts.length} ModelTypeTexts for ${isoLanguageCode}, now checking match to ModelType Visibilities.`,
    );

    //overwrite lang code with requested one so frontend is not confused
    modelTypeTexts.forEach((mtt) => (mtt.iso_language_code = isoLanguageCode));

    logger.log(LogLevel.TRACE, 'Successfully loaded mtts that match allowed mtvs', { data: modelTypeTexts });

    return sendSuccess({ body: modelTypeTexts, reqHeaders: event.headers }, logger);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error loading model type texts', { data: error });
    return sendFail({ message: 'Error loading model types', status: 500, reqHeaders: event.headers }, logger);
  }
};

async function getAllowedMttsForLanguage(
  isoLanguageCode: string,
  ggid: number,
  visibilityLevel: string,
  allowedImporters: CoraOrgRelModel[],
): Promise<ModelTypeTextModel[]> {
  const mtv_from = new Date().toISOString().split('T', 1)[0];

  const queryBuilder = dataSource!
    .getRepository(ModelTypeTextModel)
    .createQueryBuilder('mtt')
    .innerJoin(
      //1. query and filter mtvs first as subquery
      (subQuery) => {
        return subQuery
          .select(['mtv.model_type', 'mtv.my4'])
          .from(ModelTypeVisibilityModel, 'mtv')
          .where('mtv.role = :role', { role: visibilityLevel })
          .andWhere('mtv.importer_number IN (:...importerNumbers)', {
            importerNumbers: allowedImporters.map((imp) => imp.importer_number),
          })
          .andWhere('mtv.valid_from <= :valid_from', { valid_from: mtv_from })
          .distinct();
      },
      //2. join the mtv query to the mtt query
      'mtv',
      'mtt.model_type = mtv.mtv_model_type AND CAST(mtt.model_year AS TEXT) = mtv.mtv_my4',
    )
    //3. apply filters to the mtt query
    .where('mtt.iso_language_code = :isoLanguageCode', { isoLanguageCode })
    .andWhere('mtt.ggid = :ggid', { ggid })
    .select([
      'mtt.iso_language_code',
      'mtt.ggid',
      'mtt.model_year',
      'mtt.model_type',
      'mtt.model_text',
      'mtt.language_code',
      'mtt.importer_number',
      'mtt.cnr',
    ]);

  return await queryBuilder.getMany();
}

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('fetch-model-type-texts', LogLevel.TRACE)(event, context, fetchModelTypeTextsFunc);
