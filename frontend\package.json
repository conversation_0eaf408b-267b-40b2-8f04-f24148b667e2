{"name": "cora", "version": "1.2.0", "private": true, "dependencies": {"@js-joda/core": "^5.6.4", "@kas-resources/react-components": "^0.7.14", "@porsche-design-system/components-react": "^3.26.0", "@reduxjs/toolkit": "^2.5.1", "@tippyjs/react": "^4.2.6", "ag-grid-community": "^32.0.4", "ag-grid-enterprise": "^32.0.4", "ag-grid-react": "^32.0.4", "aws": "^0.0.3-2", "buffer": "^6.0.3", "copy-to-clipboard": "^3.3.3", "date-fns": "^4.1.0", "eslint": "^9.19.0", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.2", "react": "^19.0.0", "react-csv": "^2.2.2", "react-diff-viewer-continued": "^4.0.5", "react-dom": "^19.0.0", "react-i18next": "^15.4.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-select": "^5.10.0", "uuid": "^11.0.5", "web-vitals": "^4.2.4"}, "scripts": {"prettier": "npm run format:check", "format:check": "prettier --check .", "prettier:write": "npm run format:write", "format:write": "prettier --write .", "prestart": "npm run replace", "prebuild": "npm run replace", "replace": "sh ./pag_production_design_index_replace.sh", "start": "run-p start:dev start:api", "start:dev": "vite --port 4000", "prestart:api": "node tools/createMockDb.js", "start:api": "node tools/server.js", "build": "vite build", "test": "vite test", "eject": "vite eject", "e2e": "chmod +x connectToBastionHostForE2ETest.sh && ./connectToBastionHostForE2ETest.sh && npx cypress run"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@aws-sdk/client-dynamodb": "^3.741.0", "@aws-sdk/client-scheduler": "^3.741.0", "@aws-sdk/client-secrets-manager": "^3.741.0", "@aws-sdk/client-ssm": "^3.741.0", "@aws-sdk/lib-dynamodb": "^3.741.0", "@aws-sdk/util-dynamodb": "^3.741.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.13.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/styled-components": "^5.1.34", "@types/totp-generator": "^0.0.8", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "cypress": "^13.8.1", "cypress-terminal-report": "^7.1.0", "jsdom": "^26.0.0", "json-server": "^0.17.4", "jsonwebtoken": "^9.0.2", "npm-run-all": "^4.1.5", "pg": "^8.13.1", "prettier": "^3.4.2", "replace": "^1.2.2", "rollup-plugin-analyzer": "^4.0.0", "sass-embedded": "^1.83.4", "totp-generator": "^1.0.0", "typeorm": "^0.3.20", "typescript": "^5.7.3", "vite": "^6.0.11", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.5"}}