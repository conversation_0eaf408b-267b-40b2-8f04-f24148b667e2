import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';

import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  InboundEventHandlerEvent,
  InboundEventHandlerUnparsableEvent,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SpecialStatusCode,
  TotalLossHandlerResult,
  UserOrderActionEventHandlerEvent,
} from '../../../../../lib/types/process-steering-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { EventHandlerContext } from '../event-handler-context';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { EventHandlerValidationContext } from '../validation-context';
import { saveNcosWithAuditTrail } from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { EntityManager } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { InboundProcessMappingModel } from '../../../../../lib/entities/inbound-mapping-model';

EventHandlerContext.init(OneVmsEventHandlerKey.TOTAL_LOSS_REVOKE, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
  InboundProcessMappingModel,
]);

const sqsEventValidator = new ObjectValidator<UserOrderActionEventHandlerEvent>('UserOrderActionEventHandlerEvent');

const totalLossRevokeFunc = async (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  event: SQSEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  /**
   * @deprecated use EventHandlerContext.logger instead
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  logger: KasLambdaLogger,
  // eslint-disable-next-line @typescript-eslint/require-await
): Promise<SQSBatchResponse | void> => {
  const outboundStatusUpdateStatement = await EventHandlerContext.getOutboundStatusUpdateStatement(
    TotalLossHandlerResult.SUCCESS,
    true,
  );
  const inboundStatusMappings = await (await EventHandlerContext.getDataSource())
    .getRepository(InboundProcessMappingModel)
    .findBy({ target_event_handler: EventHandlerContext.eventHandlerKey });
  const inboundStatus = inboundStatusMappings.find((m) => m.event === OneVmsEventKey.TOTAL_LOSS_REVOKE);
  if (!inboundStatus) {
    EventHandlerContext.logger.log(
      LogLevel.ERROR,
      'No inbound status mapping found, cannot revoke total loss on any order',
    );
    throw Error('Internal Error, No inbound status mapping found, cannot revoke total loss on any order');
  }

  const { unParseableEvents, parsedEvents: parsedEvents } = EventHandlerContext.commonEventInputValidation(
    event,
    sqsEventValidator,
  );
  const expectedFailedEvents: InboundEventHandlerEvent[] = [];
  const unexpectedFailedEvents: InboundEventHandlerUnparsableEvent[] = [];
  const successfulEvents: (InboundEventHandlerEvent & { messageId: string })[] = [];

  /**
   * 1. Validate if user is allowed on orders
   * 2. Change Status
   * 3. Delete Eventbridge Schedule? -> No
   * 4. Send Result
   */

  const ncoRepo = (await EventHandlerContext.getDataSource()).getRepository(NewCarOrderModel);
  const updatedNcos: NewCarOrderModel[] = [];
  for (const reportRequest of parsedEvents) {
    try {
      // Fetch source nco from the database
      const sourceNco = await ncoRepo.findOneBy({
        pk_new_car_order_id: reportRequest.nco_id,
      });

      // Validate Request
      if (!sourceNco) {
        const message = 'Failed to find order with id: ' + reportRequest.nco_id;
        EventHandlerContext.logger.log(LogLevel.FATAL, message, { data: reportRequest });
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      if (new Date(sourceNco.modified_at ?? 0).toISOString() !== reportRequest.modified_at) {
        const message = 'Nco was changed by someone else since the event was created';
        EventHandlerContext.logger.log(LogLevel.WARN, message, { data: { sourceNco, reportRequest } });
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      if (
        !(await EventHandlerValidationContext.authorizedForOrder(
          reportRequest.user_auth_context.organizationId,
          sourceNco,
        ))
      ) {
        const message = 'User is not authorized for provided dealer or importer number';
        EventHandlerContext.logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      // Find Status to go back to (before Report)
      EventHandlerContext.logger.log(LogLevel.DEBUG, 'Fetching audit trail for order', {
        data: sourceNco.pk_new_car_order_id,
      });
      const auditTrail = await (
        await EventHandlerContext.getDataSource()
      )
        .getRepository(NewCarOrderAuditTrailModel)
        .createQueryBuilder('new_car_order_audit_trail')
        .where('new_car_order_audit_trail.pk_new_car_order_id = :id', { id: sourceNco.pk_new_car_order_id })
        .andWhere('new_car_order_audit_trail.action_type = :action_type', {
          action_type: NcoExportActionType.REPORT_LOSS,
        })
        .orderBy('new_car_order_audit_trail.action_at', 'DESC')
        .limit(1)
        .getOne();

      if (!auditTrail?.old_nco) {
        const _msg = 'Failed to find before report data in audit trail';
        EventHandlerContext.logger.log(LogLevel.ERROR, _msg, { data: sourceNco.pk_new_car_order_id });
        unexpectedFailedEvents.push({ ...reportRequest, errorMessage: _msg });
        continue;
      }

      const copyOutboundStatusUpdateStatment = JSON.parse(
        JSON.stringify(outboundStatusUpdateStatement),
      ) as QueryDeepPartialEntity<NewCarOrderModel>;
      for (const [k, v] of Object.entries(copyOutboundStatusUpdateStatment)) {
        if (v === SpecialStatusCode.CUSTOM) {
          //@ts-expect-error @description k is a key of outboundStatusUpdateStatment, but ts does not think so for some reason
          copyOutboundStatusUpdateStatment[k] = auditTrail.old_nco[k as keyof NewCarOrderModel];
        }
      }
      // Save to DB
      const res = await saveNcosWithAuditTrail(
        await EventHandlerContext.getDataSource(),
        [sourceNco.pk_new_car_order_id],
        NcoExportActionType.REVOKE_LOSS,
        async (transactionManager: EntityManager) => {
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: sourceNco.pk_new_car_order_id },
            {
              changed_by_system: OneVmsSourceSystemKey.CORA,
              order_status_onevms_timestamp_last_change: new Date().toISOString(),
              modified_by: reportRequest.user_auth_context.username,
              ...copyOutboundStatusUpdateStatment,
            },
          );

          // Reload and return updated orders to ensure all orders were changed correctly
          return transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: sourceNco.pk_new_car_order_id,
              modified_by: reportRequest.user_auth_context.username,
            },
          });
        },
        EventHandlerContext.logger,
        false,
      );

      EventHandlerContext.logger.log(
        LogLevel.INFO,
        `Action of NewCarOrder with NCO id ${sourceNco.pk_new_car_order_id} was executed successfully.`,
      );
      updatedNcos.push(...res);
      successfulEvents.push(reportRequest);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }
      const message = 'Unexpected database error occurred';
      EventHandlerContext.logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...reportRequest, errorMessage: message });
      continue;
    }
  }

  return await EventHandlerContext.sendNotifications({
    eventKey: OneVmsEventKey.TOTAL_LOSS_REVOKE,
    expectedFailedEvents,
    unexpectedFailedEvents,
    unParseableEvents,
    successfulEvents,
  });
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, totalLossRevokeFunc);
