import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ivider, PText } from '@porsche-design-system/components-react';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraNCOCopyApiResponse } from '../../../../infrastructure/lib/types/new-car-order-types';
import { CopyResultFailed } from '../../store/types';
import './CopyOrdersList.css';

export interface CopyDetailsMultiContainerProps {
  orders: CoraNCOCopyApiResponse | undefined;
  heading: string;
  error?: string;
  type: boolean;
  totalQuota: number;
  totalQuotaConsumed: number;
}

const CopyOrdersList: React.FC<CopyDetailsMultiContainerProps> = (props: CopyDetailsMultiContainerProps) => {
  const { t } = useTranslation();
  const [showAllOrders, setShowAllOrders] = useState<boolean>(false);
  const [isCopyBannerOpen, setIsCopyBannerOpen] = useState(false);
  const accordionBtnRef: React.Ref<HTMLElement> = useRef(null);

  //used to make accordion button show label and icon on the right
  const style = document.createElement('style');
  style.innerHTML = 'button { justify-content: flex-end !important; }';

  useEffect(() => {
    accordionBtnRef.current?.shadowRoot?.appendChild(style);
  }, [style]);

  const copiedOrders: string[] = []; // Storing order IDs that were copied successfully
  const failedCopyOrders: CopyResultFailed[] = []; // Storing order IDs that failed
  if (props.orders) {
    props.orders.forEach((result) => {
      if (result.quota_failed) {
        failedCopyOrders.push({
          quota_month: result.quota_month,
          quota_failed: result.quota_failed.toString(),
        });
      }

      if (result.new_car_order_ids && result.new_car_order_ids.length > 0) {
        copiedOrders.push(...result.new_car_order_ids);
      }
    });
  }
  const copyOrderIds = () => {
    navigator.clipboard.writeText(copiedOrders?.map((order) => order).join(', '));
    setIsCopyBannerOpen(true);
  };

  const MAX_DEFAULT_VISIBLE_ROWS = 5;

  //split orders array in half because UI/UX bullshit
  const halfPoint = Math.ceil(copiedOrders?.length / 2);
  let ordersFirstRow = copiedOrders?.slice(0, halfPoint);
  let ordersSecondRow = copiedOrders?.slice(halfPoint);

  //cut off after MAX if accordion is collapsed
  if (!showAllOrders) {
    ordersFirstRow = ordersFirstRow.slice(0, MAX_DEFAULT_VISIBLE_ROWS);
    ordersSecondRow = ordersSecondRow.slice(0, MAX_DEFAULT_VISIBLE_ROWS);
  }

  return props.orders && props.orders.length > 0 ? (
    <div className="copy-orders-list-container">
      {copiedOrders.length > 0 && (
        <PText size="x-small" weight={'semi-bold'}>
          {props.heading}
        </PText>
      )}
      {props.error && (
        <PText size="x-small" weight={'semi-bold'} color="notification-error">
          {props.error}
        </PText>
      )}
      {props.type && copiedOrders.length > 0 && (
        <>
          <PButtonPure
            data-e2e="copy_ids"
            size="x-small"
            alignLabel="start"
            icon="copy"
            onClick={() => {
              copyOrderIds();
            }}
          >
            {t('copy_ids')}
          </PButtonPure>
          <PBanner
            open={isCopyBannerOpen}
            dismissButton={false}
            description={t('copy_ids_success')}
            state="info"
            onTransitionEnd={(): void => {
              setTimeout(() => {
                setIsCopyBannerOpen(false);
              }, 1000);
            }}
          />
        </>
      )}
      <PText color="contrast-medium" size={'x-small'} weight={'regular'} style={{ paddingTop: '15px' }}>
        {props.type && copiedOrders.length > 0 && t('pk_new_car_order_id')}
        {!props.type && `${t('quota_month')} | ${t('quota_failed')}`}
      </PText>
      {!props.type && (
        <div className="copy-orders-list-container">
          <div className="col-container">
            <div className="field-col">
              {failedCopyOrders.map((order, i) => (
                <div className="quota-row" key={i}>
                  <span className="quota-month">{order.quota_month}</span>
                  <span className="quota-pipe">|</span>
                  <span className="quota-failed">{order.quota_failed}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
      {props.type && (
        <div className="col-container">
          <div className="field-col">
            {ordersFirstRow.map((order, i) => (
              <PText size={'x-small'} weight={'bold'} key={i}>
                {order}
              </PText>
            ))}
          </div>
          <div className="field-col">
            {ordersSecondRow.map((order, i) => (
              <PText size={'x-small'} weight={'bold'} key={i}>
                {order}
              </PText>
            ))}
          </div>
        </div>
      )}
      {halfPoint > MAX_DEFAULT_VISIBLE_ROWS && props.type && (
        <PButtonPure
          ref={accordionBtnRef}
          className="accordion-btn"
          size="x-small"
          alignLabel="start"
          icon={`arrow-head-${showAllOrders ? 'up' : 'down'}`}
          onClick={() => {
            setShowAllOrders(!showAllOrders);
          }}
        >
          {t(showAllOrders ? 'show_less' : 'show_all')}{' '}
        </PButtonPure>
      )}
      <PDivider style={{ paddingTop: '10px', paddingBottom: '10px' }}></PDivider>
    </div>
  ) : (
    <div></div>
  );
};

export default CopyOrdersList;
