#!/bin/sh
# Before bootstraping the resources account, you need to set the value of  "@aws-cdk/core:bootstrapQualifier": "<PROJECT_NAME>" in the cdk.json
export AWS_RES_ACCOUNT=************
export AWS_RES_PROFILE=${AWS_RES_ACCOUNT}_PA_DEVELOPER
export PROJECT_NAME=cora-oc
aws iam create-policy \
    --policy-name ${PROJECT_NAME}-service-pipelinedeploy \
    --policy-document file://bootstrap/pipeline_policy.json \
    --profile $AWS_RES_PROFILE > /dev/null && \
cdk bootstrap \
    --profile $AWS_RES_PROFILE \
    --qualifier $PROJECT_NAME \
    --toolkit-stack-name $PROJECT_NAME-cdk \
    --cloudformation-execution-policies arn:aws:iam::${AWS_RES_ACCOUNT}:policy/${PROJECT_NAME}-service-pipelinedeploy \
    aws://${AWS_RES_ACCOUNT}/eu-west-1 