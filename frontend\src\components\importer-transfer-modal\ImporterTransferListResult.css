.importer-transfer-orders-list-container {
  display: flex;
  flex-direction: column;
}

.importer-transfer-orders-list-container .col-container {
  display: flex;
  flex-direction: row;
}

.importer-transfer-orders-list-container .field-col {
  flex: 1; /* Makes both columns take up equal space */
  display: flex;
  flex-direction: column;
  min-width: 200px; /* Ensures they don't collapse */
}

.importer-transfer-orders-list-container .accordion-btn {
  width: 100%;
  margin-top: 10px;
}

.importer-transfer-orders-list-container .quota-row {
  display: grid;
  grid-template-columns: 75px 20px auto; /* Three columns: month, pipe, failed */
  gap: 4px; /* Space between columns */
  align-items: center; /* Aligns all items in the row vertically */
  font-family: monospace; /* Consistent character width */
}

.importer-transfer-orders-list-container .quota-month,
.importer-transfer-orders-list-container .quota-pipe,
.importer-transfer-orders-list-container .quota-failed {
  text-align: left; /* Aligns text to the left */
}
.order-row {
  display: flex;
  align-items: center;
  gap: 8px; /* Keeps the text and icon spaced */
}
