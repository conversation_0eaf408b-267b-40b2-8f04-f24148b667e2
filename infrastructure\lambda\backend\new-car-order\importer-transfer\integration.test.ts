/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DataSource, In } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import {
  CoraNCOBaseApiRequest,
  CoraNCOImporterTransferApiRequest,
  CoraNCOImporterTransferApiResponse,
} from '../../../../lib/types/new-car-order-types';
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  createStandartOrgRelPattern,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';
import { ncoApiToDbObj } from '../../../utils/utils-typeorm';
import { OneVmsSourceSystemKey } from '../../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn('nco-importer-transfer');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('importer-transfer');

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const scPks = Object.values(scs).map((sc) => ({
  pk_shipping_code: sc.pk_shipping_code,
}));

const otPks = Object.values(ots).map((ot) => ({
  pk_order_type: ot.pk_order_type,
}));

const defaultEventParams = {
  queryStringParameters: { mock_quota_api: 'true' },
};

const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

//collect created ncoids for deletion after tests
const createdNcoids: string[] = [];

let dataSource: DataSource;

const existingNcoId = 'ITSOMENCO';

const sourceNewCarOrder: NewCarOrderModel = {
  pk_new_car_order_id: existingNcoId,
  dealer_number: ids.dealer01.md_org.sk_dealer_number,
  importer_code: 'IT',
  importer_number: ids.importer.md_org.pk_importer_number,
  created_by: 'prepare_integration_test',
  modified_by: 'TestRunner',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'ItOtWhiteAllowCp',
  modified_at: '2025-03-19T12:22:24.807Z',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'ItScBlackAllowCp',
  receiving_port_code: 'ItNcoPc1',
  order_status_onevms_code: 'PP2000',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2024-10-12T00:00:00.000Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  cnr: 'C23',
  configuration: {
    created_by: 'prepare_integration_test',
    modified_by: 'TestRunner',
    ordered_options: [
      {
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        created_by: 'prepare_integration_test',
        modified_by: 'TestRunner',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            option_id: 'CONTENT1',
            option_type: 'CONTENTTYPE1',
            validity_valid_until: '2050-12-20',
            validity_valid_from: '2022-12-20',
            validity_serial_to: '2050-12-20',
            validity_serial_from: '2022-12-20',
            validity_offer_period_start: '2022-12-20',
            validity_offer_period_end: '2050-12-20',
            validity_material_lead_time: '0',
            created_by: 'prepare_integration_test',
            modified_by: 'TestRunner',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
          {
            option_id: 'OPTION4',
            option_type: Constants.CORA_NEW_CAR_ORDER_COFIG_OPTION_LOCAL,
            created_by: 'prepare_integration_test',
            modified_by: 'TestRunner',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
        ],
      },
      {
        option_id: 'OPTION5',
        option_type: Constants.CORA_NEW_CAR_ORDER_COFIG_OPTION_LOCAL,
        created_by: 'prepare_integration_test',
        modified_by: 'TestRunner',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
      },
    ],
  },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
  ]);
  const repository = dataSource.getRepository(NewCarOrderModel);
  await repository.save(
    ncoApiToDbObj(sourceNewCarOrder as unknown as CoraNCOBaseApiRequest, 'IntegrationTester', sourceNewCarOrder),
  );
  createdNcoids.push(sourceNewCarOrder.pk_new_car_order_id);
  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    { tableName: mdDlrTableName, objs: [ids.dealer01.md_org, ids.dealer02.md_org, ids.dealer03.md_org] },
    { tableName: mdImpTableName, objs: [ids.importer.md_org, ids.importer02.md_org] },
    { tableName: mdScTableName, objs: Object.values(scs) },
    { tableName: mdOtTableName, objs: Object.values(ots) },
  ]);
});

afterAll(async () => {
  await dataSource.getRepository(NewCarOrderModel).delete({ pk_new_car_order_id: In(createdNcoids) });
  await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({ pk_new_car_order_id: In(createdNcoids) });
  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: mdDlrTableName,
      pks: [
        {
          pk_importer_number: ids.dealer01.md_org.pk_importer_number,
          sk_dealer_number: ids.dealer01.md_org.sk_dealer_number,
        },
      ],
    },
    {
      tableName: mdImpTableName,
      pks: [{ pk_importer_number: ids.importer.md_org.pk_importer_number }],
    },
    {
      tableName: mdScTableName,
      pks: scPks,
    },
    {
      tableName: mdOtTableName,
      pks: otPks,
    },
  ]);
});

const defaultRequest: CoraNCOImporterTransferApiRequest = {
  new_car_order_ids: {
    [existingNcoId]: sourceNewCarOrder.modified_at ? sourceNewCarOrder.modified_at : '',
  },
  new_nco_attributes: {},
  to_dealer_number: ids.dealer03.md_org.sk_dealer_number,
  to_importer_number: ids.importer02.md_org.pk_importer_number,
};

it('should return 200', async () => {
  const event = createApiGwEvent({
    ...defaultEventParams,
    userOrgId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    body: defaultRequest as unknown as Record<string, unknown>,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body) as CoraNCOImporterTransferApiResponse;
  for (const b of body) {
    if (b.new_new_car_order_id) {
      createdNcoids.push(b.new_new_car_order_id);
    }
  }
  expect(body.length).toBe(1);
  expect(body[0].isSuccess).toBeTruthy();
});

it('should return 400 - No NcoIds', async () => {
  const event = createApiGwEvent({
    ...defaultEventParams,
    userOrgId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    body: { ...defaultRequest, new_car_order_ids: [] } as unknown as Record<string, unknown>,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
});
