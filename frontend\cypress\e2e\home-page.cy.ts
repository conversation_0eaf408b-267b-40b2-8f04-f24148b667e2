import { USERNAME_WRITE, USERNAME_READ, FULLNAME_WRITE, FULLNAME_READ } from '../support/constants';

describe('Home page with read role', () => {
  beforeEach(() => {
    cy.login(USERNAME_READ);
  });

  it('View home page as reader', () => {
    cy.visit('/');
    cy.getUserMenuBtn().contains(FULLNAME_READ);
    cy.getUserMenuBtn().trigger('mouseover');
  });
});

describe('Home page with write role', () => {
  beforeEach(() => {
    cy.login(USERNAME_WRITE);
  });

  it('View home page as writer', () => {
    cy.visit('/');
    cy.getUserMenuBtn().contains(FULLNAME_WRITE);
    cy.getUserMenuBtn().trigger('mouseover');
  });
});
