{"$id": "NewCarOrderKafkaObject", "$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"ids": {"type": "object", "properties": {"new_car_order_id": {"type": "string"}, "commission_id": {"type": "string"}, "business_partner_id": {"type": "string"}}, "required": ["new_car_order_id"], "additionalProperties": false}, "model_info": {"type": "object", "properties": {"model_type": {"type": "string"}, "model_year": {"type": "number"}, "country_code": {"type": "string"}}, "required": ["model_type", "model_year", "country_code"], "additionalProperties": false}, "order_info": {"type": "object", "properties": {"base_info": {"type": "object", "properties": {"order_type": {"type": "string"}, "quota_month": {"type": "string"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}, "cancellation_reason": {"type": "string"}}, "required": ["order_type", "quota_month", "created_by"], "additionalProperties": false}, "trading_partner": {"type": "object", "properties": {"importer_code": {"type": "string"}, "importer_number": {"type": "string"}, "dealer_sold_to_number": {"type": "string"}, "dealer_ship_to_number": {"type": "string"}}, "required": ["importer_code", "importer_number", "dealer_sold_to_number", "dealer_ship_to_number"], "additionalProperties": false}}, "required": ["base_info", "trading_partner"], "additionalProperties": false}, "logistics_info": {"type": "object", "properties": {"shipping_code": {"type": "string"}, "receiving_port_code": {"type": "string"}}, "required": ["shipping_code"], "additionalProperties": false}, "status_info": {"type": "object", "properties": {"order_status_code": {"type": "string"}, "order_status_timestamp": {"type": "string"}, "order_error_status_code": {"type": "string"}, "order_error_status_timestamp": {"type": "string"}, "order_invoice_status_code": {"type": "string"}, "order_invoice_status_timestamp": {"type": "string"}}, "required": ["order_status_code", "order_status_timestamp"], "additionalProperties": false}, "appointment_date_info": {"type": "object", "properties": {"production_logistic_dates": {"type": "object", "properties": {"order_creation_timestamp": {"type": "string"}, "order_last_modification_timestamp": {"type": "string"}, "requested_dealer_delivery_date": {"type": "string"}}, "required": ["order_creation_timestamp"], "additionalProperties": false}}, "required": ["production_logistic_dates"], "additionalProperties": false}, "configuration": {"type": "object", "description": "Min 1", "required": ["ordered_options"], "properties": {"ordered_options": {"type": "array", "description": "Min 1", "items": {"type": "object", "description": "Min 1 bis n", "required": ["option_id", "option_type"], "properties": {"option_id": {"type": "string", "example": "Q1K"}, "option_type": {"type": "string", "example": "Individual"}, "referenced_package": {"type": "string", "example": "P81"}, "referenced_package_type": {"type": "string", "example": "ContentOf"}, "referenced_package_sort_order": {"type": "string", "example": "3"}, "option_subtype_value": {"type": "string", "example": "freitext"}, "option_validity": {"type": "object", "properties": {"valid_until": {"type": "string", "example": "2024-07-15"}, "valid_from": {"type": "string", "example": "2024-07-01"}, "serial_to": {"type": "string", "example": "2024-07-15"}, "serial_from": {"type": "string", "example": "2024-07-15"}, "offer_period_start": {"type": "string", "example": "2024-07-10"}, "offer_period_end": {"type": "string", "example": "2024-07-10"}, "material_lead_time": {"type": "string", "example": "30"}}}, "content": {"type": "array", "required": ["package_content_sort_order", "option_id"], "items": {"type": "object", "properties": {"package_content_sort_order": {"type": "number", "example": 1}, "option_id": {"type": "string", "example": "04G"}}}}}}}}}, "configuration_expire": {"type": "object", "properties": {"Id": {"type": "string", "example": "APP-1104"}, "Cnr": {"type": "string", "example": "C00"}, "Importernr": {"type": "string", "example": "9690000"}, "Dealernr": {"type": "string", "nullable": true}, "Importerid": {"type": "string", "example": "DE"}, "PpnUserId": {"type": "string", "example": ""}, "BpId": {"type": "string", "example": ""}, "IsDealerConfig": {"type": "string", "example": ""}, "LeadId": {"type": "string", "example": ""}, "ProspectKit": {"type": "boolean", "example": false}, "Modeltype": {"type": "string", "example": "992120"}, "Modelyear": {"type": "string", "example": "R"}, "ConfigName": {"type": "string", "example": ""}, "CofferId": {"type": "string", "example": ""}, "TeqId": {"type": "string", "example": ""}, "PorscheCode": {"type": "string", "example": ""}, "TotalPriceGross": {"type": "string", "example": "137963.00"}, "Taxes": {"type": "string", "example": "22027.71"}, "Currency": {"type": "string", "example": "€"}, "ConfigModified": {"type": "string", "example": "1"}, "CommentsModified": {"type": "string", "example": "0"}, "ExteriorColor": {"type": "string", "example": "0Q"}, "TopColor": {"type": "string", "example": "0Q"}, "InteriorColor": {"type": "string", "example": "AE"}, "FreeZOffer": {"type": "string", "example": ""}, "VehicleOption": {"type": "object", "properties": {"Id": {"type": "string", "example": "1"}, "OptionIndividual": {"type": "object", "properties": {"results": {"type": "array", "items": {}}}}, "OptionExclusive": {"type": "object", "properties": {"results": {"type": "array", "items": {}}}}, "OptionCustomTailoring": {"type": "object", "properties": {"results": {"type": "array", "items": {}}}}, "OptionLocal": {"type": "object", "properties": {"results": {"type": "array", "items": {}}}}, "OptionZoffer": {"type": "object", "properties": {"results": {"type": "array", "items": {}}}}, "OptionPackage": {"type": "object", "properties": {"results": {"type": "array", "items": {}}}}}}, "Vguid": {"type": "string", "example": ""}, "VehicleWLTPHeader": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"uuid": {"type": "string", "example": "a0086161-82af-4e63-a80c-ebf93a5472c0"}, "response_date": {"type": "string", "example": "\\/Date(1719243470092)\\/"}, "return_code": {"type": "string", "example": "200"}, "error_message": {"type": "string", "example": "OK"}, "check_date": {"type": "string", "example": "\\/Date(1717200000000)\\/"}, "DataSource": {"type": "string", "example": "P"}}}}}}, "VehicleWLTPBody": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"Typification": {"type": "string", "example": "WLTP_EU"}, "EngineType": {"type": "string", "example": "ICE"}, "DataSource": {"type": "string", "example": "P"}, "WLTPBodyWLTPBodyRecord": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"DataType": {"type": "string", "example": "GENERAL_DATA"}, "DataSource": {"type": "string", "example": "P"}, "WLTPBodyRecordWLTPDataRecord": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"ValueType": {"type": "string", "example": ""}, "FuelType": {"type": "string", "example": ""}, "EnergyManagementType": {"type": "string", "example": ""}, "DataSource": {"type": "string", "example": "P"}, "WLTPDataRecordWLTPValue": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"Key": {"type": "string", "example": "TIRE_ROLLING_RESISTANCE_TOTAL"}, "Value": {"type": "string", "example": "9.8"}, "ValueFrom": {"type": "string", "example": ""}, "ValueTo": {"type": "string", "example": ""}, "Unit": {"type": "string", "example": "0/00"}, "DataSource": {"type": "string", "example": "P"}}}}}}}}}}}}}}}}}}}}}, "VehicleTag": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"Key": {"type": "string", "example": "KB"}, "Value": {"type": "string", "example": "PCCDP"}}}}}}, "XTraceId": {"type": "string", "example": "cef998e7-f38a-4d13-b546-5d515201e07d"}}}}, "required": ["ids", "model_info", "order_info", "logistics_info", "status_info", "appointment_date_info", "configuration", "configuration_expire"], "additionalProperties": true, "examples": [{"ids": {"new_car_order_id": "PEZOSQ6A"}, "logistics_info": {"shipping_code": "1"}, "model_info": {"model_type": "992110", "model_year": 2024, "country_code": "C99"}, "order_info": {"base_info": {"order_type": "LF", "quota_month": "2024-11", "created_by": "p358886"}, "trading_partner": {"importer_code": "PE", "dealer_sold_to_number": "2600001", "dealer_ship_to_number": "2600001", "importer_number": "2600000"}}, "status_info": {"order_status_code": "PP2000", "order_status_timestamp": "2024-07-10T13:40:28.057Z"}, "appointment_date_info": {"production_logistic_dates": {"order_creation_timestamp": "2024-07-10T13:40:17.295Z"}}, "configuration": {"ordered_options": [{"option_type": "Exterior Color", "option_id": "0Q"}, {"option_type": "Top Color", "option_id": "0Q"}, {"option_type": "Interior Color", "option_id": "AE"}]}, "configuration_expire": {"Cnr": "C99", "XTraceId": "556b3ff2-e90b-42d7-9448-f1bc4c359488", "Importerid": "PE", "VehicleTag": {"results": [{"Value": "PCCDP", "Key": "KB"}]}, "VehicleWLTPHeader": {"results": [{"error_message": "OK", "response_date": "\\/Date(1720618804439)\\/", "check_date": "\\/Date(1730419200000)\\/", "uuid": "d1f8e062-0e45-4b0d-93b0-f67298e09494", "return_code": "200", "DataSource": "P"}]}, "InteriorColor": "AE", "ConfigName": "", "PpnUserId": "", "Currency": "PLN", "TopColor": "0Q", "LeadId": "", "IsDealerConfig": "", "ExteriorColor": "0Q", "TotalPriceGross": "611000.00", "VehicleWLTPBody": {"results": [{"EngineType": "ICE", "WLTPBodyWLTPBodyRecord": {"results": [{"WLTPBodyRecordWLTPDataRecord": {"results": [{"EnergyManagementType": "", "ValueType": "", "WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "9.8", "ValueTo": "", "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_TOTAL", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_FRONT", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_REAR", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "", "Key": "DRAG_COEFFICIENT", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "", "Key": "DRAG_COEFFICIENT_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "0.603", "ValueTo": "", "Unit": "m2", "Key": "AERODYNAMIC_DRAG", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "m2", "Key": "AERODYNAMIC_DRAG_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "2.07", "ValueTo": "", "Unit": "m2", "Key": "FRONT_SURFACE_A", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "m2", "Key": "FRONT_SURFACE_A_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "1523", "ValueTo": "", "Unit": "kg", "Key": "MASS_VEHICLE", "DataSource": "P"}, {"ValueFrom": "", "Value": "563", "ValueTo": "", "Unit": "kg", "Key": "MASS_VEHICLE_FRONT", "DataSource": "P"}, {"ValueFrom": "", "Value": "961", "ValueTo": "", "Unit": "kg", "Key": "MASS_VEHICLE_REAR", "DataSource": "P"}, {"ValueFrom": "", "Value": "1598", "ValueTo": "", "Unit": "kg", "Key": "MASS_ACTUAL", "DataSource": "P"}, {"ValueFrom": "", "Value": "1580.0", "ValueTo": "", "Unit": "kg", "Key": "EU_LEER_MIN", "DataSource": "P"}, {"ValueFrom": "", "Value": "1735.0", "ValueTo": "", "Unit": "kg", "Key": "EU_LEER_MAX", "DataSource": "P"}, {"ValueFrom": "", "Value": "1653", "ValueTo": "", "Unit": "kg", "Key": "TEST_MASS_VEHICLE", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "kg", "Key": "TEST_MASS_VEHICLE_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "", "Key": "HIGH_TYPING", "DataSource": "P"}, {"ValueFrom": "", "Value": "187.0", "ValueTo": "", "Unit": "N", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F0", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "N", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F0_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "1.85", "ValueTo": "", "Unit": "N/(km/h)", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F1", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "N/(km/h)", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F1_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "0.02185", "ValueTo": "", "Unit": "N/(km/h)2", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F2", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "Unit": "N/(km/h)2", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F2_H", "DataSource": "P"}]}, "FuelType": "", "DataSource": "P"}]}, "DataType": "GENERAL_DATA", "DataSource": "P"}, {"WLTPBodyRecordWLTPDataRecord": {"results": [{"EnergyManagementType": "PURE", "ValueType": "CONSUMPTION", "WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "18.4", "ValueTo": "", "Unit": "l/100km", "Key": "LOW", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.9", "ValueTo": "", "Unit": "l/100km", "Key": "MEDIUM", "DataSource": "P"}, {"ValueFrom": "", "Value": "8.6", "ValueTo": "", "Unit": "l/100km", "Key": "HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "8.8", "ValueTo": "", "Unit": "l/100km", "Key": "EXTRA_HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "10.3", "ValueTo": "", "Unit": "l/100km", "Key": "COMBINED", "DataSource": "P"}]}, "FuelType": "PETROL_E10", "DataSource": "P"}, {"EnergyManagementType": "PURE", "ValueType": "CO2", "WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "418", "ValueTo": "", "Unit": "g/km", "Key": "LOW", "DataSource": "P"}, {"ValueFrom": "", "Value": "225", "ValueTo": "", "Unit": "g/km", "Key": "MEDIUM", "DataSource": "P"}, {"ValueFrom": "", "Value": "195", "ValueTo": "", "Unit": "g/km", "Key": "HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "200", "ValueTo": "", "Unit": "g/km", "Key": "EXTRA_HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "233", "ValueTo": "", "Unit": "g/km", "Key": "COMBINED", "DataSource": "P"}]}, "FuelType": "PETROL_E10", "DataSource": "P"}]}, "DataType": "INTERPOLATIONS", "DataSource": "P"}, {"WLTPBodyRecordWLTPDataRecord": {"results": [{"EnergyManagementType": "", "ValueType": "", "WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "IP-AP992002B00AT00-WP0-1", "ValueTo": "", "Unit": "0/00", "Key": "NAME", "DataSource": "P"}]}, "FuelType": "", "DataSource": "P"}]}, "DataType": "IP_FAMILY", "DataSource": "P"}]}, "Typification": "WLTP_EU", "DataSource": "P"}]}, "CofferId": "", "ConfigModified": "1", "Modeltype": "992110", "Modelyear": "R", "PorscheCode": "", "CommentsModified": "0", "Importernr": "2600000", "Dealernr": "2600001", "ProspectKit": false, "Vguid": "", "TeqId": "", "Taxes": "114252.03", "BpId": "", "FreeZOffer": "", "Id": "APP-1104", "VehicleOption": {"OptionExclusive": {"results": []}, "OptionPackage": {"results": []}, "OptionIndividual": {"results": []}, "OptionLocal": {"results": []}, "Id": "1", "OptionCustomTailoring": {"results": []}, "OptionZoffer": {"results": []}}}}]}