import { PDivider, PText } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useLazyGetModelTypeTextsQuery } from '../../../store/api/BossApi';
import { AppDispatch } from '../../../store/configureStore';
import { matchMttForModelTypeAndModelYear } from '../../../utils/utils';
import { OrderTypeForm } from '../form-elements/OrderTypeForm';
import { PortCodeForm } from '../form-elements/PortCodeForm';
import { QuotaForm } from '../form-elements/QuotaForm';
import { RequestedDeliveryDateForm } from '../form-elements/RequestedDeliveryDateForm';
import { ShippingCodeForm } from '../form-elements/ShippingCodeForm';
import './OrderConvertForm.css';
import { ConvertPiApiRequest } from '../../../../../infrastructure/lib/types/process-steering-types';

interface OrderConvertFormProps {
  partialNewCarOrder: FormApiNewCarOrderConvertRequest;
  onFormChange: (order: FormApiNewCarOrderConvertRequest) => void;
  setError: (err?: string) => void;
}
const neededOrderConvertRequestKeys = [
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'cnr',
  'importer_code',
] as const;
type NeededOrderConvertRequestKeys = (typeof neededOrderConvertRequestKeys)[number];
export type FormApiNewCarOrderConvertRequest = Partial<ConvertPiApiRequest> &
  Pick<ConvertPiApiRequest, NeededOrderConvertRequestKeys>;

export const OrderConvertForm: React.FC<OrderConvertFormProps> = ({ partialNewCarOrder, onFormChange, setError }) => {
  const { t, i18n } = useTranslation();

  const [formState, setFormState] = useState<FormApiNewCarOrderConvertRequest>(partialNewCarOrder);
  const [subFormErrors, setSubFormErrors] = useState<Record<string, string | undefined>>({});
  const dispatch = useDispatch<AppDispatch>();

  const [loadModelTypeTexts, { data: modelTypeTexts, isLoading: isModelTypeTextsLoading }] =
    useLazyGetModelTypeTextsQuery();
  const [modelTypeText, setModelTypeText] = useState<string>(t('not_loaded'));

  //load model type texts if empty or language changed
  useEffect(() => {
    if (!isModelTypeTextsLoading) {
      if (!modelTypeTexts || (modelTypeTexts.length > 0 && modelTypeTexts[0].iso_language_code !== i18n.language)) {
        loadModelTypeTexts(i18n.language);
      }
    }
  }, [i18n.language, dispatch]);

  useEffect(() => {
    if (modelTypeTexts) {
      const modelTypeText = matchMttForModelTypeAndModelYear(
        modelTypeTexts,
        formState.model_type,
        formState.model_year,
        t,
      );
      setModelTypeText(modelTypeText ?? t('missing_translation'));
    }
  }, [modelTypeTexts]);

  function handleOrderTypeSelect(order_type: string): void {
    if (formState) setFormState({ ...formState, order_type });
  }
  function handleShippingCodeSelect(shipping_code: string): void {
    if (formState) setFormState({ ...formState, shipping_code });
  }
  function handlePortCodeSelect(port_code: string): void {
    if (formState) setFormState({ ...formState, receiving_port_code: port_code });
  }
  function handleRequestedDeliveryDateSelect(requested_delivery_date?: string): void {
    if (formState) setFormState({ ...formState, requested_dealer_delivery_date: requested_delivery_date });
  }
  function handleQuotaMonthSelect(quota_month: string): void {
    if (formState) setFormState({ ...formState, quota_month });
  }
  function handleSubFormError(area: string, err?: string) {
    subFormErrors[area] = err;
    setSubFormErrors(subFormErrors);
  }

  useEffect(() => {
    if (Object.values(subFormErrors).some((v) => !!v)) {
      console.error('Subform Errors', JSON.stringify(subFormErrors));
      setError(t('err_in_form'));
    } else {
      setError(undefined);
    }
  }, [subFormErrors]);

  useEffect(() => {
    onFormChange(formState);
  }, [formState, onFormChange]);

  if (!formState) {
    return <>{t('missing_parameters')}</>;
  }

  return (
    <>
      <div>
        <div className="order-convert-details-single-container">
          <div className="field-col">
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('model_type_text')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {modelTypeText}
              </PText>
            </div>
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('model_year')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.model_year}
              </PText>
            </div>
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('dealer_number')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.dealer_number}{' '}
              </PText>
            </div>
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('dealer_name')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.dealer_name}{' '}
              </PText>
            </div>
          </div>
          <div className="field-col">
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('model_type')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.model_type}
              </PText>
            </div>
            <div className="field-row" data-e2e="od_business_partner_id">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('business_partner_id')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.business_partner_id}
              </PText>
            </div>
          </div>
        </div>
      </div>

      <PDivider></PDivider>
      <br />
      <div className="form-container1">
        <div className="field-row1">
          <div className="field-col1">
            <OrderTypeForm
              dealer_number={formState.dealer_number}
              isCustomerRelated={true}
              selectedOrderType={formState.order_type}
              handleSelect={handleOrderTypeSelect}
              setError={handleSubFormError}
            />
          </div>
          <div className="field-col1">
            <QuotaForm
              selectedQuotaMonth={formState.quota_month}
              dealer_number={formState.dealer_number}
              importer_number={formState.importer_number}
              model_type={formState.model_type}
              model_year={formState.model_year}
              handleQuotaSelect={handleQuotaMonthSelect}
              setError={handleSubFormError}
            />
          </div>
        </div>

        <div className="field-row1">
          <div className="field-col1">
            <ShippingCodeForm
              dealer_number={formState.dealer_number}
              selectedShippingCode={formState.shipping_code}
              handleShippingCodeSelect={handleShippingCodeSelect}
              setError={handleSubFormError}
            />
          </div>
          <div className="field-col1">
            <RequestedDeliveryDateForm
              selectedQuotaMonth={formState.quota_month}
              selectedDesiredDeliveryDate={formState.requested_dealer_delivery_date ?? undefined}
              handleSelect={handleRequestedDeliveryDateSelect}
              setError={handleSubFormError}
            />
          </div>
        </div>
      </div>
      <div className="port-code-form">
        <PortCodeForm
          dealer_number={formState.dealer_number}
          selectedReceivingPortCode={formState.receiving_port_code ?? undefined}
          handleReceivingPortCodeSelect={handlePortCodeSelect}
          setError={handleSubFormError}
        />
      </div>
    </>
  );
};
