{"eslint.workingDirectories": ["./infrastructure"], "jest.jestCommandLine": "node_modules/.bin/jest --testPathPattern=local --config=jest.local.cjs", "jest.rootPath": "./infrastructure", "jest.runMode": "on-demand", "testing.openTesting": "neverOpen", "jest.outputConfig": {"revealOn": "run", "revealWithFocus": "none", "clearOnRun": "none"}, "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.defaultProfile.linux": "bash", "testing.automaticallyOpenTestResults": "neverOpen"}