import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { sendFail } from '../../../../utils/api-helpers';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGWHandlerWithInitLogger } from '../../../../utils/api-gw-handler';
import { v4 as uuidv4 } from 'uuid';
import {
  DefaultApiRequest,
  CorePayload,
  InboundEventDispatcherEvent,
  NcoInfo,
  OneVmsEventKey,
} from '../../../../../lib/types/process-steering-types';
import { EventApiContext } from '../event-api-context';
import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { ApiHandlerError } from '../../../../utils/errors';

const objectValidator = new ObjectValidator<DefaultApiRequest>('DefaultApiRequest');

EventApiContext.init(OneVmsEventKey.MOVE_TO_INVENTORY);

const moveToInventoryFunc = async (
  event: APIGatewayProxyEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  let body_validated: DefaultApiRequest;
  let userAttributes: KasAuthEndpointResponse;
  try {
    const _res = EventApiContext.commonEventInputValidation(event, objectValidator);
    body_validated = _res.body_validated;
    userAttributes = _res.userAttributes;
  } catch (error) {
    if (error instanceof ApiHandlerError) {
      return sendFail(
        { message: error.message, status: error.statusCode, reqHeaders: event.headers },
        EventApiContext.logger,
      );
    } else {
      EventApiContext.logger.log(LogLevel.ERROR, 'Unexepected Error during validation', { data: error });
      throw error;
    }
  }

  const ncosInfo: NcoInfo[] = body_validated.nco_ids_with_modified_at.map((nco) => ({
    pk_new_car_order_id: nco.pk_new_car_order_id,
    modified_at: nco.modified_at,
    sub_transaction_id: uuidv4(),
  }));
  const transformedRequestPayload: CorePayload = {
    payload: {},
    ncos_info: ncosInfo,
    event_type: OneVmsEventKey.MOVE_TO_INVENTORY,
  };
  const dispatcherEvent: InboundEventDispatcherEvent = EventApiContext.buildDispatcherEvent(
    transformedRequestPayload,
    userAttributes,
  );
  return await EventApiContext.handleDispatcherEvent(dispatcherEvent, event.headers);
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGWHandlerWithInitLogger(EventApiContext.logger)(event, context, moveToInventoryFunc);
