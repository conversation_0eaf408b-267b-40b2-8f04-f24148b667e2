import { BatchGetCommand, BatchGetCommandOutput, QueryCommand, QueryCommandOutput } from '@aws-sdk/lib-dynamodb';

import { generateApiGatewayEvent, mockContext, setupMocks } from '../../utils/test-utils';

const mocks = setupMocks();
import { handler } from '.';
beforeEach(() => {
  mocks.ddbMock!.reset();
  mocks.smMock!.reset();
  jest.resetModules();
});

const orgRelFailFetchResult: QueryCommandOutput = {
  Items: [],
  $metadata: {},
};

const orgRelFetchResult: QueryCommandOutput = {
  Items: [
    {
      dealer_number: '1234',
      importer_number: 'importer1',
      is_deactivated: false,
      is_relevant_for_order_create: true,
    },
  ],
  $metadata: {},
};

const mtvResult: BatchGetCommandOutput = {
  Responses: {
    [process.env.TABLE_NAME_MODEL_TYPE_VISIBILITY ?? '']: [
      {
        importer_number_role: 'importer1_DLR',
        model_type_my4_cnr: '98231_2024_C02',
        importer_number: 'importer1',
        cnr: 'C02',
        model_type: '98231',
        my4: 2024,
        role: 'string',
        valid_from: '2022-01-01',
      },
    ],
  },
  $metadata: {},
};
describe('Fetch Purchase Intentions Lambda - no access to dealer', () => {
  it('should return 403 when user has no access to any dealer', async () => {
    mocks.ddbMock!.on(QueryCommand).resolves(orgRelFailFetchResult);

    const res = await handler(generateApiGatewayEvent({ body: JSON.stringify({}) }), mockContext, () => {});

    expect(res?.statusCode).toBe(403);
  });
});

describe('Fetch Purchase Intentions Lambda - invalid JSON body', () => {
  it('should return 400 when request body is invalid JSON', async () => {
    const res = await handler(generateApiGatewayEvent({ body: 'not-json' }), mockContext, () => {});

    expect(res?.statusCode).toBe(400);
    expect(res?.body).toContain('Invalid Event Body');
  });
});

describe('Fetch Purchase Intentions Lambda - user has no access (explicit table)', () => {
  it('return user has no access', async () => {
    mocks
      .ddbMock!.on(QueryCommand, {
        TableName: process.env.TABLE_NAME_ORG_RELS ?? '',
      })
      .resolves(orgRelFailFetchResult);

    const res = await handler(generateApiGatewayEvent({ body: JSON.stringify({}) }), mockContext, () => {});
    console.log('res', res);

    expect(res?.statusCode).toBe(403);
  });
});

describe('Fetch Purchase Intentions Lambda - DB fails (no mock)', () => {
  it('Should fail with 500 because DB is not connected', async () => {
    mocks.ddbMock!.on(QueryCommand).resolves(orgRelFetchResult);
    mocks.ddbMock!.on(BatchGetCommand).resolves(mtvResult);

    const res = await handler(
      generateApiGatewayEvent({
        body: JSON.stringify({
          filterModel: {},
          startRow: 0,
          endRow: 10,
        }),
      }),
      mockContext,
      () => {},
    );

    console.log('res', res);
    expect(res?.statusCode).toBe(500);
  });
});
