=====================================================================
Project Name: cora - OSO-2562
=====================================================================



Dispositions générales sur les logiciels libres et offre écrite

====================================================================

Les termes et les conditions des licences énumérées ci-dessous constituent
la base contractuelle de votre utilisation des composants logiciels
correspondants et remplacent tout autre accord contractuel concernant ce
produit, cette solution ou ce service Porsche (« produit »).



Les modifications du logiciel propriétaire dans votre produit pour votre
propre usage et l ingénierie inverse pour le débogage de ces modifications
sont autorisées par la présente dans la mesure où ces composants logiciels
sont liés à des bibliothèques de programmes sous  la Licence Générale
Publique Limitée GNU (GNU Lesser General Public License, LGPL). Néanmoins,
vous ne devez pas divulguer à des tiers les connaissances acquises lors de
lingénierie inverse ou du débogage, ni les informations obtenues lors de
l’ingénierie inverse, ni le logiciel modifié lui-même. Veuillez prendre en
considération que toute modification se fait à vos risques et périls et
qu elle entraîne une exclusion de garantie pour les défauts résultant de la
modification. En outre, le produit peut ne pas être utilisable pour l usage
auquel il est destiné. Cette disposition prévaut sur toute autre
disposition contractuelle entre vous et Porsche ou votre concessionnaire
Porsche.



Pour plus d’informations sur les composants logiciels énumérés ci-dessous
ainsi que les conditions de licence correspondantes et - dans la mesure où
la licence lexige - les codes sources, veuillez contacter Porsche à
ladresse www.porsche.com/softwareinfo.



Toutefois, certaines licences exigent la fourniture de copies physiques du
code source et/ou du code objet. Dans un tel cas, vous et tout tiers pouvez
obtenir une copie des codes source et/ou objet conformément aux exigences
de la licence de logiciel libre applicable en nous contactant à ladresse
www.porsche.com/softwareinfo. En outre, veuillez nous contacter à lURL
précitée dans le cas où vous auriez besoin de toute autre assistance
concernant lexercice des droits garantis accordés par une licence de
logiciel libre. Une redevance nominale (cest-à-dire le coût de lexécution
physique de la distribution) pourrait être facturée pour ces services.



À la demande des auteurs et des détenteurs de droits des logiciels libres
utilisés dans ce produit, nous vous informons de ce qui suit :



« LES LOGICIELS LIBRES DE CE PRODUIT SONT DISTRIBUÉS DANS LESPOIR QUILS
SERONT UTILES, MAIS SANS AUCUNE GARANTIE, MÊME PAS LA GARANTIE IMPLICITE DE
QUALITÉ MARCHANDE OU DADÉQUATION À UN USAGE PARTICULIER. Pour plus de
détails, veuillez consulter les licences applicables. »

Note : votre copie de ce produit peut ne pas contenir de code couvert par
une ou plusieurs des licences énumérées ici, selon le produit et la version
exacts que vous choisissez.

====================================================================

General Open Source Provisions and written offer

====================================================================

The license terms listed below form the contractual basis for your use of
the corresponding software components and supersede any other contractual
agreements concerning this Porsche product, solution or service
(“product”).



Modifications of the proprietary software within your product for your own
use and reverse engineering for debugging such modifications are herewith
permitted to the extent such software components are linked to program
libraries under the GNU Lesser General Public License (LGPL). However, you
must not disclose the knowledge acquired during reverse engineering or
debugging, nor the information obtained from the reengineering, nor the
modified software itself to any third party. Please note that any
modification is at your own risk and will void any warranty for defects
resulting from the modification. In addition, the product may not be usable
for its intended purpose. This provision takes precedence over any other
contractual provision between you and Porsche or your Porsche Dealer.



For further information regarding the software components listed below and
the corresponding license terms and – where required by license – source
codes, please contact Porsche at www.porsche.com/softwareinfo.



Some licenses, however, require the provision of physical copies of source
and/or object code. In this case, you and any third party may obtain a copy
of the source and/or object codes according to the requirements of the
applicable Open Source License by contacting us at
www.porsche.com/softwareinfo. Furthermore, please contact us at the
foregoing URL in case you need assistance regarding the exercise of rights
guaranteed by an Open Source License. A nominal fee (i.e., the cost of
physically performing the distribution) might be charged for these
services.


On request of the authors and right holders of the Open Source Software
used in this product we inform you about the following:



“THE OPEN SOURCE SOFTWARE IN THIS PRODUCT IS DISTRIBUTED IN THE HOPE THAT
IT WILL BE USEFUL, BUT WITHOUT ANY WARRANTY, WITHOUT EVEN THE IMPLIED
WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE. See the
applicable licenses for more details.”



Note: your copy of this product may not contain code covered by one or more
of the licenses listed here, depending on the exact product and version you
choose.

---------------------------------------------------------------------

Acknowledgements:

---------------------------------------------------------------------

This software or document includes material copied from or derived from Intersection Observer, https://w3c.github.io/IntersectionObserver/. 


*********************************************************************
@babel/types
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 68985

*********************************************************************
@emotion/memoize
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 68989

*********************************************************************
@types/hoist-non-react-statics
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 68987

*********************************************************************
ansi-styles
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 56656

*********************************************************************
babel-plugin-macros
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2020 Kent C. Dodds

MIT
Id: 56688

*********************************************************************
base64-js
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 Jameson Little

MIT
Id: 56698

*********************************************************************
buffer
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Feross Aboukhadijeh (http://feross.org), and other contributors, Copyright (c) Feross Aboukhadijeh, and other contributors

MIT
Id: 56714

*********************************************************************
callsites
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Sindre Sorhus (https://sindresorhus.com), Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 56719

*********************************************************************
chalk
*********************************************************************
Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 56727

*********************************************************************
classnames
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2018 Dave Keen <http://www.keendevelopment.ch> Adi Dahiya <https://github.com/adidahiya> Jason Killian <https://github.com/JKillian>, Copyright (c) 2018 Jed Watson

MIT
Id: 56736

*********************************************************************
color-convert
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2011-2016 Heather Arthur <<EMAIL>>, Copyright (c) 2011-2016 Heather Arthur and Josh Junon, Copyright (c) 2011-2016, Heather Arthur and Josh Junon

MIT
Id: 56743

*********************************************************************
color-name
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 Dmitry Ivanov

MIT
Id: 56745

*********************************************************************
convert-source-map
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright 2013 Thorsten Lorenz

MIT
Id: 56764

*********************************************************************
cosmiconfig
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 David Clark

MIT
Id: 56772

*********************************************************************
csstype
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2017-2018 Fredrik Nicol

MIT
Id: 56799

*********************************************************************
date-fns
*********************************************************************
(c) Sasha Koss (https://kossnocorp.mit-license.org/), Copyright (c) 2021 Sasha Koss and Lesha Koss https://kossnocorp.mit-license.org

MIT
Id: 56802

*********************************************************************
dom-helpers
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 Jason Quense, Copyright 2013-2014, Facebook, Inc., Copyright 2013-2015 Facebook, Inc., Copyright 2014-2015, Facebook, Inc.

MIT
Id: 56833

*********************************************************************
error-ex
*********************************************************************
Copyright (c) 2015 JD Ballard

MIT
Id: 56857

*********************************************************************
escape-string-regexp
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Sindre Sorhus (http://sindresorhus.com), Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 56868

*********************************************************************
escape-string-regexp
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Sindre Sorhus <<EMAIL>> (https://sindresorhus.com)

MIT
Id: 56870

*********************************************************************
find-root
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) 2017 jsdnxx, Copyright (c) 2017 jsdnxx

MIT
Id: 56922

*********************************************************************
function-bind
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2013 Raynos

MIT
Id: 56943

*********************************************************************
has-flag
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Sindre Sorhus (https://sindresorhus.com), Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 56973

*********************************************************************
has
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2013 Thiago de Arruda

MIT
Id: 56979

*********************************************************************
hoist-non-react-statics
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 Yahoo! Inc., Copyright (c) 2015, Yahoo! Inc., Copyright 2015 Yahoo! Inc., Copyright 2015, Yahoo! Inc.Yahoo! Inc.

BSD-3-Clause
Id: 56981

*********************************************************************
html-parse-stringify
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°



MIT
Id: 56992

*********************************************************************
i18next-browser-languagedetector
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2023 i18next

MIT
Id: 57004

*********************************************************************
ieee754
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2008 Fair Oaks Labs, Inc., Copyright (c) 2008, Fair Oaks Labs, Inc., Copyright 2008 Fair Oaks Labs, Inc.

BSD-3-Clause
Id: 57011

*********************************************************************
immer
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2017 Michel Weststrate

MIT
Id: 57013

*********************************************************************
import-fresh
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Sindre Sorhus <<EMAIL>> (https://sindresorhus.com)

MIT
Id: 57014

*********************************************************************
is-arrayish
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 JD Ballard

MIT
Id: 57026

*********************************************************************
is-core-module
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 Dave Justice

MIT
Id: 57031

*********************************************************************
js-tokens
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014, 2015, 2016, 2017, 2018 Simon Lydell, Copyright (c) 2014-2018 Simon Lydell, Copyright 2014 Simon Lydell, Copyright 2014, 2015, 2016, 2017, 2018 Simon Lydell, Copyright 2014-2018 Simon Lydell, Copyright 2015, 2017 Simon Lydell

MIT
Id: 57109

*********************************************************************
json-parse-even-better-errors
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright 2017 Kat Marchan, Copyright npm, Inc.

MIT
Id: 57116

*********************************************************************
lines-and-columns
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 Brian Donovan

MIT
Id: 57136

*********************************************************************
loose-envify
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 Andres Suarez <<EMAIL>>

MIT
Id: 57149

*********************************************************************
memoize-one
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2019 Alexander Reardon

MIT
Id: 57160

*********************************************************************
object-assign
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Sindre Sorhus, (c) Sindre Sorhus (https://sindresorhus.com), Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 57203

*********************************************************************
parent-module
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Sindre Sorhus (https://sindresorhus.com), Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 57229

*********************************************************************
parse-json
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Sindre Sorhus <<EMAIL>> (https://sindresorhus.com)

MIT
Id: 57230

*********************************************************************
path-parse
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Javier Blanco (http://jbgutierrez.info), Copyright (c) 2015 Javier Blanco

MIT
Id: 57238

*********************************************************************
path-type
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Sindre Sorhus (https://sindresorhus.com), Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 57240

*********************************************************************
picocolors
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2021 Alexey Raspopov, Kostiantyn Denysov, Anton Verinov

ISC
Id: 57243

*********************************************************************
prop-types
*********************************************************************
Copyright (c) 2013 present, Facebook, Inc., Copyright (c) 2013-present, Facebook, Inc.

MIT
Id: 57333

*********************************************************************
react-datepicker
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-2023 HackerOne Inc and individual contributors, Copyright (c) 2014-2023 HackerOne Inc. and individual contributors

MIT
Id: 57347

*********************************************************************
react-dom
*********************************************************************
Copyright (c) 2012-2013 TJ Holowaychuk, Copyright (c) 2015 Andreas Lubbe, Copyright (c) 2015 Tiancheng Timothy Gu, Copyright (c) 2015 Tiancheng \"Timothy\" Gu, Copyright (c) Facebook, Inc. and its affiliates

MIT
Id: 57349

*********************************************************************
react-fast-compare
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2017 Evgeny Poberezkin, Copyright (c) 2018 Formidable Labs

MIT
Id: 57351

*********************************************************************
react-i18next
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2023 i18next

MIT
Id: 57352

*********************************************************************
react-is
*********************************************************************
Copyright (c) Facebook, Inc. and its affiliates

MIT
Id: 57353

*********************************************************************
react-is
*********************************************************************
Copyright (c) Facebook, Inc. and its affiliates

MIT
Id: 57355

*********************************************************************
react-onclickoutside
*********************************************************************
Copyright 2015-2022 Mike Pomax Kamermans

MIT
Id: 57356

*********************************************************************
react-popper
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2018 React Popper

MIT
Id: 57357

*********************************************************************
react-redux
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 present Dan Abramov, Copyright (c) 2015 present Dan Abramov and the Redux, Copyright (c) 2015-present Dan Abramov, Copyright (c) 2015-present Dan Abramov and the Redux, Copyright (c) 2017 present, Facebook, Inc., Copyright (c) 2017-present, Facebook, Inc., Copyright (c) Facebook, Inc. and its affiliates

MIT
Id: 57358

*********************************************************************
react-router-dom
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2019 React Training LLC, Copyright (c) 2020-2021 Remix Software Inc., Copyright (c) 2022-2023 Shopify Inc., Copyright (c) React Training LLC 2015-2019, Copyright (c) Remix Software Inc. 2020-2021, Copyright (c) Shopify Inc. 2022-2023

MIT
Id: 57360

*********************************************************************
react-router
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2019 React Training LLC, Copyright (c) 2020-2021 Remix Software Inc., Copyright (c) 2022-2023 Shopify Inc., Copyright (c) React Training LLC 2015-2019, Copyright (c) Remix Software Inc. 2020-2021, Copyright (c) Shopify Inc. 2022-2023

MIT
Id: 57361

*********************************************************************
react-select
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2022 Jed Watson, Copyright (c) Jed Watson 2022

MIT
Id: 57363

*********************************************************************
react-transition-group
*********************************************************************
Copyright (c) 2018 React Community Forked from React (https://github.com/facebook/react), Copyright (c) 2018, React Community Forked from React (https://github.com/facebook/react),All rights reserved.
Copyright 2013 present, Facebook, Inc., Copyright 2013-present, Facebook, Inc.

BSD-3-Clause
Id: 57364

*********************************************************************
react
*********************************************************************
Copyright (c) Facebook, Inc. and its affiliates

MIT
Id: 57365

*********************************************************************
redux-thunk
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 present Dan Abramov, Copyright (c) 2015-present Dan Abramov

MIT
Id: 57371

*********************************************************************
redux
*********************************************************************
Copyright (c) 2015 present Dan Abramov, Copyright (c) 2015 present Dan Abramov and the Redux, Copyright (c) 2015-present Dan Abramov, Copyright (c) 2015-present Dan Abramov and the Redux, Copyright (c) 2017 present, Facebook, Inc., Copyright (c) 2017-present, Facebook, Inc., copyright a href https://fontawesome.com/license/free

MIT
Id: 57372

*********************************************************************
regenerator-runtime
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present, Facebook, Inc., Copyright (c) 2014-present, Facebook, Inc.

MIT
Id: 57376

*********************************************************************
reselect
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2018 Reselect, Copyright (c) 2015-2018 Reselect Contributors (see Authors)

MIT
Id: 57387

*********************************************************************
resolve-from
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Sindre Sorhus (https://sindresorhus.com), Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 57389

*********************************************************************
resolve
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2012 James Halliday

MIT
Id: 57393

*********************************************************************
scheduler
*********************************************************************
Copyright (c) Facebook, Inc. and its affiliates

MIT
Id: 57413

*********************************************************************
source-map
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright 2009-2011 Mozilla Foundation and contributors
Copyright 2011 Mozilla Foundation and contributors
Copyright 2014 Mozilla Foundation and contributors
Copyright (c) 2009-2011, Mozilla Foundation and contributors. All rights reserved.


BSD-3-Clause
Id: 57444
---------------------------------------------------------------------
Copyright 2011 The Closure Compiler Authors

BSD-3-Clause-Variant
Id: 58383

*********************************************************************
stylis
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2016 present Sultan Tarimo, Copyright (c) 2016-present Sultan Tarimo

MIT
Id: 57478

*********************************************************************
supports-color
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT
Id: 57480

*********************************************************************
supports-preserve-symlinks-flag
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2022 Inspect JS

MIT
Id: 57484

*********************************************************************
to-fast-properties
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Petka Antonov, John-David Dalton, Sindre Sorhus, Copyright (c) 2014 Petka Antonov 2015 Sindre Sorhus, Copyright (c) 2014-2015 Petka Antonov Sindre Sorhus

MIT
Id: 57507

*********************************************************************
use-isomorphic-layout-effect
*********************************************************************
Copyright (c) Mateusz Burzynski

MIT
Id: 57546

*********************************************************************
use-sync-external-store
*********************************************************************
Copyright (c) Facebook, Inc. and its affiliates

MIT
Id: 57547

*********************************************************************
void-elements
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 hemanth

MIT
Id: 57556

*********************************************************************
warning
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2013-2014 present, Facebook, Inc., Copyright (c) 2013-present, Facebook, Inc., Copyright (c) 2014-present, Facebook, Inc.

MIT
Id: 57560

*********************************************************************
web-vitals
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright 2019 Google Inc., Copyright 2020 Google LLC

Apache-2.0
Id: 57563

*********************************************************************
code-frame
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 57622

*********************************************************************
helper-module-imports
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-2016 Sebastian McKenzie <<EMAIL>>

MIT
Id: 57638

*********************************************************************
helper-string-parser
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 57647

*********************************************************************
helper-validator-identifier
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 57648

*********************************************************************
highlight
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 57652

*********************************************************************
runtime
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 57750

*********************************************************************
babel-plugin
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Emotion team and other contributors

MIT
Id: 57777

*********************************************************************
cache
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57778

*********************************************************************
hash
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57779

*********************************************************************
react
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Emotion team and other contributors

MIT
Id: 57781

*********************************************************************
serialize
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57782

*********************************************************************
sheet
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57783

*********************************************************************
unitless
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57784

*********************************************************************
use-insertion-effect-with-fallbacks
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57785

*********************************************************************
utils
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57786

*********************************************************************
weak-memoize
*********************************************************************
Copyright (c) Emotion team and other contributors

MIT
Id: 57787

*********************************************************************
core
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2021 Floating UI contributors

MIT
Id: 57792

*********************************************************************
dom
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2021 Floating UI contributors

MIT
Id: 57793

*********************************************************************
core
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2016 Philipp Thurwachter & Pattrick Huper, Copyright (c) 2016, Philipp Thurwachter & Pattrick Huper, copyright (c) 2007 present, Stephen Colebourne & Michael Nascimento Santos, copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos, copyright (c) 2015 present, Philipp Thurwachter & Pattrick Huper & Michal Sobkiewicz, copyright (c) 2015-2016 Philipp Thurwachter, Pattrick Huper & js-joda contributors, copyright (c) 2015-2016 present, Philipp Thurwachter & Pattrick Huper & js-joda contributors, copyright (c) 2015-2016, Philipp Thurwachter, Pattrick Huper & js-joda contributors, copyright (c) 2015-present, Philipp Thurwachter & Pattrick Huper & Michal Sobkiewicz, copyright (c) 2015-present, Philipp Thurwachter, Pattrick Huper & js-joda contributors, copyright (c) 2016 Philipp Thurwachter & Pattrick Huper, copyright (c) 2016 present, Philipp Thuerwaechter & Pattrick Hueper, copyright (c) 2016 present, Philipp Thurwachter, Pattrick Huper, copyright (c) 2016, 2022 Philipp Thurwachter & Pattrick Huper & Michal Sobkiewicz, copyright (c) 2016, Philipp Thuerwaechter & Pattrick Hueper, copyright (c) 2016, Philipp Thurwachter & Pattrick Huper, copyright (c) 2016, Philipp Thurwachter & Pattrick Huper & Michal Sobkiewicz, copyright (c) 2016, Philipp Thurwachter, Pattrick Huper, copyright (c) 2016-2018, 2020 Philipp Thuerwaechter & Pattrick Hueper, copyright (c) 2016-present, Philipp Thuerwaechter & Pattrick Hueper, copyright (c) 2016-present, Philipp Thurwachter & Pattrick Huper & js-joda contributors, copyright (c) 2016-present, Philipp Thurwachter, Pattrick Huper, copyright (c) 2017, Philipp Thuerwaechter & Pattrick Hueper, copyright (c) 2018, Philipp Thuerwaechter & Pattrick Hueper, copyright (c) 2020, Philipp Thuerwaechter & Pattrick Hueper, copyright (c) 2022, Philipp Thurwachter & Pattrick Huper & Michal Sobkiewicz

BSD-3-Clause
Id: 57822
---------------------------------------------------------------------


MIT
Id: 57823

*********************************************************************
core
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2019 Federico Zivolo

MIT
Id: 57832

*********************************************************************
components-js
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Microsoft Corporation

0BSD
Id: 57833
---------------------------------------------------------------------
Copyright (c) 2022 Dr. Ing. h.c. F. Porsche AG, Copyright (c) Microsoft Corporation

Apache-2.0
Id: 57834
---------------------------------------------------------------------
Copyright (c) Facebook, Inc. and its affiliates, Copyright 2022 Naotoshi Fujita, copyright (c) 2012 Scott Jehl, copyright (c) 2012 Scott Jehl, Paul Irish, Nicholas Zakas, David Knight, copyright Oleg Isonen

MIT
Id: 57835
---------------------------------------------------------------------
Copyright 2016 Google Inc.

W3C-20150513
Id: 57837

*********************************************************************
components-react
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Microsoft Corporation

0BSD
Id: 57838
---------------------------------------------------------------------
Copyright (c) 2022 Dr. Ing. h.c. F. Porsche AG

Apache-2.0
Id: 57839
---------------------------------------------------------------------
copyright Oleg Isonen (Slobodskoi) / Isonen 2014-present 
Copyright (c) 2014-present Oleg Isonen (Slobodskoi) & contributors 

MIT
Id: 57840

*********************************************************************
toolkit
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2018 Mark Erikson, Copyright (c) 2021 Lenz

MIT
Id: 57842

*********************************************************************
router
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2019 React Training LLC, Copyright (c) 2020-2021 Remix Software Inc., Copyright (c) 2022-2023 Shopify Inc., Copyright (c) React Training LLC 2015-2019, Copyright (c) Remix Software Inc. 2020-2021, Copyright (c) Shopify Inc. 2022-2023

MIT
Id: 57843

*********************************************************************
parse-json
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 57895

*********************************************************************
prop-types
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 57897

*********************************************************************
react-transition-group
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 57901

*********************************************************************
react
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 57902

*********************************************************************
scheduler
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 57905

*********************************************************************
use-sync-external-store
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 57913

*********************************************************************
ag-grid-community
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) Microsoft Corporation

0BSD
Id: 56514
---------------------------------------------------------------------
Copyright (c) 2015-2019 AG GRID LTD

MIT
Id: 56515

*********************************************************************
ag-grid-react
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2019 AG GRID LTD

MIT
Id: 56516

*********************************************************************
clsx
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

(c) Luke Edwards (https://lukeed.com), Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)

MIT
Id: 56523

*********************************************************************
date-fns
*********************************************************************
(c) Sasha Koss (https://kossnocorp.mit-license.org/), Copyright (c) 2021 Sasha Koss and Lesha Koss https://kossnocorp.mit-license.org

MIT
Id: 56529

*********************************************************************
i18next
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2024 i18next

MIT
Id: 56541

*********************************************************************
immer
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2017 Michel Weststrate, Copyright (c) 2022 Michel

MIT
Id: 56543

*********************************************************************
react-datepicker
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014-2023 HackerOne Inc. and individual contributors, Copyright (c) 2014-2024 HackerOne Inc and individual contributors

MIT
Id: 56559

*********************************************************************
react-dom
*********************************************************************
Copyright (c) Meta Platforms, Inc. and affiliates, Copyright (c) Meta Platforms, Inc. and its affiliates

MIT
Id: 56560

*********************************************************************
react-i18next
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2024 i18next

MIT
Id: 56561

*********************************************************************
react-redux
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright 2015 Yahoo! Inc., Copyright 2015, Yahoo! Inc.

BSD-3-Clause
Id: 56564
---------------------------------------------------------------------
Copyright (c) 2015 present Dan Abramov, Copyright (c) 2015-present Dan Abramov

MIT
Id: 56565

*********************************************************************
react-router-dom
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2019 React Training LLC, Copyright (c) 2020-2021 Remix Software Inc., Copyright (c) 2022-2023 Shopify Inc., Copyright (c) React Training LLC 2015-2019, Copyright (c) Remix Software Inc., Copyright (c) Remix Software Inc. 2020-2021, Copyright (c) Shopify Inc. 2022-2023

MIT
Id: 56566

*********************************************************************
react-router
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2019 React Training LLC, Copyright (c) 2020-2021 Remix Software Inc., Copyright (c) 2022-2023 Shopify Inc., Copyright (c) React Training LLC 2015-2019, Copyright (c) Remix Software Inc., Copyright (c) Remix Software Inc. 2020-2021, Copyright (c) Shopify Inc. 2022-2023

MIT
Id: 56567

*********************************************************************
react-select
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2022 Jed Watson, Copyright (c) Jed Watson 2022

MIT
Id: 56568

*********************************************************************
react
*********************************************************************
Copyright (c) Meta Platforms, Inc. and affiliates, Copyright (c) Meta Platforms, Inc. and its affiliates

MIT
Id: 56570

*********************************************************************
redux-thunk
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 present Dan Abramov, Copyright (c) 2015-present Dan Abramov

MIT
Id: 56571

*********************************************************************
redux
*********************************************************************
Copyright (c) 2015 present Dan Abramov, 

Copyright (c) 2015-present Dan Abramov

MIT
Id: 56576

*********************************************************************
reselect
*********************************************************************
Copyright (c) 2015-2018 Reselect

MIT
Id: 56578

*********************************************************************
scheduler
*********************************************************************
Copyright (c) Meta Platforms, Inc. and affiliates

MIT
Id: 56581

*********************************************************************
tabbable
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015 David Clark

MIT
Id: 56586

*********************************************************************
use-sync-external-store
*********************************************************************
Copyright (c) Meta Platforms, Inc. and affiliates

MIT
Id: 56589

*********************************************************************
web-vitals
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright 2020 Google LLC, Copyright 2020, 2022 Google LLC, Copyright 2022 Google LLC

Apache-2.0
Id: 56591

*********************************************************************
code-frame
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 56593

*********************************************************************
helper-module-imports
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 56594

*********************************************************************
helper-string-parser
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 56595

*********************************************************************
helper-validator-identifier
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 56596

*********************************************************************
highlight
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 56597

*********************************************************************
runtime
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 56598

*********************************************************************
types
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2014 present Sebastian McKenzie and other contributors, Copyright (c) 2014-present Sebastian McKenzie and other contributors

MIT
Id: 56599

*********************************************************************
react-dom
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2021 present Floating UI contributors, Copyright (c) 2021-present Floating UI contributors

MIT
Id: 56613

*********************************************************************
react
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2021 present Floating UI contributors, Copyright (c) 2021-present Floating UI contributors

MIT
Id: 56614

*********************************************************************
utils
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2021 present Floating UI contributors, Copyright (c) 2021-present Floating UI contributors

MIT
Id: 56615

*********************************************************************
core
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thuerwaechter & Pattrick Hueper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thuerwaechter & Pattrick Hueper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2015-present, Philipp Thurwachter, Pattrick Huper & js-joda contributors
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2015-present, Philipp Thurwachter, Pattrick Huper & js-joda contributors
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016-present, Philipp Thurwachter & Pattrick Huper & js-joda contributors
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016-present, Philipp Thurwachter & Pattrick Huper & js-joda contributors
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016-present, Philipp Thurwachter & Pattrick Huper & js-joda contributors
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016-present, Philipp Thurwachter & Pattrick Huper & js-joda contributors
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter, Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter, Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper
copyright (c) 2007-present, Stephen Colebourne & Michael Nascimento Santos
copyright (c) 2016, Philipp Thurwachter & Pattrick Huper


BSD-3-Clause
Id: 56617

*********************************************************************
toolkit
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2018 Mark Erikson, Copyright (c) 2021 Lenz

MIT
Id: 56629

*********************************************************************
router
*********************************************************************
Authors: <AUTHORS>

°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°°

Copyright (c) 2015-2019 React Training LLC, Copyright (c) 2020-2021 Remix Software Inc., Copyright (c) 2022-2023 Shopify Inc., Copyright (c) React Training LLC 2015-2019, Copyright (c) Remix Software Inc. 2020-2021, Copyright (c) Shopify Inc. 2022-2023

MIT
Id: 56630

*********************************************************************
react
*********************************************************************
Copyright (c) Microsoft Corporation

MIT
Id: 56634


---------------------------------------------------------------------

Appendix:

---------------------------------------------------------------------

=====================================================================

BSD-3-Clause

Ids: 56617

=====================================================================


Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the js-joda nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
=====================================================================

Apache-2.0

Ids: 56591, 57563, 57834, 57834

=====================================================================

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

=====================================================================

MIT

Ids: 56516, 56517, 56517, 56518, 56518, 56519, 56519, 56520, 56520, 56521, 56521, 56522, 56522, 56523, 56524, 56524, 56525, 56525, 56526, 56526, 56527, 56527, 56528, 56528, 56529, 56530, 56530, 56531, 56531, 56532, 56532, 56533, 56533, 56534, 56534, 56535, 56535, 56536, 56536, 56537, 56537, 56539, 56539, 56540, 56540, 56541, 56543, 56544, 56544, 56545, 56545, 56546, 56546, 56547, 56547, 56548, 56548, 56549, 56549, 56550, 56550, 56551, 56551, 56552, 56552, 56553, 56553, 56554, 56554, 56555, 56555, 56556, 56556, 56558, 56558, 56559, 56560, 56561, 56562, 56562, 56563, 56563, 56566, 56567, 56568, 56570, 56571, 56576, 56577, 56577, 56578, 56579, 56579, 56580, 56580, 56581, 56583, 56583, 56584, 56584, 56585, 56585, 56586, 56587, 56587, 56588, 56588, 56589, 56590, 56590, 56593, 56594, 56595, 56596, 56597, 56598, 56599, 56600, 56600, 56601, 56601, 56602, 56602, 56604, 56604, 56605, 56605, 56606, 56606, 56607, 56607, 56608, 56608, 56609, 56609, 56610, 56610, 56611, 56611, 56612, 56612, 56613, 56614, 56615, 56629, 56630, 56631, 56631, 56632, 56632, 56633, 56633, 56634, 56635, 56635, 56656, 56656, 56688, 56688, 56698, 56698, 56714, 56714, 56719, 56719, 56727, 56727, 56736, 56743, 56743, 56745, 56745, 56764, 56764, 56772, 56772, 56799, 56799, 56802, 56833, 56833, 56857, 56857, 56868, 56868, 56870, 56870, 56922, 56922, 56943, 56943, 56973, 56973, 56979, 56979, 56992, 56992, 57004, 57004, 57013, 57014, 57014, 57026, 57026, 57031, 57031, 57109, 57109, 57116, 57116, 57136, 57136, 57149, 57149, 57160, 57160, 57203, 57203, 57229, 57229, 57230, 57230, 57238, 57238, 57240, 57240, 57333, 57333, 57347, 57349, 57351, 57352, 57353, 57353, 57355, 57356, 57356, 57357, 57358, 57360, 57361, 57363, 57365, 57371, 57372, 57376, 57376, 57387, 57389, 57389, 57393, 57393, 57413, 57478, 57478, 57480, 57480, 57484, 57484, 57507, 57507, 57546, 57546, 57547, 57556, 57556, 57560, 57622, 57638, 57647, 57648, 57652, 57750, 57777, 57777, 57778, 57778, 57779, 57779, 57781, 57781, 57782, 57782, 57783, 57783, 57784, 57784, 57785, 57785, 57786, 57786, 57787, 57787, 57792, 57792, 57793, 57793, 57832, 57835, 57835, 57842, 57843, 57895, 57895, 57897, 57897, 57901, 57901, 57902, 57905, 57913, 57913

=====================================================================

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

=====================================================================

MIT

Ids: 68985, 68987, 68989

=====================================================================

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
=====================================================================

0BSD

Ids: 57833, 57833

=====================================================================

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

=====================================================================

ISC

Ids: 56557, 56557, 57243, 57243

=====================================================================

Permission to use, copy, modify, and/or distribute this software for any purpose
with or without fee is hereby granted, provided that the above copyright notice
and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS
OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF
THIS SOFTWARE.

=====================================================================

BSD-3-Clause

Ids: 56542, 56542, 56569, 56569, 57011, 57011, 57364, 57364

=====================================================================

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the copyright holder nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
=====================================================================

BSD-3-Clause

Ids: 56582, 56582, 57444, 57444

=====================================================================

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the Mozilla Foundation nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
=====================================================================

BSD-3-Clause

Ids: 56538, 56538, 56981, 56981

=====================================================================

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the Yahoo! Inc. nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
=====================================================================

BSD-3-Clause

Ids: 57822

=====================================================================

Referenced in: https://github.com/js-joda/js-joda/blob/%40js-joda/core%405.5.3/packages/core/LICENSE#L1

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the js-joda nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
=====================================================================

MIT

Ids: 57823

=====================================================================

Referenced in: https://github.com/js-joda/js-joda/blob/%40js-joda/core%405.5.3/packages/timezone/src/unpack.js#L6

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
=====================================================================

BSD-3-Clause-Variant

Ids: 58383, 58383, 58384, 58384

=====================================================================

Referenced in: https://github.com/mozilla/source-map/blob/0.5.7/lib/base64-vlq.js#L10

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the Google Inc. nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
=====================================================================

MIT

Ids: 56565

=====================================================================

Referenced in: https://github.com/reduxjs/react-redux/blob/v9.1.2/LICENSE.md?plain=1#L1

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
=====================================================================

BSD-3-Clause

Ids: 56564

=====================================================================

Referenced in: https://github.com/reduxjs/react-redux/blob/v9.1.2/src/utils/hoistStatics.ts#L7

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list
of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright notice, this
list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

Neither the name of the copyright holder nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
=====================================================================

MIT

Ids: 56622, 56622

=====================================================================

Referenced in: jsdom-polyfill/index.cjs of https://registry.npmjs.org/@porsche-design-system/components-js/-/components-js-3.14.0.tgz


Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
=====================================================================

0BSD

Ids: 56620, 56620

=====================================================================

Referenced in: jsdom-polyfill/index.cjs of https://registry.npmjs.org/@porsche-design-system/components-js/-/components-js-3.14.0.tgz

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
=====================================================================

W3C-20150513

Ids: 56624, 56624, 57837, 57837

=====================================================================

Referenced in: jsdom-polyfill/index.cjs of https://registry.npmjs.org/@porsche-design-system/components-js/-/components-js-3.14.0.tgz
 
This software or document includes material copied from or derived from Intersection Observer, https://w3c.github.io/IntersectionObserver/. 

W3C Software and Document Notice and License

Status: This license takes effect 13 May, 2015.

This work is being provided by the copyright holders under the following license.
License

By obtaining and/or copying this work, you (the licensee) agree that you have
read, understood, and will comply with the following terms and conditions.

Permission to copy, modify, and distribute this work, with or without
modification, for any purpose and without fee or royalty is hereby granted,
provided that you include the following on ALL copies of the work or portions
thereof, including modifications:

    The full text of this NOTICE in a location viewable to users of the
    redistributed or derivative work.

    Any pre-existing intellectual property disclaimers, notices, or terms and
    conditions. If none exist, the W3C Software and Document Short Notice should
    be included.

    Notice of any changes or modifications, through a copyright statement on the
    new code or document such as "This software or document includes material
    copied from or derived from [title and URI of the W3C document]. Copyright ©
    [YEAR] W3C® (MIT, ERCIM, Keio, Beihang)."

Disclaimers

THIS WORK IS PROVIDED "AS IS," AND COPYRIGHT HOLDERS MAKE NO REPRESENTATIONS OR
WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO, WARRANTIES OF
MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF THE
SOFTWARE OR DOCUMENT WILL NOT INFRINGE ANY THIRD PARTY PATENTS, COPYRIGHTS,
TRADEMARKS OR OTHER RIGHTS.

COPYRIGHT HOLDERS WILL NOT BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL OR
CONSEQUENTIAL DAMAGES ARISING OUT OF ANY USE OF THE SOFTWARE OR DOCUMENT.

The name and trademarks of copyright holders may NOT be used in advertising or
publicity pertaining to the work without specific, written prior permission.
Title to copyright in this work will at all times remain with copyright holders.
Notes
=====================================================================

Apache-2.0

Ids: 56626, 56626, 57839, 57839

=====================================================================

Referenced in: LICENSE of https://registry.npmjs.org/@porsche-design-system/components-react/-/components-react-3.14.0.tgz

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
=====================================================================

0BSD

Ids: 56514

=====================================================================

Referenced in: package/dist/ag-grid-community.js
https://registry.npmjs.org/ag-grid-community/-/ag-grid-community-31.3.1.tgz

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
=====================================================================

MIT

Ids: 56515

=====================================================================

Referenced in: package/LICENSE.txt
https://registry.npmjs.org/ag-grid-community/-/ag-grid-community-31.3.1.tgz


Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
=====================================================================

MIT

Ids: 56627, 56627, 57840, 57840

=====================================================================

Referenced in: package/ssr/cjs/components/dist/styles/esm/styles-entry.cjs of https://registry.npmjs.org/@porsche-design-system/components-react/-/components-react-3.14.0.tgz

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
=====================================================================

0BSD

Ids: 56625, 56625, 57838, 57838

=====================================================================

Referenced in: package/ssr/cjs/components/dist/utils/esm/utils-entry.cjs
https://registry.npmjs.org/@porsche-design-system/components-react/-/components-react-3.14.0.tgz

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
=====================================================================

Apache-2.0

Ids: 56621, 56621

=====================================================================

Referenced in: Referenced in: LICENSE and jsdom-polyfill/index.cjs of https://registry.npmjs.org/@porsche-design-system/components-js/-/components-js-3.14.0.tgz

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
