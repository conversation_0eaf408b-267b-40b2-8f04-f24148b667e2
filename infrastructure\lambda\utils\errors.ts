export interface GeneralErrorProps {
  /**
   * the explanation of this error, what it means or why it happened
   */
  message?: string;
  /**
   * an error that may have caused this error
   */
  causedBy?: unknown;
}

export class GeneralError extends Error {
  public readonly causedBy: unknown;
  public constructor(props?: GeneralErrorProps) {
    super(props?.message);
    this.causedBy = props?.causedBy;
  }
}
export interface ApiHandlerErrorProps {
  /**
   * the explanation of this error, what it means or why it happened
   */
  message: string;
  /**
   * an error that may have caused this error
   */
  causedBy?: unknown;
  /**
   * statusCode to be returned by the API
   */
  statusCode: number;
}

export class ApiHandlerError extends Error {
  public readonly causedBy: unknown;
  public readonly statusCode: number;
  public constructor(props: ApiHandlerErrorProps) {
    super(props.message);
    this.name = 'ApiHandlerError';
    this.causedBy = props.causedBy;
    this.statusCode = props.statusCode;
  }
}

// HTTP ERRORS
export class NoContentError extends GeneralError {}

export class RacingConditionError extends GeneralError {}
