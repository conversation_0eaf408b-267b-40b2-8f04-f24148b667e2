import { CoraNCOBaseApiResponse } from '../../../infrastructure/lib/types/new-car-order-types';
import { CoraNCPurchaseIntentionApiResponse } from '../../../infrastructure/lib/types/purchase-intention-types';
import { CoraFeatureFlags } from '../../../infrastructure/lib/types_cdk/cdk-types';

export interface Dealer {
  pk_importer_number: string;
  sk_dealer_number: string;
  display_name: string;
  standard_port_code?: string;
  alternative_port_codes?: string[];
  modified_by?: string;
  modified_at?: string;
}

export interface Importer {
  pk_importer_number: string;
  code: string;
  display_name: string;
  port_codes?: string[];
  modified_by?: string;
  modified_at?: string;
}

export interface CoraMdDealerImporterApiResponse {
  dealer: Dealer;
  importer: Importer;
}

export type Role = 'Dealer' | 'Importer';

export type ModalState = 'preview' | 'result';
export enum BuySellTransferSteps {
  SELECTION,
  SUMMARY,
  FINAL,
}

export interface AuthorizedDealer {
  dealer_number: string;
  display_name: string;
  role: Role;
  importer_number?: string;
  importer_display_name?: string;
  dealer_ppn_status: string | null;
}
export interface AuthorizedImporter {
  display_name: string;
  importer_number: string;
  role: Role;
}

export interface PurchaseIntentionInListTranslationItem extends CoraNCPurchaseIntentionApiResponse {
  model_text: string;
}

export interface CarOrderInList {
  dealer_number: string;
  business_partner_id: string;
  dealer_name?: string;
  pk_new_car_order_id: string;
  created_at: string;
  created_by: string;
  importer_code: string;
  importer_number: string;
  model_type: string;
  model_year: string;
  order_status_onevms_code: string;
  order_invoice_onevms_code: string;
  order_status_onevms_error_code: string;
  order_type: string;
  quota_month: string | null;
  receiving_port_code?: string;
  requested_dealer_delivery_date: string;
  shipping_code: string;
  cnr: string;
  modified_at?: string;
  modified_by?: string;
}

export interface OrderCancelReason {
  id: string;
  beschreibung: string;
}

interface Planning {
  blocking_reason_planning: string;
}
interface Ids {
  new_car_order_id: string;
}

interface StatusInfo {
  vehicle_status_pvms_code: string;
  order_status_pvms_timestamp: string;
}
interface OrderInfo {
  status_info: StatusInfo;
  planning: Planning;
}

interface Value {
  ids: Ids;
  order_info: OrderInfo;
}
export interface FailedCarOrderInList {
  object_id: string;
  key: string;
  numOfTries: number;
  value: Value;
  sperrgrund: string;
  kate: string;
  qs_sperren: string;
}
export interface CopyResultFailed {
  quota_month: string;
  quota_failed: string;
}
export type Stage = 'dev' | 'int' | 'prod' | 'local' | undefined;
export interface StageConfig {
  stage: Stage;
  ppnRolesWrite: string[];
  featureFlags: CoraFeatureFlags;
}

export interface KccProxyCacheConfigResponse {
  sessionId: string;
}

export type QuotaApiRequestParams = Pick<
  CoraNCOBaseApiResponse,
  'model_type' | 'model_year' | 'dealer_number' | 'importer_number'
>;

//old nco action stypes
export enum OrderActionType {
  COPY = 'copy',
  IMPORTER_TRANSFER = 'importer_transfer',
}
