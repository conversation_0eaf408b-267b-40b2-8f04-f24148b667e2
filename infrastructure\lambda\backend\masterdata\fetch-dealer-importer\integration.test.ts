/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';

const lambdaArn = buildLambdaArn('fetch-dealer-importer');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDealerTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImporterTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;

const importerOrgId = 'c0385ee0-1861-4629-a700-2151d6912c04';
const dealerGroupOrgId = '714dc33f-6f32-4b0c-856a-5ecf9c1e914c';
const dealerOrgId1 = 'b9cec2bb-eda1-4794-9f46-a1e0136149a9';
const dealerOrgId2 = '1b6c12f9-7e7d-40bc-b2b2-edc1d0c18d83';
const dealerOrgIdInactive = '233a2407-5e86-4033-892b-86060c95aca0';
const unrelatedImpOrgId = '33dc09ae-34ff-457f-9226-271fd2ee8a56';
const testPrefix = 'FDI';

const mdImporter = {
  pk_importer_number: `ItImp${testPrefix}`,
  code: 'IT',
  display_name: 'IntegrationTest - Importer',
  modified_at: '2023-10-04T14:16:30.079Z',
  modified_by: 'integration_test',
} as const;

const mdDealer = {
  pk_importer_number: mdImporter.pk_importer_number,
  sk_dealer_number: `ItDlr${testPrefix}1`,
  display_name: 'IntegrationTest - Dealer 1',
  modified_at: '2023-08-01T12:13:06.880Z',
  modified_by: 'integration_test',
} as const;

const orgRels: CoraOrgRelModel[] = [
  //importer relation to itself
  {
    pk_ppn_id: importerOrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: mdImporter.pk_importer_number,
    display_name: 'IntegrationTest - Importer',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to parent importer
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: importerOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to itself
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: dealerGroupOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: mdDealer.sk_dealer_number,
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId,
    dealer_number: mdDealer.sk_dealer_number,
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: mdDealer.sk_dealer_number,
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: `ItDlr${testPrefix}2`,
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId,
    dealer_number: `ItDlr${testPrefix}2`,
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: `ItDlr${testPrefix}2`,
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent dealer group
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: `ItDlr${testPrefix}Inactive`,
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent importer
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: importerOrgId,
    dealer_number: `ItDlr${testPrefix}Inactive`,
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to itself
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerOrgIdInactive,
    dealer_number: `ItDlr${testPrefix}Inactive`,
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: mdImporter.pk_importer_number,
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //unrelated importer relation to itself
  {
    pk_ppn_id: unrelatedImpOrgId,
    parent_ppn_id: unrelatedImpOrgId,
    dealer_number: 'ItUnrelatedImp',
    display_name: 'IntegrationTest - Unrelated Imp',
    importer_number: 'ItUnrelatedImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

beforeAll(async () => {
  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    { tableName: mdDealerTableName, objs: [mdDealer] },
    { tableName: mdImporterTableName, objs: [mdImporter] },
  ]);
});

afterAll(async () => {
  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: mdDealerTableName,
      pks: [{ pk_importer_number: mdDealer.pk_importer_number, sk_dealer_number: mdDealer.sk_dealer_number }],
    },
    {
      tableName: mdImporterTableName,
      pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
    },
  ]);
});

it('should return 401 if org is missing in auth context', async () => {
  const event = createApiGwEvent({ queryStringParameters: { dealer_number: mdDealer.sk_dealer_number } });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.dealer).toBeUndefined();
  expect(body.importer).toBeUndefined();
});

it('should return 400 if dealer_number query param is missing', async () => {
  const event = createApiGwEvent({ userOrgId: importerOrgId });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.dealer).toBeUndefined();
  expect(body.importer).toBeUndefined();
});

it('should return 403 for a dealer that is requesting a different dealer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId2,
    queryStringParameters: { dealer_number: mdDealer.sk_dealer_number },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.dealer).toBeUndefined();
  expect(body.importer).toBeUndefined();
});

it('should return 403 for an importer that is requesting a dealer it does not have access to', async () => {
  const event = createApiGwEvent({
    userOrgId: unrelatedImpOrgId,
    queryStringParameters: { dealer_number: mdDealer.sk_dealer_number },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.dealer).toBeUndefined();
  expect(body.importer).toBeUndefined();
});

it('should return 200 with the dealer and importer object for an authorized importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    queryStringParameters: { dealer_number: mdDealer.sk_dealer_number },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  expect(res.body).not.toContain('message');
  const body = JSON.parse(res.body);
  expect(body).not.toBeNull();
  expect(body.dealer).toEqual(mdDealer);
  expect(body.importer).toEqual(mdImporter);
});

it('should return 200 with the dealer and importer object for an authorized dealer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId1,
    queryStringParameters: { dealer_number: mdDealer.sk_dealer_number },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  expect(res.body).not.toContain('message');
  const body = JSON.parse(res.body);
  expect(body).not.toBeNull();
  expect(body.dealer).toEqual(mdDealer);
  expect(body.importer).toEqual(mdImporter);
});
