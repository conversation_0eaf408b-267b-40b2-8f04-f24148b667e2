export interface CoraMdShippingCode {
  pk_shipping_code: string;
  description: string;
  imp_is_blacklist: boolean;
  importers: string[];
  dlr_is_blacklist: boolean;
  dealers: string[];
  is_deactivated: boolean;
  created_at: string;
  created_by: string;
  modified_at: string;
  modified_by: string;
}

export interface CoraMdShippingCodeResponseItem extends CoraMdShippingCode {
  editable: boolean;
}

export interface CoraMdOrderType {
  pk_order_type: string;
  description: string;
  imp_is_blacklist: boolean;
  importers: string[];
  dlr_is_visible: boolean;
  is_deactivated: boolean;
  is_customer_related?: boolean;
  created_at: string;
  created_by: string;
  modified_at: string;
  modified_by: string;
}

export interface CoraMdOrderTypeResponseItem extends CoraMdOrderType {
  editable: boolean;
}

export interface CoraMdPortCode {
  pk_port_code: string;
  display_name: string;
  is_deactivated: boolean;
  created_at: string;
  created_by: string;
  modified_at: string;
  modified_by: string;
}

export interface CoraMdPortCodeResponseItem extends CoraMdPortCode {
  standard: boolean;
  editable: boolean;
}

export interface CoraMdImporter {
  pk_importer_number: string;
  code: string;
  display_name: string;
  port_codes?: string[];
  modified_by?: string;
  modified_at?: string;
  ppn_status?: string;
}

export interface CoraMdDealer {
  pk_importer_number: string;
  sk_dealer_number: string;
  display_name: string;
  standard_port_code?: string;
  alternative_port_codes?: string[];
  modified_by?: string;
  modified_at?: string;
}

export interface CoraMdOneVmsStatus {
  one_vms_status: string | null;
  one_vms_error_status: string | null;
  description_de: string;
  description_en: string;
  order_changeable: boolean;
}

export interface CoraMdOneVmsStatusModel {
  status_code: string;
  status_type: CoraMdOneVmsStatusType;
  status_description_EN: string;
  status_description_DE: string;
  is_deactivated: boolean;
}

export interface CoraMdMappingToOneVmsStatus {
  pvms_status: string;
  lock_reason: string | null;
  one_vms_status: string;
  one_vms_error_status: string | null;
}

export interface CoraMdMappingToOneVmsInvoiceStatus {
  pvms_invoice_status: string;
  one_vms_invoice_status: string;
  description_de: string;
  description_en: string;
}

export interface CoraMdStatusMapping {
  mappings_to_one_vms_status: CoraMdMappingToOneVmsStatus[];
  mappings_to_invoice_status: CoraMdMappingToOneVmsInvoiceStatus[];
}

export enum CoraMdOneVmsStatusType {
  Order = 'OrderStatus',
  Error = 'ErrorStatus',
  Invoice = 'InvoiceStatus',
}
