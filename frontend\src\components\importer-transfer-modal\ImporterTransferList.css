.importer-transfer-list-container {
  display: flex;
  flex-direction: column;
}

.importer-transfer-list-container .col-container {
  display: flex;
  flex-direction: row;
}

.importer-transfer-list-container .field-col {
  display: flex;
  flex-grow: 10;
  flex-direction: column;
}

.importer-transfer-list-container .accordion-btn {
  width: 100%;
  margin-top: 10px;
}

.importer-transfer-list-container .quota-row {
  display: grid;
  grid-template-columns: 75px 20px auto; /* Three columns: month, pipe, failed */
  gap: 4px; /* Space between columns */
  align-items: center; /* Aligns all items in the row vertically */
  font-family: monospace; /* Consistent character width */
}

.importer-transfer-list-container .quota-month,
.importer-transfer-list-container .quota-pipe,
.importer-transfer-list-container .quota-failed {
  text-align: left; /* Aligns text to the left */
}
