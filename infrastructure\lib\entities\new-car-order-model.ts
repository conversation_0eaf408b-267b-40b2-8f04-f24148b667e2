import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryC<PERSON>umn,
  OneToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
  ManyToOne,
  Relation,
  Index,
} from 'typeorm';
import { BaseModel } from './base-model';
import { SOURCE_OBJ_TYPES } from '../types/new-car-order-types';
import { NcoExportActionType } from '../../lambda/backend/export-nco/types';
import { OneVmsSourceSystemKey } from '../types/process-steering-types';

@Entity({ name: 'new_car_order' })
export class NewCarOrderModel extends BaseModel {
  @PrimaryColumn({ type: 'text', update: false })
  public pk_new_car_order_id: string;

  @OneToOne(() => NcoConfigurationModel, (config) => config.newCarOrder, {
    cascade: true,
  })
  public configuration: Relation<NcoConfigurationModel>;

  @Column({ type: 'jsonb', nullable: true })
  public configuration_expire: unknown;

  @Index()
  @Column({ type: 'text' })
  public importer_number: string;

  /**
   * @minLength 2
   */
  @Column({ type: 'text' })
  public importer_code: string;

  @Index()
  @Column({ type: 'text' })
  public dealer_number: string;

  @Column({ type: 'text', update: false })
  public model_type: string;

  @Column({ type: 'text', update: false })
  public model_year: string;

  /**
   * @pattern ^C\d\d$
   */
  @Column({ type: 'text' })
  public cnr: string;

  @Column({ type: 'text', nullable: true })
  public quota_month: string | null;

  @Column({ type: 'text' })
  public order_type: string;

  @Column({ type: 'text' })
  public shipping_code: string;

  @Column({ type: 'text', nullable: true })
  public receiving_port_code?: string | null;

  /**
   * @format date
   */
  @Column({ type: 'date', nullable: true })
  public requested_dealer_delivery_date?: string | null;

  /**
   * @description Id of the Object that was the Basis for this NCO
   */
  @Column({ type: 'text', nullable: true, update: false })
  public source_obj_id?: string | null;

  /**
   * @description Type of the Object that was the Basis for this NCO (e.g. purchase_intention, new_car_order etc.)
   */
  @Column({ type: 'text', nullable: true, update: false })
  public source_obj_type?: SOURCE_OBJ_TYPES | null;

  @Index()
  @Column({ type: 'text', nullable: false })
  public order_status_onevms_code: string;

  @Column({ type: 'text', nullable: false })
  public order_status_onevms_error_code: string;

  @Column({ type: 'text', nullable: false })
  public order_invoice_onevms_code: string;

  /**
   * @format date-time
   */
  @Column({ type: 'timestamp with time zone' })
  public order_status_onevms_timestamp_last_change: string;

  @Column({ type: 'text', nullable: true })
  public cancellation_reason?: string | null;

  @Column({ type: 'text', nullable: true })
  public business_partner_id?: string | null;

  @Column({ type: 'text', nullable: true })
  public deal_id?: string | null;

  @Column({ type: 'text' })
  public changed_by_system: OneVmsSourceSystemKey;
}

@Entity({ name: 'nco_configuration' })
export class NcoConfigurationModel extends BaseModel {
  @PrimaryGeneratedColumn()
  public pk_config_id?: number | null;

  @OneToOne(() => NewCarOrderModel, (nco) => nco.configuration, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  @JoinColumn({ name: 'fk_new_car_order_id' })
  public newCarOrder?: Relation<NewCarOrderModel>;

  @Index()
  @Column({ type: 'text', update: false })
  public fk_new_car_order_id?: string;

  @OneToMany(() => NcoConfigOrderedOptionsModel, (option) => option.configuration, { cascade: true })
  public ordered_options: Relation<NcoConfigOrderedOptionsModel[]>;

  @Column({ type: 'jsonb', nullable: true })
  public technical_options?: unknown;
}

//Date Format but allows empty string
//const dateFormatOrEmptyString = /^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$/;

@Entity({ name: 'nco_config_ordered_options' })
// @Tree('materialized-path')
//not needed as long as we have the validation in ts interfaces
/*@Check(`"validity_valid_until" ~ '${dateFormatOrEmptyString}'`)
@Check(`"validity_valid_from" ~ '${dateFormatOrEmptyString}'`)
@Check(`"validity_serial_to" ~ '${dateFormatOrEmptyString}'`)
@Check(`"validity_serial_from" ~ '${dateFormatOrEmptyString}'`)
@Check(`"validity_offer_period_start" ~ '${dateFormatOrEmptyString}'`)
@Check(`"validity_offer_period_end" ~ '${dateFormatOrEmptyString}'`)
@Check(`"validity_material_lead_time" ~ '${/^(\d+|)$/}'`)*/
export class NcoConfigOrderedOptionsModel extends BaseModel {
  @PrimaryColumn({ type: 'text' })
  public pk_option_id?: string;

  //reference to the configuration (null for content options inside options)
  @ManyToOne(() => NcoConfigurationModel, (config) => config.ordered_options, {
    nullable: true,
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  public configuration?: Relation<NcoConfigurationModel>;

  @OneToMany(() => NcoConfigOrderedOptionsModel, (child) => child.parent_option, { cascade: true })
  // @TreeChildren({ cascade: true })
  public content?: NcoConfigOrderedOptionsModel[];

  // @TreeParent({ onDelete: 'CASCADE' })
  @ManyToOne(() => NcoConfigOrderedOptionsModel, (parent) => parent.content, { onDelete: 'CASCADE' })
  public parent_option?: NcoConfigOrderedOptionsModel;

  /*@Column({ type: 'integer', update: false, nullable: true })
  public fk_parent_option?: number;*/

  @Column({ type: 'text' })
  public option_id: string;

  @Column({ type: 'text', nullable: true })
  public option_type?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_valid_until?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_valid_from?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_serial_to?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_serial_from?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_offer_period_start?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_offer_period_end?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_material_lead_time?: string | null;

  @Column({ type: 'text', nullable: true })
  public validity_added_to_order_timestamp?: string | null;

  @Column({ type: 'text', nullable: true })
  public referenced_package?: string | null;

  @Column({ type: 'text', nullable: true })
  public referenced_package_type?: string | null;

  @Column({ type: 'integer', nullable: true })
  public referenced_package_sort_order?: number | null;

  @Column({ type: 'integer', nullable: true })
  public package_content_sort_order?: number | null;

  @Column({ type: 'text', nullable: true })
  public option_subtype?: string | null;

  @Column({ type: 'text', nullable: true })
  public option_subtype_value?: string | null;
}

export type NewCarOrderModelWithOptionalActionType = NewCarOrderModel & { action_type?: NcoExportActionType };
export type NewCarOrderModelWithQuota = NewCarOrderModel & {
  /**
   * @pattern ^\d\d\d\d-\d\d$
   */
  quota_month: string;
};
