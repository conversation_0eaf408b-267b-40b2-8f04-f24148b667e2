import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { v4 as uuidv4 } from 'uuid';

type HandlerLogic = (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
) => Promise<APIGatewayProxyResult>;

export function createApiGWHandlerWithInitLogger(
  logger: KasLambdaLogger,
): (event: APIGatewayProxyEvent, context: Context, handlerLogic: HandlerLogic) => Promise<APIGatewayProxyResult> {
  return async (
    event: APIGatewayProxyEvent,
    context: Context,
    handlerLogic: HandlerLogic,
  ): Promise<APIGatewayProxyResult> => {
    logger.setRequestContext(context);
    // api-gateway converts the header to lower case
    // when testing via apigateway console headers can be undefined
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    const correlationId = event.headers?.['x-kas-request-id'] ?? uuidv4();
    logger.log(LogLevel.TRACE, 'correlationId', { data: correlationId });
    logger.setCorrelationId(correlationId);
    logger.setObjectId(undefined);
    return handlerLogic(event, context, logger);
  };
}

export function createApiGwHandler(
  serviceName: string,
  logLevel: LogLevel = LogLevel.TRACE,
): (event: APIGatewayProxyEvent, context: Context, handlerLogic: HandlerLogic) => Promise<APIGatewayProxyResult> {
  const logger = new KasLambdaLogger(serviceName, logLevel);
  return createApiGWHandlerWithInitLogger(logger);
}
