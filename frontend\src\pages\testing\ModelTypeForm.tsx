import { PSelectWrapper, PSpinner } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { FetchError } from '../errors/FetchErrors';
import { useGetModelTypesQuery } from '../../store/api/BossApi';
import { ModelTypeVisibilityModel } from '../../../../infrastructure/lib/entities/model-type-visibility-model';

interface ModelTypeFormProps {
  importer_number: string;
  selectedModelType?: ModelTypeVisibilityModel;
  handleSelect: (element: ModelTypeVisibilityModel) => void;
}
export const ModelTypeForm: React.FC<ModelTypeFormProps> = ({ importer_number, selectedModelType, handleSelect }) => {
  const area = 'model_type' as const;
  const { t } = useTranslation();

  const { data, error, isLoading } = useGetModelTypesQuery(importer_number);

  if (isLoading) {
    return <PSpinner />;
  }

  if (!data) {
    return <FetchError custom_error={error} error_area={area} />;
  }

  return (
    <PSelectWrapper style={{ flexBasis: 'calc(50% - 10px)' }} label={t(`${area}_prompt`)}>
      <select
        data-e2e="SelectModelType"
        required
        value={selectedModelType ? modelTypeToId(selectedModelType) : ''}
        onChange={(e) => {
          const selectedElement = data.find((m) => modelTypeMatchesId(m, e.target.value));
          if (selectedElement) {
            handleSelect(selectedElement);
          }
        }}
      >
        <option>{t(`${area}_selection`)}</option>
        {data.map((element) => (
          <option key={modelTypeToId(element)} value={modelTypeToId(element)}>
            {`${element.model_type} - ${element.my4} - ${element.cnr}`}
          </option>
        ))}
      </select>
    </PSelectWrapper>
  );
};

export function modelTypeToId(m: ModelTypeVisibilityModel): string | undefined {
  return `${m.importer_number}_${m.role}|${m.model_type}_${m.my4}_${m.cnr}`;
}
export function modelTypeMatchesId(m: ModelTypeVisibilityModel, id: string): boolean {
  const [a, b] = id.split('|');
  return `${m.importer_number}_${m.role}` === a && `${m.model_type}_${m.my4}_${m.cnr}` === b;
}
