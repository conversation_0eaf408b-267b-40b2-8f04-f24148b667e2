import i18next from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';
import { OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import { OrderActionType } from '../store/types';

const resources = {
  de: {
    translation: {
      language: 'Language: English',
      back: 'Zurück',
      continue: 'Weiter',
      view: 'Anzeigen',
      edit: 'Ändern',
      delete: 'Löschen',
      deactivate: 'Deaktivieren',
      reactivate: 'Reaktivieren',
      reset: 'Zurücksetzen',
      save: 'Speichern',
      filter: 'Suchen',
      changeable: 'Änderbar',
      non_changeable: 'Nicht änderbar',
      no_rows_to_show: 'Daten sind nicht bereit oder leer',
      session_timeout: 'Session Timeout. Die Session konnte nicht erneuert werden (Error: "{{errorMessage}}").',
      session_timer: 'Session Timer: {{time}}',
      delete_entity: '"{{name}}" deaktivieren',
      restore_entity: '"{{name}}" wiederherstellen',
      delete_entity_with_id: '"{{name}}" "{{id}}" deaktivieren?',
      restore_entity_with_id: '"{{name}}" "{{id}}" wiederherstellen?',
      create_entity: '{{name}} anlegen',
      update_entity: '{{name}} ändern',
      view_entity: '{{name}} Details',
      validation_error: 'Die eingegebenen Daten sind für diesen Auftrag nicht gültig',
      no_value_field_required: 'Kein Wert für Pflichtfeld',
      not_unique: 'Ein Objekt mit diesem Wert existiert bereits',
      arr_not_empty: 'Bitte wählen Sie mindestens einen Eintrag aus',
      validation_failed: 'Validierung fehlgeschlagen',
      confirm_delete: 'Löschen Bestätigen',
      confirm_delete_details: '"{{entity}}" löschen?',
      confirm_action: 'Bestätigen',
      cancel: 'Abbrechen',
      field: 'Feld',
      empty_field: '(leer)',
      per_page: '/Page', //max 5 letters...
      pagesize: 'Aufträge je Seite',
      no_entities: 'Keine Elemente gefunden...',
      no_purchase_intention_id_in_params: 'Fehlende Purchase Intention Id',
      no_new_car_order_id_in_params: 'Fehlende NewCarOrder Id',
      details: 'Details',
      loading: 'Lade...',
      logout: 'Abmelden',
      is_deactivated: 'Deaktiviert',
      is_deactivated_true: 'Inaktiv',
      is_deactivated_false: 'Aktiv',
      is_deactivated_undefined: 'Ungefiltert',
      created_at: 'Erstellt am',
      created_by: 'Erstellt durch',
      modified_at: 'Geändert am',
      modified_by: 'Geändert durch',
      changed_on_date: 'Letzte Änderung am',
      changed_by: 'Letzte Änderung durch',
      filter_for_more: '... weitere Einträge durch Filtern anzeigen (insg. {{length}}) ...',
      no_app_permissions: 'Sie haben leider keine Berechtigung für CORA',
      user_permission_label: 'Rechte:',
      user_permission_write: 'Lesen, Schreiben',
      user_permission_read: 'Lesen',
      user_permission_none: 'Keine',
      api_error: 'Fehler bei API',
      fetch_error: 'Fehler bei `fetch` - Daten konnten nicht geladen werden',
      serialization_error: 'Serialisierungsfehler',
      unknown_error: 'Unbekannter Fehler',
      error_area: 'Fehlerbereich',
      missing_parameters: 'Die eingegebenen Daten sind unvollständig',

      // New Car Order
      config_validity_excludes_selected_quota:
        'Optionen in der ausgewählten Konfiguration verhindern die ausgewählte Quote',
      existing_quota: 'Aktuelle Quote des Auftrags',
      create_order: 'Neuen Auftrag anlegen',
      change_order: 'Auftrag ändern',
      change_order_button: 'Auftrag ändern',
      order_edited: 'Auftrag erfolgreich geändert',
      order_created: 'Auftrag erfolgreich angelegt',
      convert_order: 'Purchase Intention konvertieren',
      convert_order_button: 'Purchase Intention konvertieren',
      order_id: 'Auftrag ID',
      fetch_quotas: 'Quoten abrufen',
      wrong_order_id:
        'Die Auftragsnummer muss aus acht oder neun Großbuchstaben oder Ziffern bestehen. Bitte überprüfen Sie sie erneut!',
      dealer_prompt: 'Händlerauswahl',
      dealer_selection_prompt: 'Bitte wählen Sie einen Händler aus der Liste aus',
      convert_purchase_intention: 'Auftragsbedarf konvertieren',
      convert_purchase_intention_success: 'Auftragsbedarf in Auftrag konvertiert',
      purchase_intention_loading_error: 'Fehler beim Lader der Purchase Intention(s)',
      purchase_intention_loading_error_description:
        'Auftragsbedarf {{purchaseIntentionId}} existiert nicht, oder Sie haben keine Rechte diesen Auftragsbedarf anzusehen',
      dealer_selection_error: 'Die gewählten Händler konnten nicht geladen werden',
      dealer_selection_error_description:
        'Händler {{dealer_number}} existiert nicht, oder Sie haben keine Rechte diesen Händler anzusehen ',
      submit_order: 'Auftrag anlegen',
      order_type_prompt: 'Auftragsart',
      order_type_selection: 'Bitte wählen Sie eine Auftragsart aus der Liste aus',
      model_type_prompt: 'Modelltyp',
      model_type_selection: 'Bitte wählen Sie einen Modelltypen aus der Liste aus',
      model_year_prompt: 'Modelljahr',
      model_year_selection: 'Bitte wählen Sie ein Modelljahr aus der Liste aus',
      quota_month_prompt: 'Quotenmonat',
      desired_delivery_date: 'Wunschlieferdatum',
      desired_delivery_date_prompt: 'Wunschlieferdatum',
      desired_date_must_be_after_quota_month_and_after_today:
        'Wunschlieferdatum muss nach Quotenmonat und nach dem heutigen Datum liegen',
      port_name: 'Hafenname',
      port_code_prompt: 'Empfangshafen',
      port_code_standard: 'Standardhafen',
      port_code_alternative: 'Alternativhäfen',
      port_code_selection: 'Bitte wählen Sie den Empfangshafen aus der Liste aus',
      port_code_not_editable: 'Sie dürfen den Hafen nicht anpassen',
      shipping_code: 'Versandbedingung',
      shipping_code_prompt: 'Versandbedingung',
      shipping_code_selection: 'Bitte wählen Sie die Versandbedingung aus der Liste aus',
      shipping_code_not_editable: 'Sie dürfen die Versandbedingung nicht anpassen',
      get_order_success_header: 'Auftrag mit ID "{{ncoid}}"',
      order_success_header: 'Auftrag mit ID "{{ncoid}}" wurde erfolgeich angelegt oder geändert',
      dealer_data_error:
        'Fehler beim Laden der Daten für den Händler. Es fehlen Versandbedingungen oder Auftragsarten.',
      no_dealer_data: 'Keine Daten für den Händler gefunden',
      importer: 'Importeur',
      dealer: 'Händler',
      error_missing_order_types: 'Keine Auftragsart für diesen Importeur/Händler',
      error_missing_port_codes: 'Keine Hafencodes für diesen Importeur/Händler',
      error_missing_shipping_codes: 'Keine Versandbedingungen für diesen Importeur/Händler',
      importer_number: 'Importeurs-Nummer',
      dealer_number: 'Händler-Nummer',
      importer_code: 'Importeurs-Code',
      is_cancelled: 'Storniert',
      config_id_prompt: 'Konfigurations-ID',
      continue_to_kcc: 'Weiter zu KCC',
      cancel_to_kcc: 'Zurück zu KCC',
      order_found: 'Auftrag gefunden',
      submit_order_change: 'Auftrag ändern',
      order_status_onevms_code_prompt: 'Auftragsstatus',
      no_quota_err_title: 'Der Auftrag konnte nicht gespeichert werden',
      purchase_intention_no_quota_err_title: 'Keine Quote',
      no_quota_err_msg: 'Keine PAG & Händlerquote für die gewählte Modelltyp / Modelljahr Kombination vorhanden',
      requested_quota_unavailable_title: 'Gewünschte Quote nicht verfügbar',
      requested_quota_unavailable_msg:
        'Die Quote bei Auftragsbedarf ist nicht verfügbar; eine alternative Quote wird herangezogen',
      prefilled_quota_not_available: 'Die gewünschte Quote ist leider nicht verfügbar',
      selected_quota_has_no_free_quota: 'Für den gewählten Quotenmonat ist leider keine Quote verfügbar',
      quota_month_selection: 'Quotenmonat wählen',
      seller: 'Verkäufer',
      business_partner_id: 'Geschäftspartner-ID',
      dealer_id: 'Händler-ID',
      customer_name: 'Kundenname',
      search_params_validation_error: 'Die eingegebene Kombination der Parameter konnte nicht validiert werden',
      navigate_to_deal_list: 'Purchase Intention Liste öffnen',
      navigate_to_order_list: 'Auftragsliste öffnen',
      navigate_to_dori: 'Auftragsdetails öffnen',
      navigate_to_quota_dashboard: 'Quotendashboard öffnen',
      navigate_back_to_quota_dashboard: 'Zurück zum Quotendashboard',

      //Cancel Car Order
      cancel_orders: 'Aufträge stornieren',
      err_in_form: 'Fehler in Formular! Siehe unten für weitere Informationen',

      //total loss
      reported: 'gemeldet',
      reported_upper: 'Gemeldete',
      report_total_loss: 'Totalverlust melden',
      revoked: 'widerrufen',
      revoked_upper: 'Widerrufene',
      revoke_total_loss: 'Totalverlust widerrufen',
      total_loss_orders: 'Aufträge im Totalverlust (Widerrufbar)',
      total_loss_yes: 'Totalverlust {{totalLossAction}}',
      total_loss_no: 'Abbrechen',
      total_loss_finish: 'Zurück zur Auftragsliste',
      failed_reported_orders: 'Fehlgeschlagene Aufträge',
      failed_revoked_orders: 'Fehlgeschlagene Aufträge',
      failed_reported_orders_error: 'Die folgenden Aufträge konnten nicht erfolgreich verarbeitet werden',
      failed_revoked_orders_error: 'Die folgenden Aufträge konnten nicht erfolgreich verarbeitet werden',
      reported_orders: 'Gemeldete Totalschäden',
      revoked_orders: 'Widerrufene Totalschäden',

      //Order action status
      order_action_wrong_status: 'Der Auftrag hat einen Status, der diese Aktion verhindert.',
      total_loss_too_long_ago:
        'Die Zeitgrenze von 72 Stunden wurde überschritten. Diese Aktion ist nicht mehr erlaubt.',
      order_action_forbidden: 'Sie haben nicht die erforderlichen Berechtigungen, um diese Aktion auszuführen.',
      order_not_found: 'Der angegebene Auftrag konnte nicht gefunden werden.',
      aurora_error:
        'Beim Speichern des Auftrags ist ein interner Fehler aufgetreten. Bitte versuchen Sie es später erneut.',

      //Handle dealer inventory action
      move_to_dealer_inventory: 'In den Händlerbestand überführen',
      remove_from_dealer_inventory: 'Aus dem Händlerbestand entfernen',
      moved_orders: 'Überführte Aufträge',
      removed_orders: 'Entfernte Aufträge',
      failed_moved_orders: 'Fehlgeschlagene Auftrags-Überführungen',
      failed_removed_orders: 'Fehlgeschlagene Auftrags-Entfernungen',
      failed_moved_orders_error: 'Die folgenden Aufträge konnten nicht überführt werden',
      failed_removed_orders_error: 'Die folgenden Aufträge konnten nicht entfernt werden',

      //LISTS
      'aggrid.pagination.to': 'bis',
      'aggrid.pagination.of': 'von',
      'aggrid.pagination.more': 'mehr',
      navigate_page: 'Seite navigieren',
      close: 'Schließen',
      copy_order: 'Auftrag Kopieren',
      edit_order: 'Auftrag Ändern',
      cancel_order: 'Auftrag Stornieren',
      deallocate_quota: 'Quoten freigeben',
      order_details: 'Auftragdetails',
      copy_order_head: '{{successes}}/{{total}} Aufträge angelegt aus Auftragskopie',
      open_cora_modal: 'CORA Modal öffnen (Testing)',
      use_kcc_dummy: 'KCC Dummy (Testing)',
      select_cancel_reason: 'Grund der Stornierung',
      cancel_reason_placeholder: 'Grund wählen',
      cancel_order_question: 'Möchten Sie diesen Auftrag stornieren?',
      cancel_orders_question: 'Möchten Sie diese Aufträge stornieren?',
      submit_order_no: 'Abbrechen',
      failed_cancelled_orders: 'Fehlgeschlagene Auftrags-Stornierungen',
      failed_cancelled_orders_error: 'Die folgenden Aufträge konnten nicht storniert werden',
      selected_orders: 'Ausgewählte Aufträge',
      cancelled_orders: 'Stornierte Aufträge',
      cancellation_status: 'Status',
      orders: 'Aufträge',
      pk_new_car_order_id: 'New Car Order ID',
      quota_failed: 'Quotenprüfung fehlgeschlagen',
      copy_order_warning_quota: 'Keine verfügbaren Quoten für die nächsten sechs Monate.',
      copy_order_error_quota: 'Die gewählte Quotenanzahl darf die verfügbaren Quoten nicht überschreiten.',
      copy_order_error_quota_limit: 'Die gewählte Quotenanzahl überschreitet das Limit von 100.',
      copy_order_quota_month: 'Quotenmonat',
      copy_order_available_month: 'Verfügbare Quote',
      copy_order_input_month: 'Gewählte Quote',
      copy_order_no: 'Abbrechen',
      copied_orders: 'Angelegte Aufträge',
      copy_order_finish: 'Zur Auftragsliste zurückkehren',
      copy_order_fail_head: '{{successes}}/{{total}} Aufträge angelegt aus Auftragskopie',
      failed_copy_orders: 'Fehlgeschlagene Aufträge',
      failed_copy_orders_error: 'Die folgenden Aufträge konnten nicht kopiert werden:',
      copy_orders_error: 'Fehler beim Anlegen der Aufträge',
      copy_ids: 'IDs kopieren',
      copy_ids_success: 'Auftrags-IDs wurden in die Zwischenablage kopiert',
      copy_to_clipboard: 'Erfolgreich in die Zwischenablage kopiert',
      purchase_intentions: 'Auftragsbedarfe',
      changeable_orders: 'Änderbare Aufträge',
      non_changeable_orders: 'Nicht änderbare Aufträge',
      pre_production_orders: 'Aufträge vor Produktion',
      in_production_orders: 'Aufträge in Produktion',
      in_distribution_orders: 'Aufträge in Distribution',
      at_dealership_orders: 'Auträge beim Händler',
      purchase_intention_id: 'Purchase Intention ID',
      new_car_order_id: 'Auftrags ID',
      order_type: 'Auftragsart',
      quota_month: 'Quotenmonat',
      loading_quota: 'Quoten werden geladen..',
      model_year: 'Modelljahr',
      model_text: 'Modelltyp', //type is used internally for a lot of references, only translate it like this
      model_type: 'Modellcode',
      model_type_text: 'Modelltype',
      dealer_name: 'Händlername',
      order_status_onevms_code: 'Auftragsstatus',
      vehicle_status_pvms_code: 'Auftragsstatus',
      order_status_onevms_error_code: 'Fehlerstatus',
      sperrgrund: 'Sperrgrund',
      kate: 'Kate',
      qs_sperren: 'QS Sperren',
      timestamp: 'Timestamp',
      row_actions: 'Aktionen',
      convert_to_order: 'In Auftrag Konvertieren',
      convert_to_order_multi: 'Ausgewählte Auftragsbedarfe konvertieren',
      missing_translation: 'Fehlende Übersetzung',
      missing_status: 'Fehler: Kein Auftragsstatus gefunden',
      not_loaded: 'Lädt...',
      show_all: 'Zeige alle',
      show_less: 'Einklappen',
      authorized_dealer: 'Autorisierte Händler',

      //update core data
      update_core_data: 'Auftragsdaten ändern',
      update_core_data_customer_related_error_multi:
        'Mindestens einer der gewählten Aufträge ist kundenbezogen und die Auftragsart kann daher nicht geändert werden.',
      update_core_data_customer_related_error_single:
        'Der ausgewählte Auftrag ist kundenbezogen und die Auftragsart kann daher nicht geändert werden.',
      error_with_code_single:
        'Der Auftrag ist bereits storniert oder ausgeliefert und kann daher nicht geändert werden.',
      error_with_code_multi:
        'Mindestens einer der Aufträge ist bereits storniert oder ausgeliefert und kann daher nicht geändert werden.',

      //Importer Transfer

      importer_transfer: 'Importeurstransfer',
      importer_transfer_order_no: 'Abbrechen',
      importer_transfer_order_finish: 'Zurück zur Auftragsliste',
      importer_transfer_next_step: 'Weiter zur Detailerfassung',
      importer_transfer_new_importer: 'Neuer Importeur',
      importer_transfer_new_dealer: 'Neuer Händler',
      importer_transfer_old_id: 'Vorherige New Car Order Id',
      importer_transfer_failed_transfer: 'Aufträge konnten nicht übertragen werden',
      importer_transfer_successful_transfer_multi_header: '{{successes}}/{{total}} Erfolgreich transferierte Aufträge',
      importer_transfer_successful_transfer_multi: 'Erfolgreich transferierte Aufträge',
      importer_transfer_partial_fail: '{{successes}}/{{total}} Aufträge erfolgreich transferiert',
      importer_transfer_successful_transfer_single: 'Erfolgreich transferierter Auftrag',
      importer_transfer_failed_transfer_single: 'Auftrag konnte nicht transferiert werden',
      importer_transfer_failed_noti_single:
        'Leider konnten wir den Transfer nicht abschließen. Weitere Details siehe unten.',
      importer_transfer_select_dealer: 'Händler auswählen',
      importer_transfer_select_importer: 'Importeur auswählen',
      importer_transfer_current_dealer: 'Aktueller Händler',
      importer_transfer_current_importer: 'Aktueller Importeur',
      importer_transfer_user_guide:
        'Weitere Details zu den Fehlern können durch Hovern über das Icon angezeigt werden.',

      //Process steering
      transaction_details: 'Transaktionsdetails',
      transaction_id: 'Transaktions-ID',
      affected_nco_id: 'Betroffene New Car Order ID',
      affected_nco_ids: 'Betroffene New Car Order IDs',

      //Order actions common
      [`${OneVmsEventKey.UPDATE_NCO}_order_successful_header`]: 'Änderungsanfrage Angenommen',
      [`${OneVmsEventKey.UPDATE_NCO}_order_failed_header`]: 'Änderungsanfrage fehlgeschlagen',
      [`${OneVmsEventKey.UPDATE_NCO}_order_failed`]: 'Leider konnten wir die Änderungsanfrage nicht abschließen.',
      [`${OneVmsEventKey.UPDATE_NCO}_order_successful`]:
        'Die Änderungsanfrage wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Notifaction Center sichtbar.',
      [`${OneVmsEventKey.UPDATE_NCO}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.UPDATE_NCO}_order_modal`]: 'Bestellung ändern',
      [`${OneVmsEventKey.UPDATE_NCO}_order_yes`]: 'Änderung bestätigen',
      [`${OrderActionType.IMPORTER_TRANSFER}_orders_modal`]: 'Importeurstransfer Aufträge',
      [`${OrderActionType.IMPORTER_TRANSFER}_order_modal`]: 'Importeurstransfer Auftrag',
      [`${OrderActionType.IMPORTER_TRANSFER}_order_yes`]: 'Transfer bestätigen',
      [`${OrderActionType.COPY}_order_yes`]: 'Auftragskopie bestätigen',
      [`${OrderActionType.COPY}_order_success`]: 'Aufträge angelegt aus Auftragskopie',
      [`${OrderActionType.COPY}_order_fail_noti`]: 'Leider konnten nicht alle Aufträge kopiert werden.',
      [`${OrderActionType.COPY}_order_modal`]: 'Auftrag kopieren',
      [`${OneVmsEventKey.CANCEL}_order_yes`]: 'Auftragsstornierung bestätigen',
      [`${OneVmsEventKey.CANCEL}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.CANCEL}_order_successful_header`]: 'Stornierungs-Anfrage Angenommen',
      [`${OneVmsEventKey.CANCEL}_order_failed_header`]: 'Stornierungs-Anfrage fehlgeschagen',
      [`${OneVmsEventKey.CANCEL}_order_failed`]:
        'Leider konnte die Stornierungs-Anfrage nicht durchgeführt werden. {{message}}',
      [`${OneVmsEventKey.CANCEL}_order_successful`]:
        'Die Stornierungs-Anfrage wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.CANCEL}_order_modal`]: 'Auftrag stornieren',
      [`${OneVmsEventKey.CANCEL}_orders_modal`]: 'Aufträge stornieren',
      [`${OneVmsEventKey.CREATE}_order_yes`]: 'Auftragsanlage bestätigen',
      [`${OneVmsEventKey.CREATE}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.CREATE}_order_successful_header`]: 'Anlagen-Anfrage Angenommen',
      [`${OneVmsEventKey.CREATE}_order_failed_header`]: 'Anlagen-Anfrage fehlgeschlagen',
      [`${OneVmsEventKey.CREATE}_order_failed`]:
        'Leider konnte die Anlagen-Anfrage nicht durchgeführt werden. {{message}}',
      [`${OneVmsEventKey.CREATE}_order_successful`]:
        'Die Anlagen-Anfrage wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.CREATE}_order_modal`]: 'Auftrag anlegen',
      [`${OneVmsEventKey.CREATE}_orders_modal`]: 'Aufträge anlegen',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_modal`]: 'Auftrag ändern',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_orders_modal`]: 'Aufträge ändern',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_yes`]: 'Änderung bestätigen',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_successful_header`]: 'Änderungsanfrage Angenommen',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_failed_header`]: 'Änderungsanfrage fehlgeschagen',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_failed`]:
        'Leider konnte die Änderungsanfrage nicht durchgeführt werden: {{message}}',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_successful`]:
        'Die Änderungsanfrage wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_yes`]: 'Auftragsüberführung bestätigen',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_orders_modal`]: 'Aufträge in den Händlerbestand überführen',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_modal`]: 'Auftrag in den Händlerbestand überführen',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_successful`]:
        'Die Anfrage zur Auftragsüberführung in den Händlerbestand wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_successful_header`]: 'Überführungsanfrage Angenommen',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_failed`]:
        'Leider konnte die Anfrage zur Auftragsüberführung nicht durchgeführt werden: {{message}}',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_failed_header`]: 'Anfrage zur Auftragsüberführung fehlgeschlagen',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_orders_modal`]: 'Aufträge aus dem Händlerbestand entfernen',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_yes`]: 'Auftragsentfernung bestätigen',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_modal`]: 'Auftrag aus dem Händlerbestand entfernen',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_successful`]:
        'Die Anfrage zur Auftragsentfernung aus dem Händlerbestand wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_successful_header`]: 'Entfernungsanfrage Angenommen',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_failed`]:
        'Leider konnte die Anfrage zur Auftragsentfernung nicht durchgeführt werden: {{message}}',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_failed_header`]: 'Anfrage zur Auftragsentfernung fehlgeschlagen',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_failed_header`]: 'Anfrage des Totalverlust meldens fehlgeschagen',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_successful`]:
        'Die Anfrage des Totalverlust meldens wurde erfolgreich versendet. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_successful_header`]:
        'Anfrage des Totalverlust meldens erfolgreich angenommen',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_failed`]:
        'Leider konnte die Anfrage des Totalverlust meldens nicht durchgeführt werden. {{message}}',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_success`]: 'Totalschäden gemeldet',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_success`]: 'Totalverlust gemeldet',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_modal`]: 'Totalverlust für Auftrag melden',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_modal`]: 'Totalverlust für Aufträge melden',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_info`]:
        'Nach dem Melden des Totalverlust ist ein Widerruf für drei Kalendertage möglich. Am {{deadlineDate}} läuft die Widerrufsfrist ab.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_info_pl`]:
        'Nach dem Melden des Totalverlust ist ein Widerruf für drei Kalendertage möglich. Am {{deadlineDate}} läuft die Widerrufsfrist ab.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_fail_head`]: 'Totalverlust melden fehlgeschlagen',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_fail_head`]:
        '{{successes}}/{{total}} Aufträge im Totalverlust gemeldet',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_fail_noti`]:
        'Leider konnte das Melden des Totalverlust nicht ausgeführt werden.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_fail_noti`]:
        'Leider konnte das Melden der Totalverlust Status nicht komplett durchgeführt werden. Siehe unten für mehr Details.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_yes`]: 'Melden',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_failed_header`]: 'Anfrage Totalverlust Widerrufen fehlgeschlagen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_successful`]:
        'Die Anfrage des Totalverlust Widerrufens wurde erfolgreich versendet. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_successful_header`]:
        'Anfrage des Totalverlust Widerrufens erfolgreich angenommen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_failed`]:
        'Leider konnte die Anfrage des Totalverlust Widerrufens nicht durchgeführt werden. {{message}}',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_success`]: 'Totalschäden widerrufen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_success`]: 'Totalverlust widerrufen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_modal`]: 'Totalverlust für Auftrag widerrufen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_modal`]: 'Totalverlust für Aufträge widerrufen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_info`]:
        'Das Widerrufen des Totalverlust setzt den Auftrag in den vorherigen Status zurück.',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_info_pl`]:
        'Das Widerrufen des Totalverlust setzt die Aufträge in die vorherigen Status zurück.',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_fail_head`]: 'Totalverlust Widerrufen fehlgeschlagen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_fail_head`]:
        '{{successes}}/{{total}} Aufträge im Totalverlust widerrufen',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_fail_noti`]:
        'Leider konnte das Widerrufen des Totalverlust nicht ausgeführt werden.',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_fail_noti`]:
        'Leider konnte das Widerrufen der Totalverlust Status nicht vollständig durchgeführt werden. Siehe unten für mehr Details.',

      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_yes`]: 'Widerrufen',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_failed`]:
        'Leider konnte die Anfrage zur Quotenfreigabe nicht durchgeführt werden. {{message}}',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_failed_header`]: 'Anfrage zur Quotenfreigabe fehlgeschagen',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_successful`]:
        'Die Anfrage zur Quotenfreigabe wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_successful_header`]: 'Anfrage Zur Quotenfreigabe Angenommen',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_modal`]: 'Quoten für Auftrag freigeben',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_orders_modal`]: 'Quoten für Aufträge freigeben',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_yes`]: 'Quoten freigeben',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_finish`]: 'Zurück zur Auftragsliste',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_info`]:
        'Nach dem Freigeben der Quote wird der Auftrag bei Kundenfahrzeugen nach 6 Monaten und bei Nicht-Kundenbezogenen Auftragsarten automatisch nach 1 Monat storniert',

      [`${OneVmsEventKey.CONVERT_PI}_order_failed`]:
        'Leider konnte die Konvertierung der Auftragsanfrage in einen Auftrag nicht durchgeführt werden. {{message}}',
      [`${OneVmsEventKey.CONVERT_PI}_order_failed_header`]: 'Konvertierungs-Anfrage fehlgeschlagen',
      [`${OneVmsEventKey.CONVERT_PI}_order_successful`]:
        'Die Konvertierung der Auftragsanfrage in einen Auftrag wurde übermittelt und die Transaktion befindet sich nun in Bearbeitung. Der Status und das Ergebnis sind im Benachrichtigungszentrum sichtbar.',
      [`${OneVmsEventKey.CONVERT_PI}_order_successful_header`]: 'Konvertierungs-Anfrage Angenommen',
      [`${OneVmsEventKey.CONVERT_PI}_order_modal`]: 'Auftragsanfrage in Auftrag konvertieren',
      [`${OneVmsEventKey.CONVERT_PI}_order_yes`]: 'Konvertieren',
      [`${OneVmsEventKey.CONVERT_PI}_order_finish`]: 'Zurück zur Auftragsanfragen-Liste',

      //concurrency
      concurrency_error:
        'Der Auftrag wurde zwischenzeitlich von einem anderen Prozess geändert oder aktualisiert. Bitte laden Sie ihn neu und prüfen die Daten vor der Aktionsausführung',
      cancelled_racing_cond_orders: 'Auftragsstornierung fehlgeschlagen',
      cancelled_racing_cond_orders_error: 'Folgende Aufträge konnten nicht storniert werden',
      deallocate_quotaed_racing_cond_orders: 'Auftragsdeallokierung fehlgeschlagen',
      deallocate_quotaed_racing_cond_orders_error: 'Folgende Aufträge konnten nicht deallokiert werden',
      importer_transfer_racing_cond_orders: 'Importeurstransfer fehlgeschlagen',
      importer_transfer_racing_cond_orders_error: 'Folgende Aufträge konnten nicht transferiert werden',
      reported_racing_cond_orders: 'Melden des Totalverlustes fehlgeschlagen',
      reported_racing_cond_orders_error: 'Folgende Aufträge konnten nicht als Totalverlust gemeldet werden',
      revoked_racing_cond_orders: 'Korrigieren des Totalverlustes fehlgeschlagen',
      revoked_racing_cond_orders_error: 'Folgende Aufträge konnten nicht aus dem Totalverlust korrigiert werden',
      update_core_data_racing_cond_orders: 'Auftragsänderung fehlgeschlagen',
      update_core_data_racing_cond_orders_error: 'Folgende Aufträge konnten nicht akualisiert werden',

      buy_sell_transfer_header: 'Buy-Sell-Transfer',
      buy_sell_transfer_fail_head: '{{successes}}/{{total}} Bestellungen wurden übertragen',
      buy_sell_transfer_fail_noti: 'Leider konnten wir den Transfer nicht abschließen.',
      buy_sell_transfer_success: 'Aufträge erfolgreich transferiert',
      buy_sell_transfer_success_header: '{{successes}}/{{total}} Erfolgreich übertragene Aufträge',
      buy_sell_transfer_importer_number: 'Importeur-Nummer',
      buy_sell_transfer_importer_name: 'Importeur-Name',
      buy_sell_transfer_importer: 'Importeur',
      buy_sell_transfer_source_dealer_number: 'Quellhändler-Nummer',
      buy_sell_transfer_source_dealer_name: 'Quellhändler-Name',
      buy_sell_transfer_source_dealer: 'Quellhändler',
      buy_sell_transfer_target_dealer_number: 'Zielhändler-Nummer',
      buy_sell_transfer_target_dealer_name: 'Zielhändler-Name',
      buy_sell_transfer_target_dealer: 'Zielhändler',
      buy_sell_transfer_source_total_orders: 'Gesamtzahl der zu transferierenden Aufträge',
      buy_sell_transfer_next: 'Weiter',
      buy_sell_transfer_confirm: 'Buy-Sell-Transfer bestätigen',
      buy_sell_transfer_confirm_message:
        'Sind Sie sicher, dass Sie {{amount}} Aufträge zu Händler {{dealerNumber}} - {{dealerName}} transferieren wollen?',
      high_level_actions_header: 'High-level Aktions-Navigations-Space',
      high_level_actions_description:
        'Innerhalb des High-level Aktions-Navigations-Spaces können weitere Auftragsaktionen ausgeführt werden, welche nicht innerhalb der Auftragslisten verfügbar sind.',
      no_access: 'Keine Berechtigung für Aktionen in diesem Bereich.',
    },
  },
  en: {
    translation: {
      language: 'Sprache: Deutsch',
      back: 'Back',
      continue: 'Continue',
      view: 'View',
      edit: 'Change',
      delete: 'Delete',
      deactivate: 'Deactivate',
      reactivate: 'Reactivate',
      reset: 'Reset',
      save: 'Save',
      filter: 'Search',
      changeable: 'Changeable',
      non_changeable: 'Non-changeable',
      no_rows_to_show: 'Data is not ready or empty',
      session_timeout: 'Session Timeout. The session could not be refreshed (Error: {{errorMessage}}).',
      session_timer: 'Session Timer: {{time}}',
      delete_entity: 'Deactivate {{name}}',
      restore_entity: 'Restore {{name}}',
      delete_entity_with_id: 'Deactivate {{name}} {{id}}?',
      restore_entity_with_id: 'Restore {{name}} {{id}}?',
      create_entity: 'Create {{name}}',
      update_entity: 'Update "{{name}}"',
      view_entity: '{{name}} details',
      validation_error: 'The current order is not valid for the entered data',
      no_value_field_required: 'No value for required field',
      not_unique: 'An object with this value already exists',
      arr_not_empty: 'Please select at least one entry',
      validation_failed: 'Validation failed',
      confirm_delete: 'Confirm Delete',
      confirm_delete_details: 'Delete "{{entity}}"?',
      confirm_action: 'Confirm',
      cancel: 'Cancel',
      field: 'Field',
      empty_field: '(empty)',
      per_page: '/Page',
      pagesize: 'Orders per Page',
      no_entities: 'No elements found...',
      no_purchase_intention_id_in_params: 'Missing Purchase Intention Id',
      no_new_car_order_id_in_params: 'Missing NewCarOrder Id',
      details: 'Details',
      loading: 'Loading...',
      logout: 'Log out',
      is_deactivated: 'Deactivated',
      is_deactivated_true: 'Inactive',
      is_deactivated_false: 'Active',
      is_deactivated_undefined: 'Unfiltered',
      created_at: 'Created at',
      created_by: 'Created by',
      modified_at: 'Modified at',
      modified_by: 'Modified by',
      changed_on_date: 'Last change on',
      changed_by: 'Last change by',
      filter_for_more: '... provide filter for more entries ({{length}} total)...',
      no_app_permissions: 'You do not have permissions to access CORA',
      user_permission_label: 'Rights:',
      user_permission_write: 'Read, Write',
      user_permission_read: 'Read',
      user_permission_none: 'None',
      api_error: 'Internal API Error',
      fetch_error: 'Error during `fetch` - Data could not be retrieved',
      serialization_error: 'Serialization Error',
      unknown_error: 'Unknown Error',
      error_area: 'Error area',
      missing_parameters: 'There are missing parameters',

      // New Car Order
      config_validity_excludes_selected_quota: 'Options in the configuration prohibit the chosen quota',
      existing_quota: 'Current quota of this order',
      create_order: 'Create New Order',
      change_order: 'Change order',
      change_order_button: 'Change order',
      order_edited: 'Order has been succesfully changed',
      order_created: 'Order has been successfully created',
      convert_order: 'Convert purchase intention',
      convert_order_button: 'Convert to order',
      order_id: 'Order ID',
      fetch_quotas: 'Fetch Quotas',
      wrong_order_id: 'The Order ID must consist of eight or nine letters or digits. Please check it again!',
      dealer_prompt: 'Select a dealer',
      dealer_selection_prompt: 'Please select a dealer from the list',
      convert_purchase_intention: 'Convert Purchase Intention',
      convert_purchase_intention_success: 'Purchase Intention converted to order',
      purchase_intention_loading_error: 'Failed to load Purchase Intention(s)',
      purchase_intention_loading_error_description:
        'Purchase Intention {{purchaseIntentionId}} does not exist, or you do not have rights for it',
      dealer_selection_error: 'Failed to select the corresponding dealers',
      dealer_selection_error_description: 'Dealer {{dealer_number}} does not exist, or you do not have rights for it',
      submit_order: 'Submit Order',
      order_type_prompt: 'Order Type',
      order_type_selection: 'Please select an order type from the list',
      model_type_prompt: 'Model Type',
      model_type_selection: 'Please select a model type from the list',
      model_year_prompt: 'Model Year',
      model_year_selection: 'Please select a model year from the list',
      quota_month_prompt: 'Quota Month',
      desired_delivery_date: 'Desired Delivery Date',
      desired_delivery_date_prompt: 'Desired Delivery Date',
      desired_date_must_be_after_quota_month_and_after_today:
        'Date needs to be after quota month and after today´s date',
      port_name: 'Port Name',
      port_code_prompt: 'Recieving Port Code',
      port_code_standard: 'Standard Port',
      port_code_alternative: 'Alternative Ports',
      port_code_selection: 'Please select a receiving port code from the list',
      port_code_not_editable: 'You are not allowed to change the Port Code',
      shipping_code: 'Shipping Code',
      shipping_code_prompt: 'Shipping Code',
      shipping_code_selection: 'Please select a shipping code from the list',
      shipping_code_not_editable: 'You are not allowed to change the Shipping Code',
      order_success_header: 'Successfully created or changed order with ID "{{ncoid}}"',
      get_order_success_header: 'Order with ID "{{ncoid}}"',
      dealer_data_error: 'Error fetching data for dealer. Missing shipping codes or order types.',
      no_dealer_data: 'No data found for dealer',
      importer: 'Importer',
      dealer: 'Dealer',
      error_missing_order_types: 'No Order Types for this Importer/Dealer',
      error_missing_port_codes: 'No Port Codes for this Importer/Dealer',
      error_missing_shipping_codes: 'No Shipping Codes for this Importer/Dealer',
      pk_new_car_order_id: 'New Car Order ID',
      importer_number: 'Importer-Number',
      dealer_number: 'Dealer-Number',
      importer_code: 'Importer-Code',
      is_cancelled: 'Cancelled',
      config_id_prompt: 'Configuration ID',
      continue_to_kcc: 'Continue to KCC',
      cancel_to_kcc: 'Back to KCC',
      order_found: 'Order found',
      submit_order_change: 'Submit Order Change',
      order_status_onevms_code_prompt: 'Order Status',
      no_quota_err_title: 'Could not save order',
      purchase_intention_no_quota_err_title: 'No Quota',
      no_quota_err_msg: 'No PAG & Dealer quota available for the selected combination of modeltype / modelyear',
      requested_quota_unavailable_title: 'Requested Quota Unavailable',
      requested_quota_unavailable_msg:
        'The quota for the Purchase Intention is not available; an alternative quota will be prefilled.',
      prefilled_quota_not_available: 'The prefilled quota is not available',
      selected_quota_has_no_free_quota: 'For the selected quota month, there is no quota available',
      quota_month_selection: 'Select quota month',
      seller: 'Seller',
      business_partner_id: 'Business-Partner-ID',
      dealer_id: 'Dealer-ID',
      customer_name: 'Customer Name',
      search_params_validation_error: 'Validation for this combination of parameters failed',
      navigate_to_deal_list: 'Open list of Purchase Intentions',
      navigate_to_order_list: 'Open order list',
      navigate_to_dori: 'Open order details',
      navigate_to_quota_dashboard: 'Open quota dashboard',
      navigate_back_to_quota_dashboard: 'Back to quota dashboard',

      //Cancel Car Order
      cancel_orders: 'Cancel Orders',
      err_in_form: 'Error in form! See below for further information',

      //total loss
      reported: 'reported',
      reported_upper: 'Reported',
      report_total_loss: 'Report total loss',
      revoked: 'revoked',
      revoked_upper: 'Revoked',
      revoke_total_loss: 'Revoke total loss',

      total_loss_orders: 'Orders in Total Loss (Revokable)',
      total_loss_yes: '{{totalLossAction}} total loss',
      total_loss_no: 'Cancel',
      total_loss_finish: 'Back To Order List',
      failed_reported_orders: 'Failed orders',
      failed_revoked_orders: 'Failed orders',
      failed_reported_orders_error: 'The following total loss status could not be reported',
      failed_revoked_orders_error: 'The following total loss status could not be revoked',
      reported_orders: 'Reported total losses',
      revoked_orders: 'Revoked total losses',

      //Order Action status
      order_action_wrong_status: 'The order has a status that does not allow this operation.',
      total_loss_too_long_ago: 'The time limit of 72 hours has been exceeded. Operation is not permissible anymore.',
      order_action_forbidden: 'You do not have the necessary permissions to perform this operation.',
      order_not_found: 'The order could not be found.',
      aurora_error: 'An internal server error occurred while saving the order. Please try again later.',

      //Handle dealer inventory action
      move_to_dealer_inventory: 'Move to dealer inventory',
      remove_from_dealer_inventory: 'Remove from dealer inventory',
      moved_orders: 'Moved orders',
      remove_orders: 'Removed orders',
      failed_moved_orders: 'Failed order moves',
      failed_removed_orders: 'Failed order removes',
      failed_moved_orders_error: 'The following orders could not be moved',
      failed_removed_orders_error: 'The following orders could not be removed',

      //LISTS
      'aggrid.pagination.to': 'to',
      'aggrid.pagination.of': 'of',
      'aggrid.pagination.more': 'more',
      navigate_page: 'Navigate page',
      close: 'Close',
      copy_order: 'Copy order',
      edit_order: 'Change order',
      cancel_order: 'Cancel order',
      deallocate_quota: 'Deallocate Quota',
      order_details: 'Order details',
      copied_orders: 'Created Orders',
      open_cora_modal: 'Open CORA modal (testing)',
      use_kcc_dummy: 'Use KCC dummy (testing)',
      select_cancel_reason: 'Reason for cancellation',
      cancel_reason_placeholder: 'Select reason',
      cancel_order_question: 'Do you want to cancel the order?',
      cancel_orders_question: 'Do you want to cancel the orders?',
      submit_order_no: 'Cancel',
      failed_cancelled_orders: 'Failed order cancellations',
      failed_cancelled_orders_error: 'The following orders could not be cancelled',
      selected_orders: 'Selected orders',
      cancelled_orders: 'Cancelled orders',
      cancellation_status: 'Status',
      orders: 'Orders',
      quota_failed: 'Quota Check Failed',
      copy_order_warning_quota: 'No available quotas for the next six months.',
      copy_order_error_quota: 'Input quota cannot exceed available quota.',
      copy_order_error_quota_limit: 'Input quota exceeds the limit of 100.',
      copy_order_quota_month: 'Quota Month',
      copy_order_available_month: 'Available Quota',
      copy_order_input_month: 'Input Quota',
      copy_order_finish: 'Return to Order List',
      copy_order_no: 'Cancel',
      copy_orders_error: 'Error occurred during order creation.',
      copy_order_fail_head: '{{successes}}/{{total}} orders created from copy order',
      copy_order_head: '{{successes}}/{{total}} orders created from copy order',
      purchase_intentions: 'Purchase Intentions',
      changeable_orders: 'Changeable Orders',
      non_changeable_orders: 'Non-changeable Orders',
      pre_production_orders: 'Orders before Production',
      in_production_orders: 'Orders in Production',
      in_distribution_orders: 'Orders in Distribution',
      at_dealership_orders: 'Orders at Dealership',
      purchase_intention_id: 'Purchase Intention ID',
      new_car_order_id: 'Order ID',
      order_type: 'Order Type',
      quota_month: 'Quota Month',
      loading_quota: 'Quotas are being loaded..',
      model_year: 'Model Year',
      model_text: 'Model Type', //type is used internally for a lot of references, only translate it like this
      model_type: 'Model Code',
      model_type_text: 'Model Type',
      dealer_name: 'Dealer Name',
      order_status_onevms_code: 'Order Status',
      vehicle_status_pvms_code: 'Order Status',
      order_status_onevms_error_code: 'Error Status',
      sperrgrund: 'Blocking Reason',
      kate: 'Kate',
      qs_sperren: 'QS Blocks',
      timestamp: 'Timestamp',
      row_actions: 'Actions',
      convert_to_order: 'Convert to order',
      convert_to_order_multi: 'Convert selected Purchase Intention',
      missing_translation: 'Missing translation',
      missing_status: 'Error: Did not find Order Status',
      not_loaded: 'Loading...',
      show_all: 'Show all',
      show_less: 'Collapse',
      copy_ids: 'Copy IDs',
      copy_ids_success: 'Order IDs copied to clipboard',
      copy_to_clipboard: 'Successfully copied to clipboard',
      failed_copy_orders: 'Failed orders',
      failed_copy_orders_error: 'The following orders could not be copied:',
      authorized_dealer: 'Authorized Dealer',

      //update core data
      update_core_data: 'Change order data',
      update_core_data_customer_related_error_multi:
        'At least one of the selected orders is customer related. Therefore the order type cannot be changed.',
      update_core_data_customer_related_error_single:
        'The selected order is customer related. Therefore the order type cannot be changed.',
      error_with_code_single: 'The order is already delivered or canceled and therefore cannot be changed.',
      error_with_code_multi:
        'At least one of the orders is already delivered or canceled and therefore cannot be changed.',
      //concurrency
      concurrency_error:
        'The order was updated or modified in the meantime by another process. Please reload and check the latest data before making changes.',
      cancelled_racing_cond_orders: 'Failed Order Cancellation',
      cancelled_racing_cond_orders_error: 'The following orders could not be cancelled',
      deallocate_quotaed_racing_cond_orders: 'Failed Order Deallocation',
      deallocate_quotaed_racing_cond_orders_error: 'The following orders could not be deallocated',
      importer_transfer_racing_cond_orders: 'Failed Importer Transfer',
      importer_transfer_racing_cond_orders_error: 'The following orders could not be transfered',
      reported_racing_cond_orders: 'Failed to Report Total Loss',
      reported_racing_cond_orders_error: 'The following orders could not be reported as total loss',
      revoked_racing_cond_orders: 'Failed to Revoke Total Loss',
      revoked_racing_cond_orders_error: 'The following orders could not be revoked from total loss',
      update_core_data_racing_cond_orders: 'Failed to Update Order',
      update_core_data_racing_cond_orders_error: 'The following orders could not be updated',

      //Importer Transfer
      importer_transfer: 'Importer Transfer',
      importer_transfer_order_no: 'Cancel',
      importer_transfer_order_finish: 'Return to Order List',
      importer_transfer_next_step: 'Continue to add details',
      importer_transfer_new_importer: 'New Importer Name',
      importer_transfer_new_dealer: 'New Dealer Name',
      importer_transfer_old_id: 'Previous New Car Order ID',
      importer_transfer_failed_transfer: 'Orders Failed to Transfer',
      importer_transfer_successful_transfer_multi_header: '{{successes}}/{{total}} Successfully Transferred Orders',
      importer_transfer_successful_transfer_multi: 'Successfully Transferred Orders',
      importer_transfer_partial_fail: '{{successes}}/{{total}} Orders Transferred',
      importer_transfer_successful_transfer_single: 'Successfully Transferred Order',
      importer_transfer_failed_transfer_single: 'Failed to Transfer Order',
      importer_transfer_failed_noti_single:
        'Unfortunately we could not complete the transfer request. Check below for details.',
      importer_transfer_select_dealer: 'Select Dealer',
      importer_transfer_select_importer: 'Select Importer',
      importer_transfer_current_dealer: 'Current Dealer',
      importer_transfer_current_importer: 'Current Importer',
      importer_transfer_user_guide:
        'When you hover over the error icons, you will see the details of the occurred error.',

      //Process steering
      transaction_details: 'Transaction Details',
      transaction_id: 'Transaction ID',
      affected_nco_id: 'Affected New Car Order ID',
      affected_nco_ids: 'Affected New Car Order IDs',

      //Order actions common
      [`${OneVmsEventKey.UPDATE_NCO}_order_successful_header`]: 'Update Request Accepted',
      [`${OneVmsEventKey.UPDATE_NCO}_order_failed_header`]: 'Update request failed',
      [`${OneVmsEventKey.UPDATE_NCO}_order_failed`]: 'Unfortunately we could not complete the update request.',
      [`${OneVmsEventKey.UPDATE_NCO}_order_successful`]:
        'The update request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.UPDATE_NCO}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.UPDATE_NCO}_order_modal`]: 'Update Order',
      [`${OneVmsEventKey.UPDATE_NCO}_order_yes`]: 'Confirm Update',
      [`${OrderActionType.IMPORTER_TRANSFER}_orders_modal`]: 'Importer Transfer Orders',
      [`${OrderActionType.IMPORTER_TRANSFER}_order_modal`]: 'Importer Transfer Order',
      [`${OrderActionType.IMPORTER_TRANSFER}_order_yes`]: 'Confirm Transfer',
      [`${OrderActionType.COPY}_order_yes`]: 'Confirm Copy Order',
      [`${OrderActionType.COPY}_order_fail_noti`]: 'Unfortunately not all orders could be copied.',
      [`${OrderActionType.COPY}_order_success`]: 'Order created from Copy Order',
      [`${OrderActionType.COPY}_order_modal`]: 'Copy order',
      [`${OneVmsEventKey.CANCEL}_order_yes`]: 'Confirm order cancellation',
      [`${OneVmsEventKey.CANCEL}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.CANCEL}_order_successful_header`]: 'Cancellation Request Accepted',
      [`${OneVmsEventKey.CANCEL}_order_failed_header`]: 'Cancellation Request Failed',
      [`${OneVmsEventKey.CANCEL}_order_failed`]: 'Unfortunately we could not complete the cancellation request.',
      [`${OneVmsEventKey.CANCEL}_order_successful`]:
        'The cancel request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.CANCEL}_orders_modal`]: 'Cancel orders',
      [`${OneVmsEventKey.CANCEL}_order_modal`]: 'Cancel order',
      [`${OneVmsEventKey.CREATE}_order_yes`]: 'Confirm order creation',
      [`${OneVmsEventKey.CREATE}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.CREATE}_order_successful_header`]: 'Creation Request Accepted',
      [`${OneVmsEventKey.CREATE}_order_failed_header`]: 'Creation request failed',
      [`${OneVmsEventKey.CREATE}_order_failed`]: 'Unfortunately we could not complete the creation request.',
      [`${OneVmsEventKey.CREATE}_order_successful`]:
        'The create request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.CREATE}_orders_modal`]: 'Create orders',
      [`${OneVmsEventKey.CREATE}_order_modal`]: 'Create order',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_modal`]: 'Change order',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_orders_modal`]: 'Change orders',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_yes`]: 'Confirm Change',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_successful_header`]: 'Change Request Accepted',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_failed_header`]: 'Change request failed',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_failed`]:
        'Unfortunately we could not complete the change request: {{message}}',
      [`${OneVmsEventKey.UPDATE_CORE_DATA}_order_successful`]:
        'The change request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_yes`]: 'Confirm order move',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_orders_modal`]: 'Move orders to dealer inventory',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_modal`]: 'Move order to dealer inventory',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_successful`]:
        'The move order to dealer inventory request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_successful_header`]: 'Move Order Request Accepted',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_failed`]:
        'Unfortunately we could not complete the move order request: {{message}}',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_failed_header`]: 'Move order request failed',
      [`${OneVmsEventKey.MOVE_TO_INVENTORY}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_orders_modal`]: 'Remove orders from dealer inventory',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_yes`]: 'Confirm order remove',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_modal`]: 'Remove order from dealer inventory',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_successful`]:
        'The remove order from dealer inventory request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_successful_header`]: 'Remove Order Request Accepted',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_failed`]:
        'Unfortunately we could not complete the remove order request: {{message}}',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_failed_header`]: 'Remove order request failed',
      [`${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_failed_header`]: 'Report total loss request failed',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_failed`]:
        'Unfortunately we could not complete the report total loss request.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_successful_header`]:
        'Report total loss request successfully accepted',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_successful`]:
        'Successfully dispatched the report total loss request. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_modal`]: 'Report total loss for Order',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_modal`]: 'Report total loss for Orders',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_yes`]: 'Report',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_success`]: 'Total losses reported',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_info`]:
        'After reporting a total loss, you can revoke the total loss process for three calendar days. The revoking period expires at {{deadlineDate}}.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_info_pl`]:
        'After reporting a total loss, you can revoke the total loss process for three calendar days. The revoking period expires at {{deadlineDate}}.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_fail_head`]: 'Report of total loss failed',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_fail_head`]: '{{successes}}/{{total}} orders reported as total loss',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_fail_noti`]:
        'Unfortunately the report of total loss could not be completed.',
      [`${OneVmsEventKey.TOTAL_LOSS_REPORT}_orders_fail_noti`]:
        'Unfortunately the report of total losses could not be completed. Check below for details.',

      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_failed_header`]: 'Revoke total loss request failed',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_failed`]:
        'Unfortunately we could not complete the revoke total loss request.',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_successful_header`]:
        'Revoke total loss request successfully accepted',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_successful`]:
        'Successfully dispatched the revoke total loss request. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_modal`]: 'Revoke total loss for Order',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_modal`]: 'Revoke total loss for Orders',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_yes`]: 'Revoke',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_success`]: 'Total losses revoked',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_fail_head`]: 'Revoke of total loss failed',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_fail_head`]: '{{successes}}/{{total}} orders revoked as total loss',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_fail_noti`]:
        'Unfortunately the revoke of total loss could not be completed.',
      [`${OneVmsEventKey.TOTAL_LOSS_REVOKE}_orders_fail_noti`]:
        'Unfortunately the revoke of total losses could not be completed. Check below for details.',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_failed_header`]: 'Quota deallocation request failed',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_failed`]:
        'Unfortunately we could not complete the deallocation request.',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_successful_header`]: 'Quota Deallocation Request accepted',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_successful`]:
        'The quota deallocation request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_modal`]: 'Deallocate Quota for Order',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_orders_modal`]: 'Deallocate Quota for Orders',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_yes`]: 'Deallocate Quota',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_order_finish`]: 'Return to Order List',
      [`${OneVmsEventKey.DEALLOCATE_QUOTA}_info`]:
        'After deallocation of the quota, the order will be automatically cancelled after 6 months for customer vehicles and after 1 month for non-customer related order types',

      [`${OneVmsEventKey.CONVERT_PI}_order_failed_header`]: 'Conversion of Purchase Intention request failed',
      [`${OneVmsEventKey.CONVERT_PI}_order_failed`]:
        'Unfortunately we could not complete the conversion request: "{{message}}"',
      [`${OneVmsEventKey.CONVERT_PI}_order_successful_header`]: 'Conversion Of Purchase Intention Request Accepted',
      [`${OneVmsEventKey.CONVERT_PI}_order_successful`]:
        'The conversion of the Purchase Intention request was dispatched and the transaction is now in progress. The status and result will be visible in the notification center.',
      [`${OneVmsEventKey.CONVERT_PI}_order_modal`]: 'Convert Purchase Intention into New Car Order',
      [`${OneVmsEventKey.CONVERT_PI}_order_yes`]: 'Convert',
      [`${OneVmsEventKey.CONVERT_PI}_order_finish`]: 'Return to List of Purchase Intentions',

      buy_sell_transfer_header: 'Buy Sell Transfer',
      buy_sell_transfer_fail_head: '{{successes}}/{{total}} orders were transferred',
      buy_sell_transfer_fail_noti: 'Unfortunately, we could not complete the transfer request.',
      buy_sell_transfer_success: 'Successfully Transferred Orders',
      buy_sell_transfer_success_header: '{{successes}}/{{total}} Successfully Transferred Orders',
      buy_sell_transfer_importer_number: 'Importer Number',
      buy_sell_transfer_importer_name: 'Importer Name',
      buy_sell_transfer_importer: 'Importer',
      buy_sell_transfer_source_dealer_number: 'Source Dealer Number',
      buy_sell_transfer_source_dealer_name: 'Source Dealer Name',
      buy_sell_transfer_source_dealer: 'Source Dealer',
      buy_sell_transfer_target_dealer_number: 'Target Dealer Number',
      buy_sell_transfer_target_dealer_name: 'Target Dealer Name',
      buy_sell_transfer_target_dealer: 'Target Dealer',
      buy_sell_transfer_source_total_orders: 'Total Number Of Orders that will be transferred',
      buy_sell_transfer_next: 'Next',
      buy_sell_transfer_confirm: 'Confirm Buy Sell Transfer',
      buy_sell_transfer_confirm_message:
        'Are you sure you want to transfer {{amount}} orders to dealer {{dealerNumber}} - {{dealerName}}?',
      buy_sell_transfer_finish_main: 'Return to Main Page',
      high_level_actions_header: 'High-level Action Navigation Space',
      high_level_actions_description:
        'The High-level Action Navigation Space offers further order actions which cannot be found within the order lists.',
      no_access: 'Your user is not permitted to perform any actions within this page.',
    },
  },
};

i18next
  .use(initReactI18next)
  .use(LanguageDetector)
  .init({
    resources,
    interpolation: {
      escapeValue: false,
    },
  })
  .catch(console.error);

export default i18next;
