{"channel": "KASCC_NOTIFICATION", "type": "CONFIG_SAVED", "version": "v1", "payload": {"Id": "APP-1104", "Cnr": "C22", "Importernr": "2050000", "Dealernr": null, "Importerid": "BL", "PpnUserId": "", "BpId": "", "IsDealerConfig": "", "LeadId": "", "ProspectKit": false, "Modeltype": "95BAN1", "Modelyear": "R", "ConfigName": "", "CofferId": "", "TeqId": "", "PorscheCode": "", "TotalPriceGross": "65206.67", "Taxes": "13693.40", "Currency": "EUR", "ConfigModified": "1", "CommentsModified": "0", "ExteriorColor": "0Q", "TopColor": "0Q", "InteriorColor": "WT", "FreeZOffer": "", "VehicleOption": {"Id": "1", "OptionIndividual": {"results": [{"Id": "1T3", "ContentOf": "", "UpgradeTo": "", "DealerComment": "", "ImporterComment": "", "PagComment": "", "SortOrder": "", "Type": ""}]}, "OptionExclusive": {"results": []}, "OptionCustomTailoring": {"results": []}, "OptionLocal": {"results": []}, "OptionZoffer": {"results": []}, "OptionPackage": {"results": []}}, "Vguid": "", "VehicleWLTPHeader": {"results": [{"uuid": "b6b6c4da-c0e0-4e8c-9ab1-5470eac996c4", "response_date": "\\/Date(1716469799244)\\/", "return_code": "200", "error_message": "OK", "check_date": "\\/Date(1716422400000)\\/", "DataSource": "P"}]}, "VehicleWLTPBody": {"results": [{"Typification": "WLTP_EU", "EngineType": "ICE", "DataSource": "P", "WLTPBodyWLTPBodyRecord": {"results": [{"DataType": "GENERAL_DATA", "DataSource": "P", "WLTPBodyRecordWLTPDataRecord": {"results": [{"ValueType": "", "FuelType": "", "EnergyManagementType": "", "DataSource": "P", "WLTPDataRecordWLTPValue": {"results": [{"Key": "TIRE_ROLLING_RESISTANCE_TOTAL", "Value": "8.4", "ValueFrom": "", "ValueTo": "", "Unit": "0/00", "DataSource": "P"}, {"Key": "TIRE_ROLLING_RESISTANCE_FRONT", "Value": "8.4", "ValueFrom": "", "ValueTo": "", "Unit": "0/00", "DataSource": "P"}, {"Key": "TIRE_ROLLING_RESISTANCE_REAR", "Value": "8.4", "ValueFrom": "", "ValueTo": "", "Unit": "0/00", "DataSource": "P"}, {"Key": "DRAG_COEFFICIENT", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "", "DataSource": "P"}, {"Key": "DRAG_COEFFICIENT_H", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "", "DataSource": "P"}, {"Key": "AERODYNAMIC_DRAG", "Value": "0.955", "ValueFrom": "", "ValueTo": "", "Unit": "m2", "DataSource": "P"}, {"Key": "AERODYNAMIC_DRAG_H", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "m2", "DataSource": "P"}, {"Key": "FRONT_SURFACE_A", "Value": "2.63", "ValueFrom": "", "ValueTo": "", "Unit": "m2", "DataSource": "P"}, {"Key": "FRONT_SURFACE_A_H", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "m2", "DataSource": "P"}, {"Key": "MASS_VEHICLE", "Value": "1867", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "MASS_VEHICLE_FRONT", "Value": "1037", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "MASS_VEHICLE_REAR", "Value": "830", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "MASS_ACTUAL", "Value": "1942", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "EU_LEER_MIN", "Value": "1940.0", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "EU_LEER_MAX", "Value": "2160.0", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "TEST_MASS_VEHICLE", "Value": "2016", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "TEST_MASS_VEHICLE_H", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "kg", "DataSource": "P"}, {"Key": "HIGH_TYPING", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "", "DataSource": "P"}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F0", "Value": "250.5", "ValueFrom": "", "ValueTo": "", "Unit": "N", "DataSource": "P"}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F0_H", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "N", "DataSource": "P"}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F1", "Value": "0.286", "ValueFrom": "", "ValueTo": "", "Unit": "N/(km/h)", "DataSource": "P"}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F1_H", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "N/(km/h)", "DataSource": "P"}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F2", "Value": "0.04505", "ValueFrom": "", "ValueTo": "", "Unit": "N/(km/h)2", "DataSource": "P"}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F2_H", "Value": "", "ValueFrom": "", "ValueTo": "", "Unit": "N/(km/h)2", "DataSource": "P"}]}}]}}, {"DataType": "INTERPOLATIONS", "DataSource": "P", "WLTPBodyRecordWLTPDataRecord": {"results": [{"ValueType": "CO2", "FuelType": "PETROL_E10", "EnergyManagementType": "PURE", "DataSource": "P", "WLTPDataRecordWLTPValue": {"results": [{"Key": "LOW", "Value": "296", "ValueFrom": "", "ValueTo": "", "Unit": "g/km", "DataSource": "P"}, {"Key": "MEDIUM", "Value": "223", "ValueFrom": "", "ValueTo": "", "Unit": "g/km", "DataSource": "P"}, {"Key": "HIGH", "Value": "199", "ValueFrom": "", "ValueTo": "", "Unit": "g/km", "DataSource": "P"}, {"Key": "EXTRA_HIGH", "Value": "234", "ValueFrom": "", "ValueTo": "", "Unit": "g/km", "DataSource": "P"}, {"Key": "COMBINED", "Value": "229", "ValueFrom": "", "ValueTo": "", "Unit": "g/km", "DataSource": "P"}]}}, {"ValueType": "CONSUMPTION", "FuelType": "PETROL_E10", "EnergyManagementType": "PURE", "DataSource": "P", "WLTPDataRecordWLTPValue": {"results": [{"Key": "LOW", "Value": "13.1", "ValueFrom": "", "ValueTo": "", "Unit": "l/100km", "DataSource": "P"}, {"Key": "MEDIUM", "Value": "9.8", "ValueFrom": "", "ValueTo": "", "Unit": "l/100km", "DataSource": "P"}, {"Key": "HIGH", "Value": "8.8", "ValueFrom": "", "ValueTo": "", "Unit": "l/100km", "DataSource": "P"}, {"Key": "EXTRA_HIGH", "Value": "10.3", "ValueFrom": "", "ValueTo": "", "Unit": "l/100km", "DataSource": "P"}, {"Key": "COMBINED", "Value": "10.1", "ValueFrom": "", "ValueTo": "", "Unit": "l/100km", "DataSource": "P"}]}}]}}, {"DataType": "IP_FAMILY", "DataSource": "P", "WLTPBodyRecordWLTPDataRecord": {"results": [{"ValueType": "", "FuelType": "", "EnergyManagementType": "", "DataSource": "P", "WLTPDataRecordWLTPValue": {"results": [{"Key": "NAME", "Value": "IP-AP95B034B00AT0S-WP1-1", "ValueFrom": "", "ValueTo": "", "Unit": "0/00", "DataSource": "P"}]}}]}}]}}]}, "VehicleTag": {"results": [{"Key": "KB", "Value": "PCCDP"}]}, "XTraceId": "e559edb1-a4d3-4054-a363-2dbda10c11bb"}, "status": {"code": 200, "message": "OK", "errors": null}}