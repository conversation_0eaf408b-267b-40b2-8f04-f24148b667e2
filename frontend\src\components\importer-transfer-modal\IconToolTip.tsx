import { PIcon } from '@porsche-design-system/components-react';
import { useState } from 'react';

interface IconTooltipProps {
  state: string;
  message: string;
  disabled?: boolean;
  onClick?: () => void;
  hoverable: boolean;
}

const IconTooltip: React.FC<IconTooltipProps> = ({ state, message, disabled = false, onClick, hoverable }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      <PIcon
        name={state === 'success' ? 'success' : 'exclamation'}
        size="x-small"
        color={state === 'success' ? 'notification-success' : 'notification-error'}
        style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
        onClick={!disabled && onClick ? onClick : undefined}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      />

      {hoverable && hovered && (
        <span
          style={{
            backgroundColor: state === 'success' ? '#197e10' : '#cc1922',
            color: '#fff',
            textAlign: 'center',
            borderRadius: '6px',
            padding: '5px 10px',
            position: 'absolute',
            zIndex: 1,
            bottom: '125%',
            left: '50%',
            transform: 'translateX(-50%)',
            opacity: 1,
            transition: 'opacity 0.3s',
            whiteSpace: 'nowrap',
          }}
        >
          {message}
          <span
            style={{
              position: 'absolute',
              top: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              borderWidth: '5px',
              borderStyle: 'solid',
              borderColor:
                state === 'success'
                  ? '#197e10 transparent transparent transparent'
                  : '#cc1922 transparent transparent transparent',
            }}
          ></span>
        </span>
      )}
    </div>
  );
};

export default IconTooltip;
