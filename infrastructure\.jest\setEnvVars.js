process.env.TABLE_NAME_ORG_RELS = 'TABLE_NAME_ORG_RELS';
process.env.TABLE_NAME_ORDER_TYPES = 'TABLE_NAME_ORDER_TYPES';
process.env.TABLE_NAME_SHIPPING_CODES = 'TABLE_NAME_SHIPPING_CODES';
process.env.TABLE_NAME_PORT_CODES = 'TABLE_NAME_PORT_CODES';
process.env.TABLE_NAME_IMPORTER = 'TABLE_NAME_IMPORTER';
process.env.TABLE_NAME_DEALER = 'TABLE_NAME_DEALER';
process.env.TABLE_NAME_NCO = 'TABLE_NAME_NCO';
process.env.TABLE_NAME_NCOID = 'TABLE_NAME_NCOID';
process.env.TABLE_NAME_SC = 'TABLE_NAME_SC';
process.env.TABLE_NAME_OT = 'TABLE_NAME_OT';
process.env.TABLE_NAME_IMP = 'TABLE_NAME_IMP';
process.env.TABLE_NAME_DLR = 'TABLE_NAME_DLR';
process.env.TABLE_NAME_MTV = 'TABLE_NAME_MTV';
process.env.TABLE_NAME_COR = 'TABLE_NAME_COR';
process.env.TABLE_NAME_QUOTA = 'TABLE_NAME_QUOTA';
process.env.TABLE_NAME_STATUS_MAPPING = 'TABLE_NAME_STATUS_MAPPING'
process.env.PURCHASE_INTENTION_PK = 'something';
process.env.AURORA_SECRET_ARN = 'aurorasecretarn'
process.env.APPLICATION_NAME_TO_AUTHORIZE = 'cora';
process.env.AWS_PROFILE = '274282531523_PA_DEVELOPER';
process.env.AWS_REGION = 'eu-west-1';
process.env.DYNAMODB_CORA_NEW_CAR_ORDER_TABLE_NAME = 'something';
process.env.TABLE_NAME_NEW_CAR_ORDER_LISTS = 'new-car-orders-for-lists'; //value is relevant!
process.env.TABLE_NAME_PURCHASE_INTENTIONS_LIST = 'pvms-purchase-intentions-for-lists'; //value is relevant!
process.env.CORA_ORG_PERMISSON_CHECK_PARAMS_TABLE_NAME = 'cora_org_relation';
process.env.TABLE_NAME_LISTS = 'cora_new_car_order_list';
process.env.TABLE_NAME_CORA_PURCHASE_INTENTIONS = 'TABLE_NAME_CORA_PURCHASE_INTENTIONS';
process.env.TABLE_NAME_MODEL_TYPE_VISIBILITY = 'mtvTableName';
process.env.STATUS_MAPPING_TABLE_NAME = 'statusMappingTableName';
process.env.QUOTA_API_SECRET_ARN = 'quotasecretarn';
process.env.ALLOW_QUOTA_API_MOCK = 'true';
process.env.TABLE_NAME_QUOTA = 'TABLE_NAME_QUOTA';
process.env.QUOTA_KAFKA_TOPIC = 'quota-topic-dummy';
process.env.QUOTA_TABLE_NAME = 'something';
process.env.PK_NCO = 'pk_new_car_order_id';
process.env.TABLE_NAME_STATUS_MAPPINGS = 'statusses_of_statussi_for_states_in_stadiums';
process.env.TABLE_NAME_FAILED_STATUS = 'failed-status-mappings';
process.env.PRIMARY_KEY_NAME = 'something';
process.env.KAFKA_SECRET_ARN = 'something';
process.env.KAFKA_BROKERS = '[]';
process.env.SORT_KEY_NAME = 'something';
process.env.STAGE = 'dev';
process.env.KAFKA_TOPIC_ONE_VMS = 'KAFKA_TOPIC_ONE_VMS'
process.env.KAFKA_TOPIC_PVMS = 'KAFKA_TOPIC_PVMS'
process.env.KAFKA_TOPIC_P06 = 'KAFKA_TOPIC_P06'
process.env.KAFKA_TOPIC_NOTIFICATION = 'KAFKA_TOPIC_NOTIFICATION';
process.env.EXPORT_NCO_SQS_URL = 'EXPORT_NCO_SQS_URL'
process.env.NOTIFICATION_KAFKA_TOPIC = 'notification-topic-dummy'
process.env.TRANSACTIONS_TABLE_NAME = 'transactions-table-name'
process.env.SUBTRANSACTIONS_TABLE_NAME = 'subtransactions-table-name'
process.env.ENABLE_NCO_INVOICE_MAPPING = 'false';
process.env.DISPATCHER_QUEUE_URL = 'https://sqs.eu-west-1.amazonaws.com/123456789012/dispatcher-queue';
process.env.PI_IMPORT_QUEUE_URL = 'PI_IMPORT_QUEUE_URL';