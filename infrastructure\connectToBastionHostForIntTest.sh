#!/bin/bash
set -e

echo "Setting up SSM Port Forwarding for DB Connection"
export TARGET_INSTANCE_ID=$(aws ec2 describe-instances --query 'Reservations[].Instances[0].InstanceId' --output text --region eu-west-1)
export RDS_CLUSTER_WRITER=$(aws rds describe-db-cluster-endpoints --query 'DBClusterEndpoints[0].Endpoint' --output text  --region eu-west-1)
nohup aws ssm start-session --target "$TARGET_INSTANCE_ID" --document-name AWS-StartPortForwardingSessionToRemoteHost --parameters "{\"host\":[\"$RDS_CLUSTER_WRITER\"],\"portNumber\":[\"5432\"],\"localPortNumber\":[\"5432\"]}" --region eu-west-1 &
