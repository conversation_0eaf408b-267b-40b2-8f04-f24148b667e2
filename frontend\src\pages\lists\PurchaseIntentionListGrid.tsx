import { P<PERSON><PERSON>onPure, PH<PERSON>ing, P<PERSON>inkPure, PText } from '@porsche-design-system/components-react';
import '@porsche-design-system/components-react/ag-grid/theme.css';
import { ColDef, GridApi, GridReadyEvent, IServerSideDatasource, ITooltipParams } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import copy from 'copy-to-clipboard';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { FilterModelRequest } from '../../../../infrastructure/lib/types/new-car-order-types';
import { OneVmsEventKey } from '../../../../infrastructure/lib/types/process-steering-types';
import { CoraNCPurchaseIntentionRequestBody } from '../../../../infrastructure/lib/types/purchase-intention-types';
import { useAuthContext } from '../../app/AuthContext';
import { useLazyGetModelTypeTextsQuery } from '../../store/api/BossApi';
import { PurchaseIntentionInListTranslationItem } from '../../store/types';
import { matchMttForOrderOrPurchaseIntention } from '../../utils/utils';
import './etc/ag-grid-compact-mode.css';
import '@porsche-design-system/components-react/ag-grid/theme.css';
import { CoraNCPurchaseIntentionApiResponse } from '../../../../infrastructure/lib/types/purchase-intention-types';
import { useTheme } from '../../utils/useTheme';
import { useInIframe } from '../../utils/useInIframe';
import { getAllowedEventKeysWithoutNcos } from '../../utils/ncoActionHelper';
import { useGetInboundStatusMappingQuery } from '../../store/api/MasterdataApi';
import { AppDispatch } from '../../store/configureStore';
import { useDispatch } from 'react-redux';
import { fetchAgGridSsrmPurchaseIntentions } from '../../store/api/ServerSideDataSource';
import { displayNotification } from '../../store/slices/NotificationSlice';

const PurchaseIntentionListGrid = () => {
  const [gridApi, setGridApi] = useState<GridApi<CoraNCPurchaseIntentionApiResponse> | null>(null);
  const [gridKey, setGridKey] = useState<boolean>(false);
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [loadModelTypeTexts, { data: modelTypeTexts, isLoading: isModelTypeTextsLoading }] =
    useLazyGetModelTypeTextsQuery();
  const dispatch = useDispatch<AppDispatch>();
  const inboundMappings = useGetInboundStatusMappingQuery(undefined);

  const authContext = useAuthContext();
  const theme = useTheme();
  const isInIframe = useInIframe();
  const serverDataSource: IServerSideDatasource = useMemo(() => {
    return {
      async getRows(params) {
        try {
          const res = await fetchAgGridSsrmPurchaseIntentions({
            ...params.request,
          } as CoraNCPurchaseIntentionRequestBody);
          console.log('res:', res);
          if (!res) {
            params.fail();
            return;
          }
          params.success({ rowData: res.data, rowCount: res.rowCount });
          if (!res.data || res.data.length === 0 || res.rowCount === 0) {
            params.api.showNoRowsOverlay();
          } else {
            params.api.hideOverlay();
          }
        } catch (error) {
          if (error instanceof Error) {
            dispatch(
              displayNotification({
                title: `${t('api_error')}: ${t('pi_list')}`,
                msg: error.message,
                state: 'error',
              }),
            );
          } else {
            console.error(error);
          }
          params.fail();
        }
      },
    };
  }, []);
  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const refreshGridData = (purge_grid_cache: boolean = false) => {
    if (gridApi) {
      gridApi.refreshServerSide({ route: undefined, purge: purge_grid_cache });
    }
  };

  const convertPurchaseIntention = (purchaseIntention: CoraNCPurchaseIntentionApiResponse) => {
    navigate(`/orders/convert/${purchaseIntention.purchase_intention_id}`);
  };

  const MttCellRenderer = (params: any) => {
    const [modelText, setModelText] = useState<string | null>(null);
    const [isModelTextMatching, setIsModelTextMatching] = useState<boolean>(false);

    const loadAndMatchModelText = useCallback(async () => {
      setIsModelTextMatching(true);

      // Load ModelTypeTexts and match them
      if (!modelTypeTexts || (modelTypeTexts.length > 0 && modelTypeTexts[0].iso_language_code !== i18n.language)) {
        await loadModelTypeTexts(i18n.language);
      }
      const matchedText = matchMttForOrderOrPurchaseIntention(modelTypeTexts, params.data, t);
      setModelText(matchedText);
      setIsModelTextMatching(false);
    }, [loadModelTypeTexts, modelTypeTexts, i18n.language, params.data, t]);

    useEffect(() => {
      if (params.data && !isModelTypeTextsLoading) {
        loadAndMatchModelText();
      }
    }, [params.data, isModelTypeTextsLoading, loadAndMatchModelText]);

    useEffect(() => {
      if (!isModelTextMatching) {
        gridApi?.autoSizeColumns(['model_text']);
      }
    }, [modelText]);

    return (
      <div className={'ag-cell-mtt-renderer'}>
        {isModelTypeTextsLoading || isModelTextMatching ? (
          <PText size="small">{t('loading')}</PText>
        ) : (
          <PText size="small">{modelText}</PText>
        )}
      </div>
    );
  };

  const allowedEventKeys = getAllowedEventKeysWithoutNcos(inboundMappings.data ?? []);

  const columns = useMemo<ColDef<PurchaseIntentionInListTranslationItem>[]>(
    () => [
      {
        headerName: t('purchase_intention_id'),
        headerTooltip: t('purchase_intention_id'),
        field: 'purchase_intention_id',
        tooltipField: 'purchase_intention_id',
        pinned: 'left',
      },
      {
        headerName: t('business_partner_id'),
        headerTooltip: t('business_partner_id'),
        field: 'business_partner_id',
        tooltipField: 'business_partner_id',
      },
      {
        headerName: t('quota_month'),
        headerTooltip: t('quota_month'),
        field: 'quota_month',
        tooltipField: 'quota_month',
      },
      {
        headerName: t('model_year'),
        headerTooltip: t('model_year'),
        field: 'model_year',
        tooltipField: 'model_year',
      },
      {
        headerName: t('model_text'),
        colId: 'model_text',
        headerTooltip: t('model_text'),
        field: 'model_text',
        tooltipField: 'model_text',
        cellRenderer: MttCellRenderer,
        filter: false,
        sortable: false,
      },
      {
        headerName: t('model_type'),
        headerTooltip: t('model_type'),
        field: 'model_type',
        tooltipField: 'model_type',
      },
      {
        headerName: t('dealer_number'),
        headerTooltip: t('dealer_number'),
        field: 'dealer_number',
        tooltipField: 'dealer_number',
      },
      {
        headerName: t('dealer_name'),
        headerTooltip: t('dealer_name'),
        field: 'dealer_name',
        filter: false,
        sortable: false,
        tooltipValueGetter: (params: ITooltipParams<PurchaseIntentionInListTranslationItem, string>) =>
          `${params.data?.dealer_name ?? 'MissingName'}`,
      },
      {
        headerName: t('row_actions'),
        colId: 'actions',
        filter: false,
        sortable: false,
        minWidth: 200,
        pinned: 'right',
        cellRenderer: (params: any) => {
          const handleCopyClick = () => {
            const rowData = columns
              .filter((col) => {
                if (typeof col.field === 'string') {
                  return col.field.toLowerCase() !== 'row_actions';
                }
                return true;
              })
              .map((col) => col.field ?? '')
              .map((field) => params.data[field])
              .join(',');
            copy(rowData);
          };
          if (authContext.permissions.NCO_CREATE && allowedEventKeys.includes(OneVmsEventKey.CONVERT_PI)) {
            return (
              <div className={'ag-cell-renderer'}>
                <PButtonPure
                  data-e2e="convert_order"
                  theme={theme}
                  size={'small'}
                  icon="none"
                  underline={true}
                  onClick={() => convertPurchaseIntention(params.data)}
                >
                  {t('convert_to_order')}
                </PButtonPure>
                <PButtonPure theme={theme} size={'x-small'} icon="copy" hideLabel={true} onClick={handleCopyClick} />
              </div>
            );
          } else {
            return (
              <div>
                <PButtonPure theme={theme} size={'x-small'} icon="copy" hideLabel={true} onClick={handleCopyClick} />
              </div>
            );
          }
        },
      },
    ],
    [t, modelTypeTexts, authContext, allowedEventKeys],
  );

  return (
    <div>
      <div
        className={`${theme === 'light' ? 'ag-theme-pds' : 'ag-theme-pds-dark'} compact`}
        style={{ width: '100%', overflow: 'hidden', position: 'relative' }}
      >
        <PHeading size="medium" style={{ paddingBottom: '1vh', height: '3vh' }}>
          {t('purchase_intentions')}
          <PButtonPure
            data-e2e="refresh_list"
            theme={theme}
            size={'small'}
            icon="refresh"
            underline={false}
            style={{ marginLeft: '20px', verticalAlign: 'middle' }}
            onClick={() => refreshGridData()}
          ></PButtonPure>
        </PHeading>
        <div
          style={{
            height: isInIframe ? '88vh' : '78vh',
            overflow: 'hidden',
            flex: '1 1 0px',
            width: '100%',
          }}
        >
          <AgGridReact
            gridId="PurchaseIntentionGrid"
            key={gridKey ? 'somekey' : 'someotherkey'}
            rowModelType="serverSide"
            onGridReady={onGridReady}
            getRowId={(params) => params.data.purchase_intention_id}
            serverSideDatasource={serverDataSource}
            columnDefs={columns}
            animateRows
            defaultColDef={{
              filter: 'agTextColumnFilter',
              enableCellChangeFlash: true,
              filterParams: {
                defaultOption: 'contains',
                filterOptions: ['equals', 'notEqual', 'contains', 'notContains', 'startsWith', 'endsWith'],
                trimInput: true,
              },
              sortable: true,
              resizable: true,
              editable: false,
              floatingFilter: true,
              minWidth: 80,
              flex: 1,
              cellStyle: () => ({
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }),
              autoHeight: true,
              // Disable Export (Excel/CSV) full list of options: https://www.ag-grid.com/javascript-data-grid/context-menu/
              contextMenuItems: ['copy', 'copyWithHeaders'],
            }}
            floatingFiltersHeight={70}
            enableCellTextSelection={true}
            // SelectAll Checkbox removed with CSS, SSRM makes it effectivly unusable
            rowSelection={{ mode: 'multiRow', selectAll: undefined }}
            selectionColumnDef={{
              pinned: 'left',
            }}
            domLayout="normal"
            alwaysShowHorizontalScroll={false}
            suppressHorizontalScroll={false}
            autoSizeStrategy={{
              type: 'fitCellContents',
            }}
            // Pagination props
            pagination={true}
            paginationPageSize={100}
            paginationPageSizeSelector={[20, 100, 500]}
            localeText={{
              page: t('navigate_page'),
              to: t('aggrid.pagination.to'),
              of: t('aggrid.pagination.of'),
              more: t('aggrid.pagination.more'),
              noRowsToShow: t('no_rows_to_show'),
            }}
            suppressMultiSort={true}
          />
        </div>
      </div>
    </div>
  );
};
export default PurchaseIntentionListGrid;
