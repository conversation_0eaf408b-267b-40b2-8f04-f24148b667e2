import { Construct } from 'constructs';
import { Rest<PERSON>pi, LambdaIntegration, IAuthorizer } from 'aws-cdk-lib/aws-apigateway';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import {
  KasDynamodbTable,
  KasNodejsFunction,
  KasKmsKey,
  KasSecret,
  KasSqsQueue,
  KasStage,
} from '@kas-resources/constructs';
import { Constants } from '../utils/constants';
import * as iam from 'aws-cdk-lib/aws-iam';
import { RemovalPolicy, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';
import { ProcessSteeringEventApiConstruct } from './process-steering-event-api-construct';
import { OneVmsEventKey } from '../types/process-steering-types';

export interface PurchaseIntentionEndpointConstructProps {
  api: RestApi;
  authorizer: IAuth<PERSON>zer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  pvmsPurchaseIntentionTable: KasDynamodbTable;
  coraOrgRelTable: KasDynamodbTable;
  applicationNameToAuthorize: string;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  kafkaSecurityGroup: ec2.ISecurityGroup;
  auroraReaderSecret: KasSecret;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  typeormLayer: ILayerVersion;
  eventDispatcherInboundQueue: KasSqsQueue;
  kafkaParameters: {
    brokers: string[];
    ncoNotificationTopic: string;
    kafkaSecret: KasSecret;
  };
}

export class PurchaseIntentionEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: PurchaseIntentionEndpointConstructProps) {
    super(scope, id);

    const purchaseIntentionResource = props.api.root.addResource('purchase-intention');
    const purchaseIntentionIdPath = purchaseIntentionResource.addResource('{purchaseIntentionId}');
    const piConvertEndpoint = purchaseIntentionIdPath.addResource('convert');

    // GET /purchase-intention
    const getPurchaseIntention = new KasNodejsFunction(this, 'GetPurchaseIntention', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/purchase-intention/get-purchase-intention/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_COR: props.coraOrgRelTable.tableName,
        TABLE_NAME_PURCHASE_INTENTION: props.pvmsPurchaseIntentionTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        PURCHASE_INTENTION_PK: Constants.DYNAMODB_CORA_PURCHASE_INTENTIONS_TABLE_PARAMS.pk,
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Get a purchase intention',
      customManagedKey: props.logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-get-purchase-intention`,
      errHandlingLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup, props.vpcEndpointsSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [props.typeormLayer],
      stage: props.stage,
    });

    props.pvmsPurchaseIntentionTable.grantReadData(getPurchaseIntention);
    props.coraOrgRelTable.grantReadData(getPurchaseIntention);
    props.auroraReaderSecret.grantRead(getPurchaseIntention);

    getPurchaseIntention.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [
          `${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`,
        ],
      }),
    );

    const getPurchaseIntentionLambdaIntegration = new LambdaIntegration(getPurchaseIntention);
    purchaseIntentionIdPath.addMethod('GET', getPurchaseIntentionLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.purchaseIntentionId': true,
      },
    });

    // PATCH /purchase-intention/{purchaseIntentionId}/convert
    const convertPiApiConstruct = new ProcessSteeringEventApiConstruct(
      this,
      `${OneVmsEventKey.CONVERT_PI}EventApiConstruct`,
      {
        eventKey: OneVmsEventKey.CONVERT_PI,
        applicationNameToAuthorize: props.applicationNameToAuthorize,
        vpc: props.vpc,
        stage: props.stage,
        dispatcherQueue: props.eventDispatcherInboundQueue,
        logGroupKey: props.logGroupKey,
        logSubscriptionLambda: props.logSubscriptionLambda,
        kafkaSecurityGroup: props.kafkaSecurityGroup,
        kafkaParameters: props.kafkaParameters,
        commonEnvVars: {
          CORS_DOMAIN: props.corsDomain,
        },
      },
    );

    const convertPiLambdaIntegration = new LambdaIntegration(convertPiApiConstruct.eventApiHandler);
    piConvertEndpoint.addMethod('PATCH', convertPiLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.purchaseIntentionId': true,
      },
    });
  }
}
