import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand, QueryCommandOutput } from '@aws-sdk/lib-dynamodb';
import { Constants } from '../../../../lib/utils/constants';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { getPpnId, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { CoraNCPurchaseIntentionApiResponse } from '../../../../lib/types/purchase-intention-types';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { CoraPurchaseIntentionModel } from '../../../../lib/entities/purchase-intention-model';
import { secretCache } from '../../../utils/secret-cache';

const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);
// Initialize database client and table names
const dbClient = new DynamoDBClient({ region: process.env.AWS_REGION });
const cora_org_relationTableName = getEnvVarWithAssert('TABLE_NAME_COR');
const purchaseIntentionPk = getEnvVarWithAssert('PURCHASE_INTENTION_PK');
const dataSourcePromise = createTypeORMDataSource(
  new KasLambdaLogger('getPurchaseIntention'),
  AURORA_SECRET_ARN,
  'dev',
  [CoraPurchaseIntentionModel],
);

const getPurchaseIntentionFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const purchaseIntentionId = event.pathParameters ? event.pathParameters['purchaseIntentionId'] : undefined;
  logger.setObjectId(purchaseIntentionId);
  const regex = /^[A-Z0-9]{8,9}$/;
  if (!purchaseIntentionId || !purchaseIntentionPk || !regex.test(purchaseIntentionId.trim())) {
    const message = 'Could not get purchase intention, invalid new car order id (deal id)';
    logger.log(LogLevel.WARN, message, { data: purchaseIntentionId });
    return sendFail({ message: message, status: 400, reqHeaders: event.headers }, logger);
  }
  // // Check user's permissions
  const ppnId = getPpnId({ event: event }, logger);
  if (!ppnId) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: sanitizeApiGwEvent({ event: event }, logger) });
    return sendFail({ message: 'No PPN OrgId in request', status: 400, reqHeaders: event.headers }, logger);
  }

  const dataSource = await dataSourcePromise;
  const purchase_intention_repository = dataSource.getRepository(CoraPurchaseIntentionModel);
  let purchaseIntention: CoraPurchaseIntentionModel;
  try {
    const _purchaseIntention = await purchase_intention_repository.findOneBy({
      purchase_intention_id: purchaseIntentionId,
      is_converted: false,
    });
    if (_purchaseIntention === null) {
      return sendFail(
        { message: 'Could not find purchase intention or access was denied', status: 404, reqHeaders: event.headers },
        logger,
      );
    }
    purchaseIntention = _purchaseIntention;
  } catch (error) {
    return sendFail(
      {
        message: `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`,
        status: 500,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  const cora_org_relationOutput = await query(
    cora_org_relationTableName,
    Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
    Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk,
    ppnId,
    logger,
  );
  if (cora_org_relationOutput.Items) {
    const cora_org_relationItems = cora_org_relationOutput.Items as CoraOrgRelModel[];
    const dealerNrs = new Set<string>();
    cora_org_relationItems.forEach(
      (cora_org_relation) => cora_org_relation.dealer_number && dealerNrs.add(cora_org_relation.dealer_number),
    );

    if (dealerNrs.has(purchaseIntention.dealer_number)) {
      logger.log(LogLevel.INFO, `${ppnId} is allowed to view the purchase intention ${purchaseIntentionId}`);

      const purchaseIntention_response: CoraNCPurchaseIntentionApiResponse = {
        ...purchaseIntention,
        receiving_port_code: purchaseIntention.receiving_port_code,
        dealer_name:
          cora_org_relationItems.find(
            (cora_org_relation) => cora_org_relation.dealer_number === purchaseIntention.dealer_number,
          )?.display_name ?? undefined,
      };

      return sendSuccess({ body: purchaseIntention_response, reqHeaders: event.headers }, logger);
    } else {
      logger.log(LogLevel.INFO, `${ppnId} isn't allowed to view the purchase intention ${purchaseIntentionId}`);
      return sendFail(
        { message: 'Could not find purchase intention or access was denied', status: 404, reqHeaders: event.headers },
        logger,
      );
    }
  } else {
    logger.log(
      LogLevel.ERROR,
      `No match by querying table ${cora_org_relationTableName} with parameters: key=${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk}; value=${ppnId}`,
    );
    return sendFail(
      { message: 'Could not find purchase intention or access was denied', status: 404, reqHeaders: event.headers },
      logger,
    );
  }
};

// Helper function to perform a DynamoDB query
async function query(
  tableName: string,
  indexName: string | undefined,
  key: string,
  value: string,
  logger: KasLambdaLogger,
): Promise<QueryCommandOutput> {
  const cmd = new QueryCommand({
    TableName: tableName,
    IndexName: indexName,
    KeyConditionExpression: `${key} = :value`,
    ExpressionAttributeValues: { ':value': value },
  });
  logger.log(LogLevel.TRACE, 'getPurchaseIntention query', { data: cmd });
  try {
    return await dbClient.send(cmd);
  } catch (error) {
    logger.log(LogLevel.ERROR, `Failed to query table ${tableName} with parameters: key=${key}; value=${value}`, {
      data: { error: error, cmd: cmd },
    });
    throw error;
  }
}

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-purchase-intention', LogLevel.TRACE)(event, context, getPurchaseIntentionFunc);
