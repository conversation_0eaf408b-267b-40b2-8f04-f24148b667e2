import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PIcon, PInlineNotification } from '@porsche-design-system/components-react';
import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/dist/query';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  OrderActionResult,
  OrderActionStatusCodes,
} from '../../../../../infrastructure/lambda/backend/new-car-order/shared-order-action-status-result/types';
import { ModelTypeTextModel } from '../../../../../infrastructure/lib/entities/model-type-text-model';
import { OneVmsEventKey } from '../../../../../infrastructure/lib/types/process-steering-types';
import { CarOrderInList, OrderActionType } from '../../../store/types';
import OrderActionMultiList from '../order-action-multi-lists/OrderActionMultiList';
import OrderActionSingleDetails from '../order-action-single-details/OrderActionSingleDetails';

export interface OrderActionResultStateProps {
  successfulOrders: CarOrderInList[];
  failedOrders: CarOrderInList[];
  isMulti: boolean;
  error?: FetchBaseQueryError | SerializedError;
  closeModal: () => void;
  modelTypeTexts?: ModelTypeTextModel[];
  actionType: OrderActionType | OneVmsEventKey;
  orderResult?: OrderActionResult;
  racingCondErrorOrders: CarOrderInList[];
}

interface SuccessHeaderProps {
  heading: string;
}

const SuccessHeader: React.FC<SuccessHeaderProps> = (props: SuccessHeaderProps) => {
  return (
    <div className="header-success-container">
      <PIcon size="medium" name="success-filled" color="notification-success" />
      <PHeading size="large">{props.heading}</PHeading>
    </div>
  );
};

interface FailureHeaderProps {
  notification: string;
  heading: string;
  dataE2E: string;
}

interface RacingConditionHeaderProps {
  notification: string;
  heading: string;
  dataE2E: string;
}

const FailureHeader: React.FC<FailureHeaderProps> = (props: FailureHeaderProps) => {
  return (
    <div className="header-error-container">
      <PInlineNotification data-e2e={props.dataE2E} state="error">
        {props.notification}
      </PInlineNotification>
      <PHeading size="large">{props.heading}</PHeading>
    </div>
  );
};

export const OrderActionResultStateComponent: React.FC<OrderActionResultStateProps> = (
  props: OrderActionResultStateProps,
) => {
  const { t } = useTranslation();

  const toPastTense = (action: string) => {
    if (action.endsWith('e')) {
      return `${action}d`;
    } else if (action.endsWith('l')) {
      return `${action}led`;
    }
    return `${action}ed`;
  };
  const pastTenseAction = toPastTense(props.actionType);

  const wasPartialFail =
    props.orderResult && Object.values(props.orderResult).some((value) => value !== OrderActionStatusCodes.SUCCESS);

  const singleFailNoti = () => {
    if (props.failedOrders && props.failedOrders.length === 1) {
      const status = props.orderResult ? (Object.values(props.orderResult)?.[0] ?? null) : null;
      return t(status || 'unknown_error');
    } else {
      return t(`${props.actionType}_order_fail_noti`);
    }
  };

  const multiFailNoti = () => {
    // @ts-ignore
    const isTimeoutError = (props.error && props.error?.status === 'TIMEOUT_ERROR') || false;
    return isTimeoutError
      ? t(`${props.actionType}_orders_fail_noti_timeout`)
      : t(`${props.actionType}_orders_fail_noti`);
  };

  const renderSuccess = () => {
    if (props.isMulti) {
      return (
        <>
          <SuccessHeader
            data-e2e={`${props.actionType}_success_header`}
            heading={t(`${props.actionType}_orders_success`)}
          />
          <OrderActionMultiList
            dataE2e={`${props.actionType}_multi_details`}
            orders={props.successfulOrders}
            heading={t(`${pastTenseAction}_orders`)}
          />
        </>
      );
    }

    return (
      <>
        <SuccessHeader
          data-e2e={`${props.actionType}_success_header`}
          heading={t(`${props.actionType}_order_success`)}
        />
        <OrderActionSingleDetails order={props.successfulOrders[0]} modelTypeTexts={props.modelTypeTexts} />
      </>
    );
  };
  const renderError = () => {
    if (props.isMulti) {
      return (
        <>
          <FailureHeader
            dataE2E={'noti_error_head_multi'}
            notification={multiFailNoti()}
            heading={t(`${props.actionType}_orders_fail_head`, {
              successes: props.successfulOrders.length,
              total: props.successfulOrders.length + props.failedOrders.length + props.racingCondErrorOrders.length,
            })}
          />
          <OrderActionMultiList
            dataE2e={`${props.actionType}_multi_details`}
            orders={props.successfulOrders}
            heading={t(`${pastTenseAction}_orders`)}
          />
          {props.failedOrders.length > 0 && (
            <OrderActionMultiList
              dataE2e={`${props.actionType}_multi_errors`}
              orders={props.failedOrders}
              heading={t(`failed_${pastTenseAction}_orders`)}
              error={t(`failed_${pastTenseAction}_orders_error`)}
            />
          )}
          {props.racingCondErrorOrders.length > 0 && (
            <OrderActionMultiList
              dataE2e={`${props.actionType}_multi_racing_errors`}
              orders={props.racingCondErrorOrders}
              heading={t(`${pastTenseAction}_racing_cond_orders`)}
              error={t(`${pastTenseAction}_racing_cond_orders_error`)}
            />
          )}
        </>
      );
    }

    return (
      <>
        <FailureHeader
          dataE2E={'noti_error_head_single'}
          notification={singleFailNoti()}
          heading={t(`${props.actionType}_order_fail_head`)}
        />
        {props.failedOrders.length > 0 && (
          <OrderActionSingleDetails order={props.failedOrders[0]} modelTypeTexts={props.modelTypeTexts} />
        )}
        {props.racingCondErrorOrders.length > 0 && (
          <OrderActionSingleDetails order={props.racingCondErrorOrders[0]} modelTypeTexts={props.modelTypeTexts} />
        )}
      </>
    );
  };

  return (
    <>
      {!wasPartialFail && props.orderResult !== undefined ? renderSuccess() : renderError()}
      <PButton data-e2e="close" variant="primary" onClick={props.closeModal}>
        {t('cancel_order_finish')}
      </PButton>
    </>
  );
};
