import * as route53 from 'aws-cdk-lib/aws-route53';
import { Construct } from 'constructs';
import { Constants } from '../utils/constants';
import {
  ExportLogsToCompBucketConstruct,
  KasDefaultLogRetentions,
  KasDynamodbTable,
  KasKmsKey,
  KasLogBasedAlertingConstruct,
  KasSecret,
  KasSnsTopic,
  KasSqsQueue,
  KasStage,
  KasVpc,
} from '@kas-resources/constructs';
import {
  aws_dynamodb as dynamodb,
  aws_iam as iam,
  aws_ssm,
  aws_ec2 as ec2,
  aws_logs as logs,
  RemovalPolicy,
  Stack,
  Tags,
  Duration,
} from 'aws-cdk-lib';
import { AttributeType } from 'aws-cdk-lib/aws-dynamodb';
import { EmailSubscription } from 'aws-cdk-lib/aws-sns-subscriptions';
import { CoraStackProps } from '../types_cdk/cdk-types';
import { CfnSubscription } from 'aws-cdk-lib/aws-sns';
import { Code, Function, IFunction, LayerVersion } from 'aws-cdk-lib/aws-lambda';
import { CfnDocument, StringParameter } from 'aws-cdk-lib/aws-ssm';
import { EndpointConfig } from '@kas-resources/constructs/src/lib/kas-vpc';
import path from 'path';
import { ConstantsCdk } from '../utils/constants_cdk';

export class GlobalStack extends Stack {
  public readonly vpc: KasVpc;
  public constructor(scope: Construct, id: string, props: CoraStackProps) {
    super(scope, id, props);

    const hostedZone = route53.PublicHostedZone.fromLookup(this, 'HostedZone', {
      domainName: props.hostedZoneName,
    });
    const tags = Tags.of(hostedZone);
    tags.add('Owner', props.owner);
    tags.add('Mail', props.mail);
    tags.add('UseCase', props.useCase);

    if (props.stage.toLowerCase() === 'dev') {
      //create session manager key
      const sessionManagerKmsKeyId = Constants.buildKmsKeyId(
        props.stage,
        Constants.KMS_KEY_GLOBAL_SESSION_MANAGER_NAME,
      );
      const sessionManagerKey = new KasKmsKey(this, sessionManagerKmsKeyId);
      sessionManagerKey.grantEncryptDecrypt(new iam.ServicePrincipal('ssm.amazonaws.com'));

      //create session manager document to make it compliant with BB/Prisma
      new CfnDocument(this, 'BossSmConfigDocument', {
        content: {
          schemaVersion: '1.0',
          description: 'BB/Prisma compliant session manager config',
          sessionType: 'Standard_Stream',
          inputs: {
            s3BucketName: `porsche-compliance-log-${props.env.account}-${props.env.region}`,
            s3KeyPrefix: 'session_manager',
            s3EncryptionEnabled: true,
            cloudWatchLogGroupName: '',
            cloudWatchEncryptionEnabled: true,
            cloudWatchStreamingEnabled: false,
            kmsKeyId: sessionManagerKey.keyId,
            runAsEnabled: true,
            runAsDefaultUser: '',
            idleSessionTimeout: '30',
            maxSessionDuration: '720',
            shellProfile: {
              windows: 'date',
              linux: 'pwd;ls',
            },
          },
        },
        documentFormat: 'JSON',
        documentType: 'Session',
        name: 'SSM-SessionManagerRunShell',
        updateMethod: 'NewVersion', //cannot use default 'Replace', because of CF Update err
      });

      //export global session manager key arn
      new aws_ssm.StringParameter(this, 'GlobalSessionManagerKeyArnParameter', {
        parameterName: props.globalParameterNames.globalSessionManagerKmsKeyArnPname,
        stringValue: sessionManagerKey.keyArn,
      });
    }

    // Create secrets for the IDP client, kafka certs, configuration and  quota API with a dummy value
    // Replace the value with the real one after the stack is deployed
    const globalSecretKmsKeyId = Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SECRET_NAME);
    const globalSecretKmsKey = new KasKmsKey(this, globalSecretKmsKeyId);

    const kafkaSecret = new KasSecret(this, Constants.KAFKA_SECRET_NAME, {
      secretName: Constants.KAFKA_SECRET_NAME,
      encryptionKey: globalSecretKmsKey,
      description: 'Cora OC Kafka Secret',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({ certificate: '', privateKey: '' }),
        generateStringKey: 'privateKey',
      },
    });

    const quotaApiSecret = new KasSecret(this, 'CoraQuotaApiSecret', {
      secretName: 'CoraQuotaApiSecret',
      encryptionKey: globalSecretKmsKey,
      description: 'Cora Quota API Secret',
    });

    const omConfigApiSecret = new KasSecret(this, 'CoraConfigApiSecret', {
      secretName: 'CoraConfigApiSecret',
      encryptionKey: globalSecretKmsKey,
      description: 'Cora Order Management Configuration API Secret',
    });

    const logGroupKmsKeyId = Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_LOG_NAME);
    const logGroupKey = new KasKmsKey(this, logGroupKmsKeyId);
    logGroupKey.grantEncryptDecrypt(new iam.ServicePrincipal('logs.amazonaws.com'));

    // Create vpc
    const endpointConfig: EndpointConfig = {
      endpoints: [
        {
          name: 'SecretsManagerEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.SECRETS_MANAGER,
          actions: [
            'secretsmanager:DescribeSecret',
            'secretsmanager:GetSecretValue',
            'secretsmanager:GetRandomPassword',
            'secretsmanager:PutSecretValue',
            'secretsmanager:UpdateSecretVersionStage',
          ],
        },
        {
          name: 'KmsEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.KMS,
          actions: [
            'kms:GenerateDataKey*',
            'kms:Decrypt',
            'kms:Describe*',
            'kms:List*',
            'kms:Get*',
            'kms:Encrypt',
            'kms:ReEncrypt*',
          ],
        },
        {
          name: 'CloudwatchEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.CLOUDWATCH_LOGS,
          actions: [
            'logs:CreateLogGroup',
            'logs:CreateLogStream',
            'logs:PutLogEvents',
            'logs:DescribeLogGroups',
            'logs:PutRetentionPolicy',
            'logs:DescribeLogStreams',
            'logs:DeleteLogGroup',
          ],
        },
        {
          name: 'EventBridgeEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.EVENTBRIDGE,
          actions: [
            'events:PutRule',
            'events:PutTargets',
            'events:DescribeRule',
            'events:RemoveTargets',
            'events:DeleteRule',
          ],
        },
        {
          name: 'SnsEndpointServices',
          service: ec2.InterfaceVpcEndpointAwsService.SNS,
          actions: ['sns:Publish', 'sns:Subscribe', 'sns:Unsubscribe', 'sns:Receive'],
        },
        {
          name: 'SqsEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.SQS,
          actions: [
            'sqs:ChangeMessageVisibility',
            'sqs:DeleteMessage',
            'sqs:GetQueueAttributes',
            'sqs:GetQueueUrl',
            'sqs:ReceiveMessage',
            'sqs:SendMessage',
          ],
        },
        {
          name: 'SsmEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.SSM,
          actions: ['ssm:UpdateInstanceInformation', 'ssm:ListInstanceAssociations', 'ssm:ListAssociations'],
        },
        {
          name: 'SsmMessagesEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.SSM_MESSAGES,
          actions: [
            'ssmmessages:CreateControlChannel',
            'ssmmessages:CreateDataChannel',
            'ssmmessages:OpenControlChannel',
            'ssmmessages:OpenDataChannel',
          ],
        },
        {
          name: 'RdsEndpoint',
          service: ec2.InterfaceVpcEndpointAwsService.RDS,
          actions: ['rds:CreateDBInstance', 'rds:ModifyDBInstance', 'rds:CreateDBSnapshot'],
        },
      ],
    };

    const vpc = new KasVpc(this, id, {
      cidrIp: Constants.VPC_CIDR,
      vpcName: Constants.VPC_NAME,
      vpcEndpoints: endpointConfig,
      createIsolated: true,
      subnetConfiguration: [
        {
          name: Constants.PRIVATE_SUBNET_GROUPNAME_AUTH,
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
          cidrMask: 24,
        },
        {
          name: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES,
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
          cidrMask: 24,
        },
        {
          name: Constants.ISOLATED_SUBNET_GROUPNAME,
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
          cidrMask: 24,
        },
        {
          name: Constants.PUBLIC_SUBNET_GROUPNAME,
          subnetType: ec2.SubnetType.PUBLIC,
          cidrMask: 24,
          mapPublicIpOnLaunch: false,
        },
        {
          name: Constants.PRIVATE_SUBNET_GROUPNAME_MD_SERVICES,
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
          cidrMask: 24,
        },
        {
          name: Constants.PRIVATE_SUBNET_GROUPNAME_RESERVED,
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
          cidrMask: 24,
          reserved: true,
        },
        {
          name: Constants.PRIVATE_SUBNET_GROUPNAME_RESERVED,
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
          cidrMask: 24,
          reserved: true,
        },
      ],
    });
    this.vpc = vpc;

    //add custom LogGroup as destination for VPC Flowlogs in order to use CMK
    const flowLogName = props.stage + 'VPCFlowLogs';
    const vpcFlowLogGroup = new logs.LogGroup(this, 'CoraVPCFlowLogs' + 'Lg', {
      encryptionKey: logGroupKey,
      logGroupName: flowLogName,
      //overwrite default for dev and int because big data be expensive
      retention: props.stage !== 'prod' ? logs.RetentionDays.ONE_WEEK : KasDefaultLogRetentions['prod'],
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });
    vpc.addFlowLog(flowLogName, {
      destination: ec2.FlowLogDestination.toCloudWatchLogs(vpcFlowLogGroup),
    });

    //Define all necessary security groups
    const auroraSecurityGroup = new ec2.SecurityGroup(this, 'AuroraSecurityGroup', {
      vpc: vpc,
      securityGroupName: 'aurora-security-group',
      description: 'Security Group for the Aurora Database',
      allowAllOutbound: false,
    });

    // Create Security Group to define vpc endpoints
    const vpcEndpointsSecurityGroup = new ec2.SecurityGroup(this, 'VpcEndpointsSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: false,
      securityGroupName: 'VpcEndpointsSecurityGroup',
    });
    vpcEndpointsSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(Constants.VPC_CIDR),
      ec2.Port.tcp(443),
      'allow access to internal VPC resources',
    );
    vpcEndpointsSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6fa54006'),
      ec2.Port.tcp(443),
      'allow access to DynamoDB',
    );
    vpcEndpointsSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6da54004'),
      ec2.Port.tcp(443),
      'allow access to S3',
    );

    // Create Security Group to allow external access
    const externalAccessSecurityGroup = new ec2.SecurityGroup(this, 'ExternalAccessSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: false,
      securityGroupName: 'ExternalAccessSecurityGroup',
    });
    externalAccessSecurityGroup.addEgressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(443), 'allow to external https');
    externalAccessSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(Constants.VPC_CIDR),
      ec2.Port.tcp(443),
      'allow access to internal vpc resources',
    );
    externalAccessSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6fa54006'),
      ec2.Port.tcp(443),
      'allow access to dynamodb',
    );
    externalAccessSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6da54004'),
      ec2.Port.tcp(443),
      'allow access to s3',
    );

    // Create Security Group to allow access to aurora cluster
    const auroraAccessSecurityGroup = new ec2.SecurityGroup(this, 'AuroraAccessSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: false,
      securityGroupName: 'AuroraAccessSecurityGroup',
    });
    auroraAccessSecurityGroup.addEgressRule(
      ec2.Peer.securityGroupId(auroraSecurityGroup.securityGroupId),
      ec2.Port.tcp(Constants.AURORA_DB_PORT),
      'allow access to aurora cluster',
    );
    auroraAccessSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(Constants.VPC_CIDR),
      ec2.Port.tcp(443),
      'allow access to internal VPC resources',
    );
    auroraAccessSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6fa54006'),
      ec2.Port.tcp(443),
      'allow access to DynamoDB',
    );
    auroraAccessSecurityGroup.addEgressRule(
      ec2.Peer.prefixList('pl-6da54004'),
      ec2.Port.tcp(443),
      'allow access to S3',
    );

    // Create Security Group to allow access from/to kafka
    const kafkaSecurityGroup = new ec2.SecurityGroup(this, 'KafkaAccessSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: false,
      securityGroupName: 'KafkaAccessSecurityGroup',
    });
    kafkaSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(Constants.VPC_CIDR),
      ec2.Port.tcp(443),
      'allow access to internal vpc resources',
    );
    kafkaSecurityGroup.addEgressRule(ec2.Peer.prefixList('pl-6fa54006'), ec2.Port.tcp(443), 'allow access to dynamodb');
    kafkaSecurityGroup.addEgressRule(ec2.Peer.prefixList('pl-6da54004'), ec2.Port.tcp(443), 'allow access to s3');
    kafkaSecurityGroup.addEgressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(9092), 'allow access to kafka brokers');
    kafkaSecurityGroup.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(9092), 'allow inbound from kafka brokers');

    //create global dynamodb encryption key
    const globalDynamoKmsKeyId = Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_DYNAMO_NAME);
    const globalDynamoKmsKey = new KasKmsKey(this, globalDynamoKmsKeyId);

    // Create dynamoDB table for new car order id lookup table
    new KasDynamodbTable(this, 'NewCarOrderIdTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_NEW_CAR_ORDER_ID_TABLE_PARAMS.tableName),
      partitionKey: {
        name: Constants.DYNAMODB_NEW_CAR_ORDER_ID_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    // Create dynamoDB table for new car order
    const newCarOrderTable = new KasDynamodbTable(this, 'NewCarOrderTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_NEW_CAR_ORDER_TABLE_PARAMS.tableName),
      partitionKey: {
        name: Constants.DYNAMODB_NEW_CAR_ORDER_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
      encryptionKey: globalDynamoKmsKey,
    });
    newCarOrderTable.addGlobalSecondaryIndex({
      indexName: Constants.DYNAMODB_NEW_CAR_ORDER_TABLE_PARAMS.deal_id_Index,
      partitionKey: {
        name: Constants.DYNAMODB_NEW_CAR_ORDER_TABLE_PARAMS.deal_id_index_pk,
        type: AttributeType.STRING,
      },
    });

    new KasDynamodbTable(this, 'NewCarOrderAuditTable', {
      tableName: Constants.buildResourceName(
        props.stage,
        Constants.DYNAMODB_NEW_CAR_ORDER_AUDIT_TABLE_PARAMS.tableName,
      ),
      partitionKey: {
        name: Constants.DYNAMODB_NEW_CAR_ORDER_AUDIT_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'timestamp',
        type: dynamodb.AttributeType.NUMBER,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    // Create dynamoDB table for new car orders from pvms that are waiting for new Cora Config
    new KasDynamodbTable(this, 'NewCarOrderForConfigRequestsTable', {
      tableName: Constants.buildResourceName(
        props.stage,
        Constants.DYNAMODB_NEW_CAR_ORDER_FOR_CONFIG_REQUESTS_TABLE_PARAMS.tableName,
      ),
      partitionKey: {
        name: Constants.DYNAMODB_NEW_CAR_ORDER_FOR_CONFIG_REQUESTS_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    //Create a DynamoDB table to store the org imported from BOSS
    const bossOrgTable = new KasDynamodbTable(this, 'BossOrgTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_BOSS_ORG_TABLE_PARAMS.tableName),
      partitionKey: {
        name: Constants.DYNAMODB_BOSS_ORG_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
      encryptionKey: globalDynamoKmsKey,
    });

    //Create a DynamoDB table to store parent-child relationships derived from the BossOrgTable
    const coraOrgRelTable = new KasDynamodbTable(this, 'CoraOrgRelTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName),
      partitionKey: {
        name: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
      encryptionKey: globalDynamoKmsKey,
    });
    coraOrgRelTable.addGlobalSecondaryIndex({
      indexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
      partitionKey: {
        name: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk,
        type: AttributeType.STRING,
      },
    });

    // Create dynamoDB table for model type visibility
    // Not in use anymore, needs to stay as backup
    new KasDynamodbTable(this, 'ModelTypeVisibilityTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_MODEL_TYPE_VISIBILITY_TABLE_NAME),
      partitionKey: {
        name: 'importer_number_role',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'model_type_my4_cnr',
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    //table for quotas
    new KasDynamodbTable(this, 'QuotaTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_QUOTA_TABLE_NAME),
      partitionKey: {
        name: 'quota_id_without_month',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'quota_month',
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    //table for purchase intentions
    const purchaseIntentionsTable = new KasDynamodbTable(this, 'purchaseIntentionsTable', {
      tableName: Constants.buildResourceName(
        props.stage,
        Constants.DYNAMODB_CORA_PURCHASE_INTENTIONS_TABLE_PARAMS.tableName,
      ),
      partitionKey: {
        name: Constants.DYNAMODB_CORA_PURCHASE_INTENTIONS_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
      encryptionKey: globalDynamoKmsKey,
    });

    //table for failed status mapping data
    new KasDynamodbTable(this, 'FailedStatusMappingTable', {
      tableName: Constants.buildResourceName(
        props.stage,
        Constants.DYNAMODB_FAILED_STATUS_MAPPING_TABLE_PARAMS.tableName,
      ),
      partitionKey: {
        name: Constants.DYNAMODB_FAILED_STATUS_MAPPING_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    //create global kms key for sqs queues
    const globalSqsKmsKeyId = Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SQS_NAME);
    const globalSqsKmsKey = new KasKmsKey(this, globalSqsKmsKeyId);
    globalSqsKmsKey.grantEncryptDecrypt(new iam.ServicePrincipal('sqs.amazonaws.com'));

    //create sqs queue for nco audit trail data marked for export
    const glocalExportNcoQueue = new KasSqsQueue(this, 'GlobalExportNcoQueue', {
      visibilityTimeout: Duration.seconds(60),
      queueName: 'ExportNcoQueue',
      encryptionMasterKey: globalSqsKmsKey,
    });

    //create global sqs key
    const sqsKmsKeyId = Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SQS);
    const sqsKmsKey = new KasKmsKey(this, sqsKmsKeyId);
    sqsKmsKey.grantEncryptDecrypt(new iam.ServicePrincipal('sqs.amazonaws.com'));

    //create global s3 key
    const s3KmsKeyId = Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_S3);
    const s3KmsKey = new KasKmsKey(this, s3KmsKeyId);
    s3KmsKey.grantEncryptDecrypt(new iam.ServicePrincipal('s3.amazonaws.com'));

    //create global health monitoring topic that sends alarm emails on important errors
    const healthMonitoringTopicKmsKeyId = Constants.buildKmsKeyId(
      props.stage,
      Constants.KMS_KEY_GLOBAL_HEALTH_MONITORING_NAME,
    );
    const healthMonitoringTopicKms = new KasKmsKey(this, healthMonitoringTopicKmsKeyId);

    //give sns and cloudwatch alarms rights on the key
    healthMonitoringTopicKms.grantEncryptDecrypt(new iam.ServicePrincipal('sns.amazonaws.com'));
    healthMonitoringTopicKms.grantEncryptDecrypt(new iam.ServicePrincipal('cloudwatch.amazonaws.com'));

    const healthMonitoringTopic = new KasSnsTopic(this, 'CoraHealthMonitoringTopic', {
      displayName: 'CoraHealthMonitoringTopic',
      masterKey: healthMonitoringTopicKms,
    });

    //add policy for all alarms etc here, since importing and adding a policy in a different stack is a no-op
    healthMonitoringTopic.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        principals: [new iam.ServicePrincipal('cloudwatch.amazonaws.com')],
        actions: ['SNS:Publish'],
        resources: [healthMonitoringTopic.topicArn],
        conditions: {
          ArnLike: {
            'aws:SourceArn': `arn:aws:cloudwatch:${props.env.region}:${props.env.account}:alarm:${Constants.MONITORING_PVMS_ORDER_DATA_ALARM}`,
          },
          Bool: { 'aws:SecureTransport': 'true' },
        },
      }),
    );

    healthMonitoringTopic.addSubscription(new EmailSubscription(Constants.MONITORING_EMAIL));

    //setup subscription for aws waf managed rules
    new CfnSubscription(this, 'WafRuleSubscription', {
      protocol: 'EMAIL',
      topicArn: `arn:aws:sns:us-east-1:************:aws-managed-waf-rule-notifications`,
      region: 'us-east-1',
      endpoint: Constants.MONITORING_EMAIL,
    });

    const typeORMLayerArn = new LayerVersion(this, 'TypeORMLambdaLayer', {
      compatibleRuntimes: [ConstantsCdk.NODE_JS_VERSION],
      code: Code.fromAsset(path.join(__dirname, `/../../dist/typeorm-pg-layer/typeorm-pg-layer.zip`)),
      description: 'Provides typeorm as a lambda layer',
    });

    //create ssm params for resources used in other stacks
    new aws_ssm.StringParameter(this, 'GlobalSqsKmsKeyArnParameter', {
      parameterName: props.globalParameterNames.globalSqsKmsKeyArnPName,
      stringValue: globalSqsKmsKey.keyArn,
    });

    new aws_ssm.StringParameter(this, 'GlobalNcoExportQueueArnParameter', {
      parameterName: props.globalParameterNames.globalNcoExportQueueArnPName,
      stringValue: glocalExportNcoQueue.queueArn,
    });

    new aws_ssm.StringParameter(this, 'TypeORMLambdaLayerParameter', {
      description: 'TypeORM Layer Arn',
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
      stringValue: typeORMLayerArn.layerVersionArn,
    });

    new aws_ssm.StringParameter(this, 'BossOrgTableStreamArnParameter', {
      description: 'CORA OC boss-org table Key Arn',
      parameterName: props.globalParameterNames.bossOrgTableStreamArnPName,
      stringValue: bossOrgTable.tableStreamArn ?? '',
    });

    new aws_ssm.StringParameter(this, 'KafkaSecretArnParameter', {
      description: 'CORA OC global Kafka Secret ARN',
      parameterName: props.globalParameterNames.kafkaSecretArnPName,
      stringValue: kafkaSecret.secretArn,
    });

    new aws_ssm.StringParameter(this, 'QuotaApiSecretArnParameter', {
      description: 'CORA OC global Quota API Secret ARN',
      parameterName: props.globalParameterNames.quotaApiSecretArnPName,
      stringValue: quotaApiSecret.secretArn,
    });

    new aws_ssm.StringParameter(this, 'ConfigApiSecretArnParameter', {
      description: 'CORA OC global Config API Secret ARN',
      parameterName: props.globalParameterNames.configApiSecretArnPName,
      stringValue: omConfigApiSecret.secretArn,
    });

    new aws_ssm.StringParameter(this, 'NewCarOrderTableStreamArnParamter', {
      description: 'CORA OC new-car-order table stream arn',
      parameterName: props.globalParameterNames.newCarOrderTableStreamArnPName,
      stringValue: newCarOrderTable.tableStreamArn ?? '',
    });

    //export purchase intentions table stream
    new aws_ssm.StringParameter(this, 'PurchaseIntentionsTableStreamArnParamter', {
      description: 'CORA OC pvms purchase intentions table stream arn',
      parameterName: props.globalParameterNames.purchaseIntentionsTableStreamArnPName,
      stringValue: purchaseIntentionsTable.tableStreamArn ?? '',
    });

    //export healt monitoring topic arn and key arn
    new aws_ssm.StringParameter(this, 'HealthMonitoringTopicArnParameter', {
      parameterName: props.globalParameterNames.healthMonitoringTopicArnPName,
      stringValue: healthMonitoringTopic.topicArn,
    });

    //export global Dynamo KMS key arn
    new aws_ssm.StringParameter(this, 'GlobalDynamoKmsKeyArnParameter', {
      parameterName: props.globalParameterNames.globalDynamoKmsKeyArnPName,
      stringValue: globalDynamoKmsKey.keyArn,
    });

    //export log group key arn
    new aws_ssm.StringParameter(this, 'LogGroupKmsKeyArnParameter', {
      parameterName: props.globalParameterNames.logGroupKmsKeyArnPName,
      stringValue: logGroupKey.keyArn,
    });

    //export global secret key arn
    new aws_ssm.StringParameter(this, 'GlobalSecretKmsKeyArnParameter', {
      parameterName: props.globalParameterNames.globalSecretKmsKeyArnPName,
      stringValue: globalSecretKmsKey.keyArn,
    });

    //export global health monitoring topic key arn
    new aws_ssm.StringParameter(this, 'HealthMonitoringTopicKmsKeyArnParameter', {
      parameterName: props.globalParameterNames.healthMonitoringTopicKmsKeyArnPName,
      stringValue: healthMonitoringTopicKms.keyArn,
    });

    // Export aurora security group ID as SSM parameter
    new aws_ssm.StringParameter(this, 'aurora-secgroup-id-param', {
      description: 'CORA Aurora security group ID',
      parameterName: props.globalParameterNames.auroraSecurityGroupPName,
      stringValue: auroraSecurityGroup.securityGroupId,
    });

    // Export aurora access security group ID as SSM parameter
    new aws_ssm.StringParameter(this, 'aurora-access-secgroup-id-param', {
      description: 'CORA Aurora access security group ID',
      parameterName: props.globalParameterNames.auroraAccessSecurityGroupPName,
      stringValue: auroraAccessSecurityGroup.securityGroupId,
    });

    // Export external access security group ID as SSM parameter
    new aws_ssm.StringParameter(this, 'external-access-secgroup-id-param', {
      description: 'CORA external access security group ID',
      parameterName: props.globalParameterNames.externalAccessSecurityGroupPName,
      stringValue: externalAccessSecurityGroup.securityGroupId,
    });

    // Export kafka access security group ID as SSM parameter
    new aws_ssm.StringParameter(this, 'kafka-secgroup-id-param', {
      description: 'CORA kafka access security group ID',
      parameterName: props.globalParameterNames.kafkaSecurityGroupPName,
      stringValue: kafkaSecurityGroup.securityGroupId,
    });

    // Export vpc endpoints security group ID as SSM parameter
    new aws_ssm.StringParameter(this, 'vpc-endpoints-secgroup-id-param', {
      description: 'CORA VPC endpoints security group ID',
      parameterName: props.globalParameterNames.vpcEndpointsSecurityGroupPName,
      stringValue: vpcEndpointsSecurityGroup.securityGroupId,
    });

    // Export global s3 kms key
    new aws_ssm.StringParameter(this, 'S3KmsKeyArnParam', {
      description: 'KMS Key ARN of the global S3 KMS key',
      parameterName: props.globalParameterNames.globalS3KmsKeyArnPName,
      stringValue: s3KmsKey.keyArn,
    });

    ////CORA ORDER LISTS
    // Create dynamoDB table for new car orders
    new KasDynamodbTable(this, 'newCarOrdersForListsTable', {
      tableName: Constants.buildResourceName(
        props.stage,
        Constants.DYNAMODB_CORA_NEW_CAR_ORDER_LISTS_TABLE_PARAMS.tableName,
      ),
      partitionKey: {
        name: Constants.DYNAMODB_CORA_NEW_CAR_ORDER_LISTS_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: Constants.DYNAMODB_CORA_NEW_CAR_ORDER_LISTS_TABLE_PARAMS.sk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    //table for purchase intentions
    new KasDynamodbTable(this, 'purchaseIntentionsForListsTable', {
      tableName: Constants.buildResourceName(
        props.stage,
        Constants.DYNAMODB_PVMS_PURCHASE_INTENTIONS_LISTS_TABLE_PARAMS.tableName,
      ),
      partitionKey: {
        name: Constants.DYNAMODB_PVMS_PURCHASE_INTENTIONS_LISTS_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: Constants.DYNAMODB_PVMS_PURCHASE_INTENTIONS_LISTS_TABLE_PARAMS.sk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    // table for transactions
    const transactionTable = new KasDynamodbTable(this, 'transactionTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.tableName),
      partitionKey: {
        name: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    // Add index for action_at
    transactionTable.addGlobalSecondaryIndex({
      indexName: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.action_index,
      partitionKey: {
        name: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.action_index_pk,
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.action_index_sk,
        type: dynamodb.AttributeType.STRING,
      },
    });

    new KasDynamodbTable(this, 'subtransactionTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName),
      partitionKey: {
        name: Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.pk,
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.sk,
        type: dynamodb.AttributeType.STRING,
      },
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      encryptionKey: globalDynamoKmsKey,
    });

    let object_expiration: Duration;
    switch (props.stage) {
      case 'dev':
        object_expiration = ExportLogsToCompBucketConstruct.OBJECT_EXPIRE_DEFAULTS.DEV;
        break;
      case 'int':
        object_expiration = ExportLogsToCompBucketConstruct.OBJECT_EXPIRE_DEFAULTS.INT;
        break;
      case 'prod':
        object_expiration = ExportLogsToCompBucketConstruct.OBJECT_EXPIRE_DEFAULTS.PROD;
        break;
      default:
        object_expiration = ExportLogsToCompBucketConstruct.OBJECT_EXPIRE_DEFAULTS.DEV;
    }

    new ExportLogsToCompBucketConstruct(this, 'LogExporter', {
      region: props.env.region!,
      accountId: props.env.account!,
      s3CustomManagedKey: s3KmsKey,
      sqsCustomManagedKey: sqsKmsKey,
      logsCustomManagedKey: logGroupKey,
      gobd: {
        object_expiration: object_expiration,
      },
      stage: props.stage,
    });

    new KasLogBasedAlertingConstruct(this, 'LogBasedAlertingConstruct', {
      alertingEmails: props.alerting.alertingEmails,
      dynamoDbKey: globalDynamoKmsKey,
      logGroupKey: logGroupKey,
      stage: props.stage,
      useCaseShortName: 'CORA',
      useCaseLongName: 'CORA - Car Order Application',
      resourceNamePrefix: Constants.buildResourceName(props.stage, 'alerting-construct-'),
      snsKmsKey: healthMonitoringTopicKms,
      logSubscriptionLambdaArnParamName: Constants.LOG_BASED_ALERTING_LOG_SUBSCRIPTION_LAMBDA_ARN_PARAM_NAME,
      alertingLambdaProps: {
        logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
        vpc: vpc,
        vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
        securityGroups: [vpcEndpointsSecurityGroup],
      },
    });
  }

  public static getLogSubscriptionLambda(scope: Construct): IFunction {
    const logSubscriptionLambdaArn = StringParameter.valueForStringParameter(
      scope,
      Constants.LOG_BASED_ALERTING_LOG_SUBSCRIPTION_LAMBDA_ARN_PARAM_NAME,
    );
    return Function.fromFunctionArn(scope, 'logSubscriptionLambda', logSubscriptionLambdaArn);
  }

  public static getDynamoDbTable(
    scope: Construct,
    constantsTableName: string,
    stage: KasStage,
    tableKey: KasKmsKey,
  ): KasDynamodbTable {
    const tableDdbName = Constants.buildResourceName(stage, constantsTableName);
    return KasDynamodbTable.fromTableAttributes(scope, constantsTableName, {
      tableName: tableDdbName,
      encryptionKey: tableKey,
    });
  }
}
