import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getPpnId, sendFail, sendSuccess, getAuthContext, sanitizeApiGwEvent } from '../../../utils/api-helpers';
import { getMdDealerImporter, getEnvVarWithAssert, scanAllFromTable } from '../../../utils/utils';
import { getAllAuthorizedOrgs, getPermissionForDealer } from '../../../utils/validation-helpers';
import { CoraMdPortCode } from '../../../../lib/types/masterdata-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const dlrTableName = getEnvVarWithAssert('TABLE_NAME_DEALER');
const impTableName = getEnvVarWithAssert('TABLE_NAME_IMPORTER');
const portCodesTableName = getEnvVarWithAssert('TABLE_NAME_PORT_CODES');
const orgRelsTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');

const fetchAllPortCodesFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  try {
    const dealerNumber = event.queryStringParameters ? event.queryStringParameters['dealer_number'] : undefined;
    if (!dealerNumber) {
      return sendFail({ message: 'Missing DealerNumber', status: 400, reqHeaders: event.headers }, logger);
    }
    //get the org id of the user from auth context
    const ppnId = getPpnId({ event }, logger);
    if (!ppnId) {
      logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: sanitizeApiGwEvent({ event }, logger) });
      return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
    }
    //determine visibility level for the user
    const userAttributes = getAuthContext({ event }, logger);
    const visibilityLevel = userAttributes?.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
    if (!visibilityLevel) {
      logger.log(LogLevel.WARN, 'Failed to determine visibility level', {
        data: sanitizeApiGwEvent({ event }, logger),
      });
      return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
    }
    const authorizedOrgs = await getAllAuthorizedOrgs({ dynamoDb, orgRelsTableName, ppnId }, logger);
    const correspondingOrg = authorizedOrgs.find((org) => org.importer_number && org.dealer_number === dealerNumber);
    if (!correspondingOrg) {
      return sendFail(
        { message: `No Permissions or Org does not exists: ${dealerNumber}`, status: 400, reqHeaders: event.headers },
        logger,
      );
    }
    let res: CoraMdPortCode[] = [];
    const portCodes = await scanAllFromTable<CoraMdPortCode>({ tableName: portCodesTableName, dynamoDb }, logger);
    const { dealer, importer } = await getMdDealerImporter(
      {
        dynamoDb,
        dlrTableName,
        impTableName,
        importerNumber: correspondingOrg.importer_number!,
        dealerNumber: dealerNumber,
      },
      logger,
    );
    switch (getPermissionForDealer({ dealerNumber, authorizedOrgs }, logger)) {
      case 'Dealer':
        res = portCodes.filter((portCode) => {
          if (portCode.is_deactivated) {
            return false;
          }
          if (portCode.pk_port_code === dealer?.standard_port_code) {
            return true;
          }
          return false;
        });
        break;
      case 'Importer':
        res = portCodes.filter((portCode) => {
          if (portCode.is_deactivated) {
            return false;
          }
          if (portCode.pk_port_code === dealer?.standard_port_code) {
            return true;
          }
          if (dealer?.alternative_port_codes?.includes(portCode.pk_port_code)) {
            return true;
          }
          if (importer?.port_codes?.includes(portCode.pk_port_code)) {
            return true;
          }
          return false;
        });
        break;
      default:
        logger.log(LogLevel.INFO, `No Permissions or Org does not exists: ${dealerNumber}`);
        return sendFail(
          { message: `No Permissions or Org does not exists: ${dealerNumber}`, status: 400, reqHeaders: event.headers },
          logger,
        );
    }
    logger.log(LogLevel.DEBUG, 'Filtered results:', { data: res.map((ps) => JSON.stringify(ps)).concat(';') });

    const res_with_attributes = res.map((pc) => ({
      ...pc,
      editable: visibilityLevel !== 'DLR',
      standard: pc.pk_port_code === dealer?.standard_port_code,
    }));
    logger.log(LogLevel.DEBUG, 'Final results with attributes:', {
      data: res_with_attributes.map((ps) => JSON.stringify(ps)).concat(';'),
    });
    return sendSuccess({ body: res_with_attributes, reqHeaders: event.headers }, logger);
  } catch (err) {
    logger.log(LogLevel.ERROR, 'Error getting portCodes', { data: err });
    return sendFail({ message: 'Error loading portcodes', status: 500, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-port-codes', LogLevel.TRACE)(event, context, fetchAllPortCodesFunc);
