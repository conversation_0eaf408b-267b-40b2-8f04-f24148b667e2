import { NotificationStatus, OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import { NOTIFICATION_CENTER_TRANSACTION_BASE_URL, USERNAME_WRITE } from '../support/constants';
import { preproductionOrderMtvs, preproductionOrders } from '../support/order-lists-test-data';
import { retryableBefore } from '../support/retry';
import { checkTransactionResponse } from '../support/utils';

const displayOrdersTab = '[data-e2e="display_orders"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';
const ordersEndpointURL = '**/new-car-order**';
const resultOrderModal = `[data-e2e="${OneVmsEventKey.UPDATE_NCO}_order_modal"]`;
const closeBtn = '[data-e2e="close"]';
describe('Order List Actions Change', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task('prepareMtvRds', { objs: preproductionOrderMtvs }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: preproductionOrderMtvs });
  });

  it('Change order from list', () => {
    const editOrderButton = '[data-e2e="edit_order"]';
    const changeOrderModal = '[data-e2e="change_order_modal"]';
    const saveKccBtn = '[data-e2e="SaveKcc"]';
    //Edit modal inputs
    const orderTypeLocator: string = '[data-e2e="SelectOrderType"]';
    const shippingCodeLocator: string = '[data-e2e="SelectShippingCode"]';
    const deliveryDateLocator: string = '[data-e2e="DeliveryDate"]';
    const submitOrderLocator: string = '[data-e2e="submit_order"]';

    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    //Click on Edit on first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(editOrderButton, { timeout: 3000 }).should('be.visible').click();

    //Check iframe and click on kcc save to open cora modal
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find('body').should('not.be.undefined');
    cy.get('iframe', { timeout: 10000 }).its('0.contentDocument').find(saveKccBtn).click();
    cy.get(changeOrderModal, { timeout: 20000 }).should('exist').and('not.be.empty');

    //Check Modal view, select some props and save
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, 2);
    cy.get(shippingCodeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(shippingCodeLocator, 1);
    cy.get(deliveryDateLocator).type('2025-11-01');

    cy.intercept('PATCH', `${ordersEndpointURL}/${preproductionOrders[0].pk_new_car_order_id}`).as('updateNco');
    cy.get(submitOrderLocator).click();
    cy.wait('@updateNco')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //close the dialog (since call was mocked, do not check if table was updated)
    cy.get(resultOrderModal).find(closeBtn).click();
  });
  it('Should show error if order was changed meanwhile (simulate race condition)', () => {
    const editOrderButton = '[data-e2e="edit_order"]';
    const changeOrderModal = '[data-e2e="change_order_modal"]';
    const saveKccBtn = '[data-e2e="SaveKcc"]';
    const orderTypeLocator: string = '[data-e2e="SelectOrderType"]';
    const shippingCodeLocator: string = '[data-e2e="SelectShippingCode"]';
    const deliveryDateLocator: string = '[data-e2e="DeliveryDate"]';
    const submitOrderLocator: string = '[data-e2e="submit_order"]';

    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(editOrderButton, { timeout: 3000 }).should('be.visible').click();

    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find('body').should('not.be.undefined');
    cy.get('iframe', { timeout: 10000 }).its('0.contentDocument').find(saveKccBtn).click();
    cy.get(changeOrderModal, { timeout: 20000 }).should('exist').and('not.be.empty');

    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, 2);
    cy.get(shippingCodeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(shippingCodeLocator, 1);
    cy.get(deliveryDateLocator).type('2025-11-01');

    //confirm and intercept cora api call to be able to let it fail
    cy.intercept('PATCH', `${ordersEndpointURL}/${preproductionOrders[0].pk_new_car_order_id}`, (req) => {
      req.body.nco_ids_with_modified_at[0].modified_at = new Date(0).toISOString();
      req.continue();
    }).as('updateNcoConflict');

    cy.get(submitOrderLocator).click();
    cy.wait('@updateNcoConflict')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.ERROR),
        );
      });

    //close modal
    cy.get(resultOrderModal).find(closeBtn).click();
  });
});
