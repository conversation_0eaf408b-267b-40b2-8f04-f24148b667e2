const fs = require('fs');
const path = require('path');
const prefix = '/new-car-order/get';

module.exports = function (req, res, next) {
  if (req.method === 'POST' && req.url.startsWith(prefix) && !!req.body) {
    const _send = res.send;

    // Lade den gesamten Datensatz aus der db.json Datei
    fs.readFile(path.join(__dirname, '../db.json'), 'utf8', (err, data) => {
      if (err) {
        return _send.call(this, JSON.stringify({ message: 'Internal Server Error' }), 500);
      }

      try {
        const json = JSON.parse(data);
        let ncos = json['new-car-order'];
        const filters = req.body;
        const sortModel = filters.sortModel;
        const filterModel = filters.filterModel;
        const actionFilters = filters.actionFilters;
        const _status = json['onevms-status'];

        // Action Filters
        if (actionFilters?.changeable) {
          ncos = ncos.filter(
            (nco) =>
              _status.find(
                (s) =>
                  s.one_vms_status === nco.order_status_onevms_code &&
                  (s.one_vms_error_status ?? 'null') === (nco.order_status_onevms_error_code ?? 'null'),
              )?.order_changeable,
          );
        }

        // Filters
        for (const [k, v] of Object.entries(filterModel)) {
          if (v.filterType === 'set') {
            ncos = ncos.filter((nco) => v.values.includes(nco[k]));
          } else {
            ncos = ncos.filter((nco) => nco[k]?.includes(v.filter));
          }
        }

        // Sorting
        if (sortModel && sortModel[0]) {
          if (sortModel[0].sort === 'asc') {
            ncos.sort((a, b) => (a[sortModel[0].colId] < b[sortModel[0].colId] ? -1 : 1));
          } else {
            ncos.sort((a, b) => (a[sortModel[0].colId] < b[sortModel[0].colId] ? 1 : -1));
          }
        }

        // Pagination
        ncos = ncos.slice(filters.startRow, filters.endRow);
        const filteredData = { data: ncos, rowCount: -1 };

        if (filteredData.data.length > 0) {
          return _send.call(res, JSON.stringify(filteredData));
        } else {
          return _send.call(res, JSON.stringify({ message: 'No matching records found' }), 404);
        }
      } catch (e) {
        return _send.call(res, JSON.stringify({ message: 'Internal Server Error' }), 500);
      }
    });
  } else {
    next();
  }
};
