import { <PERSON><PERSON><PERSON><PERSON>, PButtonGroup, PLink, PModal } from '@porsche-design-system/components-react';
import './OrderFlowModal.css';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { routes } from '../../../Constants';
import { clearConfig, setKccFocused } from '../../../store/slices/KccConfigSlice';
import { useDispatch } from 'react-redux';
import { useInIframe } from '../../../utils/useInIframe';
import { useGetStageConfigQuery } from '../../../store/api/StaticJsonApi';
import {
  InboundApiEventResponse,
  OneVmsEventKey,
} from '../../../../../infrastructure/lib/types/process-steering-types';
import { CoraNCPurchaseIntentionApiResponse } from '../../../../../infrastructure/lib/types/purchase-intention-types';
import { ProcessSteeringResultStateComponent } from '../../../components/shared/process-steering-components/order-action-common-modal/ProcessSteeringResultState';

interface DealConvertResultModalProps {
  open: boolean;
  originalPi: CoraNCPurchaseIntentionApiResponse;
  error: any;
  result?: InboundApiEventResponse;
}
export const DealConvertResultModal: React.FC<DealConvertResultModalProps> = ({ open, originalPi, error, result }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const heading = t('convert_purchase_intention_success');
  const navigate = useNavigate();
  const { data: stageConfig } = useGetStageConfigQuery(undefined);
  const isInIframe = useInIframe();

  function goToPurchaseIntentionList() {
    dispatch(clearConfig(true));
    dispatch(setKccFocused(true));
    navigate(routes.lists.purchaseIntentions);
  }
  function goToOrderList() {
    dispatch(setKccFocused(true));
    navigate(routes.lists.preProductionOrders);
  }
  const getDoriUrl = (ncoId: string): string => {
    const doriOrderDetailsPath = routes.external_urls.doriOrderDetailsRelativePath(ncoId);
    const doriAppPath = isInIframe
      ? `${document.referrer}${routes.external_urls.doriRelativePaddockPath}`
      : `${routes.external_urls.doriFullPath(stageConfig?.stage)}`;
    return doriAppPath + doriOrderDetailsPath;
  };

  const navigationButtonGroup = (
    <>
      <PButtonGroup className="footer">
        <PButton data-e2e="go_to_pi_btn" onClick={goToPurchaseIntentionList}>
          {t('navigate_to_deal_list')}
        </PButton>
        <PButton data-e2e="go_to_orders_btn" onClick={goToOrderList} variant="secondary">
          {t('navigate_to_order_list')}
        </PButton>
        <PLink
          data-e2e="go_to_dori_btn"
          variant="secondary"
          href={getDoriUrl(originalPi.purchase_intention_id)}
          target={'_top'}
        >
          {t('navigate_to_dori')}
        </PLink>
      </PButtonGroup>
    </>
  );

  return (
    <PModal
      disableBackdropClick
      data-e2e={`${OneVmsEventKey.CONVERT_PI}_result_modal`}
      open={open}
      dismissButton={false}
    >
      <ProcessSteeringResultStateComponent
        closeModal={goToPurchaseIntentionList}
        isMulti={false}
        error={error}
        result={result}
        actionType={OneVmsEventKey.CONVERT_PI}
        navigationButtonGroup={!error ? navigationButtonGroup : undefined}
      />
    </PModal>
  );
};
