import { CoraNCOConfiguration } from '../../../../infrastructure/lib/types/new-car-order-types';
import { importerCodeToPVALocale } from '../../config/constants';
import my4TranslationTable from './my4TranslationTable.json';
import { v4 as uuidv4 } from 'uuid';

export interface KccKasConfiguration {
  model_info: {
    model_type: string;
    model_year: number;
    country_code: string;
  };
  configuration: CoraNCOConfiguration;
}

export interface KccSaveConfigWindowMessage {
  channel: 'KASCC_NOTIFICATION';
  type: 'CONFIG_SAVED';
  version: string;
  payload: {
    kas: {
      configuration: CoraNCOConfiguration;
    };
    pvms: unknown;
    signature: string;
  };
  status: {
    code: number;
    message: string;
    errors: unknown;
  };
}

export interface KccProxyInitialParams {
  meta_data: {
    uuid?: string;
    caller_id: 'CORA';
    process: 'CO1' | 'CO2';
    call_type: 'Konfiguration';
    /**
     * Preisdatum YYYYMMDDHHmmss
     * Vermutich weitere Anpassungen notwendig am naming (market_date was ist das überhaupt)
     * Im KCC Team in Klärung, stand 2024-05-15
     */
    PriceDate: string;
    context_datbase_date: string;
    market_date: string;
    masterdata_environment: 'P';
    enableCustomerSelection: false;
    configurator_language: string;
  };
  order_info: {
    base_info: {
      quota_month: string | null;
    };
    trading_partner: {
      importer_number: string;
      importer_code: string;
      dealer_sold_to_number: string;
    };
  };
  model_info: {
    model_type: string;
    model_year: number;
    country_code: string;
  };
  user?: {
    importer_number: string;
    importer: string;
    dealer_number: string;
    conf_language: string;
  };
  vehicle?: unknown;
  configuration?: unknown;
}
export interface KccComponentInitialRequestParams {
  importer_number: string;
  dealer_number: string;
  importer_code: string;
  model_type: string;
  process: 'CO1' | 'CO2';
  /**
   * my4 e.g. 2024
   */
  model_year: string;
  cnr: string;
  quota_month: string | null;
  conf_language?: string;
  configuration?: CoraNCOConfiguration;
  configuration_expire?: unknown;
}

export function createKccInitParamObject(
  props: KccComponentInitialRequestParams,
  with_uuid = false,
): KccProxyInitialParams {
  const browserLanguage = navigator.language;
  const available_languages_for_market = importerCodeToPVALocale[props.importer_code];

  let conf_language = 'empty';

  if (!available_languages_for_market) {
    console.log('importer_code not found in importerCodeToPVALocale mapping', props.importer_code);
    conf_language = 'de';
  }

  if (available_languages_for_market) {
    for (const language of available_languages_for_market) {
      if (browserLanguage.includes(language)) {
        conf_language = language;
        break;
      }
    }
    if (conf_language === 'empty') {
      //this means the browser langugage was not found in the available_languages_for_market. In this case we just use the first language in the array
      console.log('browser language not found in available_languages_for_market', browserLanguage);
      conf_language = available_languages_for_market[0];
    }
  }

  // Preisdatum/Market Date (Bei Neuanlage 1. des QM)
  let market_date = new Date().toISOString().split('T')[0].replaceAll('-', '') + '000000';
  if (props.quota_month) {
    market_date = props.quota_month.replaceAll('-', '') + '01000000';
  }
  const res: KccProxyInitialParams = {
    meta_data: {
      uuid: with_uuid ? uuidv4() : undefined,
      caller_id: 'CORA',
      process: props.process,
      call_type: 'Konfiguration',
      PriceDate: market_date,
      market_date: market_date,
      context_datbase_date: market_date,
      masterdata_environment: 'P',
      enableCustomerSelection: false,
      configurator_language: conf_language,
    },
    order_info: {
      base_info: {
        quota_month: props.quota_month,
      },
      trading_partner: {
        importer_code: props.importer_code,
        importer_number: props.importer_number,
        dealer_sold_to_number: props.dealer_number,
      },
    },
    model_info: {
      country_code: props.cnr,
      model_type: props.model_type,
      model_year: Number(props.model_year),
    },
  };
  if (props.configuration) {
    res.configuration = props.configuration;
  } else if (props.configuration_expire) {
    res.vehicle = props.configuration_expire;
    res.user = {
      conf_language: res.meta_data.configurator_language,
      importer: res.order_info.trading_partner.importer_code,
      importer_number: res.order_info.trading_partner.importer_number,
      dealer_number: res.order_info.trading_partner.dealer_sold_to_number,
    };
  }
  return res;
}
export function getKccDefaultUrl() {
  if (window.location.hostname === 'localhost') {
    return 'http://localhost:3005';
  }
  return `https://kcc.${window.location.host}/integratePVMSNext.do`; //https://kcc.cora-int.dpp.porsche.com/integratePVMSNext.do
}
export function getKccSearchParams(params?: Record<string, string>): string {
  if (params) {
    return new URLSearchParams(params).toString();
  }
  return '';
}

export function getKccHostname() {
  if (window.location.hostname === 'localhost') {
    return 'http://localhost:3005';
  }
  return `https://kcc.${window.location.host}/`;
}

export function getKccCacheHostname() {
  if (window.location.hostname === 'localhost') {
    return 'http://localhost:3006/cache-config';
  }
  return `https://cache.kcc.${window.location.host}`;
}

export function translateMy4ToSingleChar(my4: string): string {
  const res = (my4TranslationTable as Partial<Record<string, string>>)[my4];
  if (!res) {
    throw new Error(`my4: ${my4} does not exist in translation table`);
  }
  return res;
}
