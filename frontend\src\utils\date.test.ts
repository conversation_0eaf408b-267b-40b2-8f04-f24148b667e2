import { describe, expect, it, vi } from 'vitest';
import { getNextSixMonths, getNextSixMonthsOfGivenDate } from './date';

describe('getNextSixMonthsOfGivenDate', () => {
  afterEach(() => {
    vi.useRealTimers(); // Restore real timers after each test
  });

  it('should return the next 6 months from today', () => {
    vi.useFakeTimers(); // Mock current date
    vi.setSystemTime(new Date('2025-01-31T12:00:00Z')); // Set fixed date

    expect(getNextSixMonths()).toEqual(['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']);
  });

  it("should return ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06'] for 2025-01-31T23:00:00Z", () => {
    expect(getNextSixMonthsOfGivenDate('2025-01-31T23:00:00Z')).toEqual([
      '2025-01',
      '2025-02',
      '2025-03',
      '2025-04',
      '2025-05',
      '2025-06',
    ]);
  });

  it("should return ['2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08'] for 2025-03-01T00:00:00Z", () => {
    expect(getNextSixMonthsOfGivenDate('2025-03-01T00:00:00Z')).toEqual([
      '2025-03',
      '2025-04',
      '2025-05',
      '2025-06',
      '2025-07',
      '2025-08',
    ]);
  });

  it("should return ['2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08'] for 2025-03-31T23:59:00Z", () => {
    expect(getNextSixMonthsOfGivenDate('2025-03-31T23:59:00Z')).toEqual([
      '2025-03',
      '2025-04',
      '2025-05',
      '2025-06',
      '2025-07',
      '2025-08',
    ]);
  });

  it('should handle year transition correctly (Dec to next year)', () => {
    expect(getNextSixMonthsOfGivenDate('2025-12-15T10:00:00Z')).toEqual([
      '2025-12',
      '2026-01',
      '2026-02',
      '2026-03',
      '2026-04',
      '2026-05',
    ]);
  });

  it('should handle leap year transition correctly', () => {
    expect(getNextSixMonthsOfGivenDate('2024-02-29T12:00:00Z')).toEqual([
      '2024-02',
      '2024-03',
      '2024-04',
      '2024-05',
      '2024-06',
      '2024-07',
    ]);
  });
});
