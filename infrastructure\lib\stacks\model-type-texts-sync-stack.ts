import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@kas-resources/constructs';
import { aws_ssm, aws_ec2 as ec2, Stack } from 'aws-cdk-lib';
import { LayerVersion } from 'aws-cdk-lib/aws-lambda';
import { AuthenticationMethod } from 'aws-cdk-lib/aws-lambda-event-sources';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';
import { GlobalStack } from './global-stack';

export class ModelTypeTextsSyncStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    const auroraWriterSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraWriterSecretArnPName,
    );
    const auroraWriterSecret = KasSecret.fromSecretAttributes(this, id + 'auroraWriterSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraWriterSecretArn,
    });

    // Kafka Parameters
    const _kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );

    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _kafkaSecretArn,
    });

    // Retrieve all necessary security groups
    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    const auroraAccessSecurityGroupID = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    //TypeORM Lambda Layer
    const typeORMLayerArn = StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeORMLayer = LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    // Lambda Consumer for pccd inbound model type texts
    const modelTypeTextsSyncLambdaName = `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-model-type-texts-consumer`;
    const groupId = `FRA_one_vms_cora_pccd_inbound_model_type_text_consumer_group_${props.stage}_01`;
    const modelTypeTextsSyncLambda = new KafkaConsumerLambda(this, modelTypeTextsSyncLambdaName, {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      description: 'Cora Lambda to sync model type texts from pccdk topic to dynamodb',
      customManagedKey: logGroupKey,
      entry: 'lambda/backend/model-type-text/model-type-text-kafka-import/index.ts',
      functionName: modelTypeTextsSyncLambdaName,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaImportGroup: groupId,
      kafkaImportTopic: props.kafkaParameters.pccdModelTypeTextTopic,
      kafkaBatchSize: 200,
      kafkaSecret: kafkaSecret,
      extraEnvVars: {
        AURORA_SECRET_ARN: auroraWriterSecret.secretArn,
      },
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [kafkaSecurityGroup, auroraAccessSecurityGroup],
      stage: props.stage,
      layers: [typeORMLayer],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
    });
    auroraWriterSecret.grantRead(modelTypeTextsSyncLambda);
  }
}
