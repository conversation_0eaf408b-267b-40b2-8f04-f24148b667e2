/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DataSource, Repository } from 'typeorm';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';

const lambdaArn = buildLambdaArn('fetch-model-types');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);

const importerOrgId1 = '5dc7f453-38e2-471e-b503-00a8e47b4df1';
const importerOrgId2 = '5979a1ef-d88a-4c03-bde7-805cf39414eb';
const dealerGroupOrgId1 = '1f9bece2-bfd9-48d3-ba49-b37251500d3b';
const dealerGroupOrgId2 = '35fd1bcb-fd86-4065-9eca-843319aacc66';
const dealerOrgId1 = '56a0c7cc-6844-4f53-8a2f-5d58c527f833';
const dealerOrgId2 = 'fd07dd84-6fe7-433b-a498-600821666fc2';
const dealerOrgIdInactive = '4a20a003-56ef-4878-ba93-ff05aa0f7838';

const orgRels: CoraOrgRelModel[] = [
  //importer 1 relation to itself
  {
    pk_ppn_id: importerOrgId1,
    parent_ppn_id: importerOrgId1,
    dealer_number: 'ItImp1',
    display_name: 'IntegrationTest - Importer1',
    importer_number: 'ItImp1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //importer 2 relation to itself
  {
    pk_ppn_id: importerOrgId2,
    parent_ppn_id: importerOrgId2,
    dealer_number: 'ItImp2',
    display_name: 'IntegrationTest - Importer2',
    importer_number: 'ItImp2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 1 relation to parent importer1
  {
    pk_ppn_id: dealerGroupOrgId1,
    parent_ppn_id: importerOrgId1,
    display_name: 'IntegrationTest - Dealer Group1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 1 relation to itself
  {
    pk_ppn_id: dealerGroupOrgId1,
    parent_ppn_id: dealerGroupOrgId1,
    display_name: 'IntegrationTest - Dealer Group1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 2 relation to parent importer2
  {
    pk_ppn_id: dealerGroupOrgId2,
    parent_ppn_id: importerOrgId2,
    display_name: 'IntegrationTest - Dealer Group2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 2 relation to itself
  {
    pk_ppn_id: dealerGroupOrgId2,
    parent_ppn_id: dealerGroupOrgId2,
    display_name: 'IntegrationTest - Dealer Group2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent dealer group1
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId1,
    dealer_number: 'ItDlr1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImp1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId1,
    dealer_number: 'ItDlr1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImp1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: 'ItDlr1',
    display_name: 'IntegrationTest - Dealer 1',
    importer_number: 'ItImp1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent dealer group2
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId2,
    dealer_number: 'ItDlr2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImp2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId2,
    dealer_number: 'ItDlr2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImp2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: 'ItDlr2',
    display_name: 'IntegrationTest - Dealer 2',
    importer_number: 'ItImp2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent dealer group1
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerGroupOrgId1,
    dealer_number: 'ItDlrInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImp1',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent importer
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: importerOrgId1,
    dealer_number: 'ItDlrInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImp1',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to itself
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerOrgIdInactive,
    dealer_number: 'ItDlrInactive',
    display_name: 'IntegrationTest - Dealer Inactive',
    importer_number: 'ItImp1',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

const mtvImpInPast: ModelTypeVisibilityModel = {
  //For IMP visible date in past
  importer_number: 'ItImp1',
  cnr: 'CNR1',
  model_type: 'MT0001',
  my4: '2030',
  role: 'IMP',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  created_at: new Date().toISOString(),
  modified_by: 'INT_TEST',
  modified_at: new Date().toISOString(),
};
const mtvImpInFuture: ModelTypeVisibilityModel = {
  //For IMP visible date in future
  importer_number: 'ItImp1',
  cnr: 'CNR1',
  model_type: 'MT0002',
  my4: '2030',
  role: 'IMP',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  created_at: new Date().toISOString(),
  modified_by: 'INT_TEST',
  modified_at: new Date().toISOString(),
};

const mtvDlrInPast: ModelTypeVisibilityModel = {
  //For DLR visible date in past
  importer_number: 'ItImp1',
  cnr: 'CNR1',
  model_type: 'MT0001',
  my4: '2030',
  role: 'DLR',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  created_at: new Date().toISOString(),
  modified_by: 'INT_TEST',
  modified_at: new Date().toISOString(),
};
const mtvDlrInFuture: ModelTypeVisibilityModel = {
  //For DLR visible date in future
  importer_number: 'ItImp1',
  cnr: 'CNR1',
  model_type: 'MT0002',
  my4: '2030',
  role: 'DLR',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  created_at: new Date().toISOString(),
  modified_by: 'INT_TEST',
  modified_at: new Date().toISOString(),
};

const mtvPagInPast: ModelTypeVisibilityModel = {
  //For PAG visible date in past
  importer_number: 'ItImp1',
  cnr: 'CNR1',
  model_type: 'MT0001',
  my4: '2030',
  role: 'PAG',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  created_at: new Date().toISOString(),
  modified_by: 'INT_TEST',
  modified_at: new Date().toISOString(),
};
const mtvPagInFuture: ModelTypeVisibilityModel = {
  //For PAG visible date in future
  importer_number: 'ItImp1',
  cnr: 'CNR1',
  model_type: 'MT0002',
  my4: '2030',
  role: 'PAG',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  created_at: new Date().toISOString(),
  modified_by: 'INT_TEST',
  modified_at: new Date().toISOString(),
};

const mtvs: ModelTypeVisibilityModel[] = [
  mtvImpInPast,
  mtvImpInFuture,
  mtvDlrInPast,
  mtvDlrInFuture,
  mtvPagInPast,
  mtvPagInFuture,
];

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

let dataSource: DataSource;
let repository: Repository<ModelTypeVisibilityModel>;
let testMtvs: ModelTypeVisibilityModel[] = [];

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([ModelTypeVisibilityModel]);
  repository = dataSource.getRepository(ModelTypeVisibilityModel);
  // deepcopy otherwise typeorm decides created_at/modified_at are timestamps and overwrites the sourceobj
  testMtvs = await repository.save(JSON.parse(JSON.stringify(mtvs)));
  await prepareDynamodb([{ tableName: orgRelTableName, objs: orgRels }]);
});

afterAll(async () => {
  await repository.remove(testMtvs);
  await dataSource.destroy();
  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
  ]);
});

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithVisibilityDlr = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'DLR',
    },
  ],
};

const appsWithVisibilityPag = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'PAG',
    },
  ],
};

it('should return 401 if org is missing in auth context', async () => {
  const event = createApiGwEvent({
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 401 if visibility is missing in auth context', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithNoRole,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 400 if importer_number query param is missing', async () => {
  const event = createApiGwEvent({ userOrgId: importerOrgId1, kasApplications: appsWithVisibilityImp });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for a user with org ID that is not in DB', async () => {
  const event = createApiGwEvent({
    userOrgId: 'somethingWrongHere',
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for a dealer whos org is inactive', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgIdInactive,
    kasApplications: appsWithVisibilityDlr,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for a dealer that is requesting a different importer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId2,
    kasApplications: appsWithVisibilityDlr,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for an importer that is requesting a different importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId2,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for an importer with PAG visibility that is requesting a different importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId2,
    kasApplications: appsWithVisibilityPag,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 200 with the list of correct MTVs for an authorized importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  //should only return mtvs with valid_from in the past
  expect(body.length).toEqual(1);
  expect(body[0]).toEqual(mtvImpInPast);
});

it('should return 200 with the list of correct MTVs for an authorized dealer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId1,
    kasApplications: appsWithVisibilityDlr,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  //should only return mtvs with valid_from in the past
  expect(body.length).toEqual(1);
  expect(body[0]).toEqual(mtvDlrInPast);
});

it('should return 200 with the list of correct MTVs for an authorized  with pag visibility', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityPag,
    queryStringParameters: { importer_number: 'ItImp1' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  //should only return mtvs with valid_from in the past
  expect(body.length).toEqual(1);
  expect(body[0]).toEqual(mtvPagInPast);
});
