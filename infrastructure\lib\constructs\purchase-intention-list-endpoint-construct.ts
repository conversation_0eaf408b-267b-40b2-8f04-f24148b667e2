import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON><PERSON>Function, KasStage } from '@kas-resources/constructs';
import { Duration, RemovalPolicy, aws_iam, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { IAuthorizer, IResource, LambdaIntegration, RestApi } from 'aws-cdk-lib/aws-apigateway';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export interface PurchaseIntentionListEndpointConstructProps {
  api: RestApi;
  authorizer: IAuthorizer;
  stage: Kas<PERSON>tage;
  logGroupKey: KasKmsKey;
  purchaseIntentionListsTable: ITable;
  globalDynamoKmsKey: KasKmsKey;
  coraOrgRelTable: ITable;
  corsDomain: string;
  parentResource: IResource;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  auroraRdsSecret: ISecret;
  typeormLayer: ILayerVersion;
}

export class PurchaseIntentionListEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: PurchaseIntentionListEndpointConstructProps) {
    super(scope, id);

    const piResource = props.parentResource.addResource('purchase-intention');

    // GET /purchase-intention
    const getPurchaseIntentions = new KasNodejsFunction(this, 'GetPurchaseIntentionList', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/fetch-purchase-intentions/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_LISTS: props.purchaseIntentionListsTable.tableName,
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        APPLICATION_NAME_TO_AUTHORIZE: Constants.APPLICATION_SHORT_NAME,
        AURORA_SECRET_ARN: props.auroraRdsSecret.secretArn,
      },
      description: 'Get all convertable purchase intentions',
      customManagedKey: props.logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-get-purchase-intention-list`,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: props.logSubscriptionLambda,
      timeout: Duration.minutes(10),
      memorySize: 1024,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      layers: [props.typeormLayer],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      stage: props.stage,
    });

    props.coraOrgRelTable.grantReadData(getPurchaseIntentions);
    getPurchaseIntentions.addToRolePolicy(
      new aws_iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: aws_iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${props.coraOrgRelTable.tableArn}/index/${Constants.CORA_ORG_PERMISSON_CHECK_PARAMS.pPIDIndex}`],
      }),
    );

    props.purchaseIntentionListsTable.grantReadData(getPurchaseIntentions);

    props.auroraRdsSecret.grantRead(getPurchaseIntentions);

    const getPurchaseIntentionsLambdaIntegration = new LambdaIntegration(getPurchaseIntentions);

    piResource.addMethod('GET', getPurchaseIntentionsLambdaIntegration, {
      authorizer: props.authorizer,
    });

    const ncoGetPIResource = piResource.addResource('get');
    ncoGetPIResource.addMethod('POST', getPurchaseIntentionsLambdaIntegration, {
      authorizer: props.authorizer,
    });
  }
}
