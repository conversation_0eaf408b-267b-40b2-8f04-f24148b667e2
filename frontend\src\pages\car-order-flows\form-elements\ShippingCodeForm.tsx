import { PSelectWrapper, PSpinner } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { FetchError } from '../../errors/FetchErrors';
import { useGetShippingCodesForDealerQuery } from '../../../store/api/MasterdataApi';
import { useEffect, useState } from 'react';

interface ShippingCodeFormProps {
  dealer_number: string;
  selectedShippingCode?: string;
  handleShippingCodeSelect: (shipping_code: string) => void;
  setError: (area: string, err?: string) => void;
}
export const ShippingCodeForm: React.FC<ShippingCodeFormProps> = ({
  dealer_number,
  selectedShippingCode,
  handleShippingCodeSelect,
  setError,
}) => {
  const { t } = useTranslation();

  const { data: shippingCodes, error, isLoading } = useGetShippingCodesForDealerQuery(dealer_number);
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);

  useEffect(() => {
    setError('shipping_code_form', errMessage);
  }, [errMessage]);

  if (isLoading) {
    return <PSpinner />;
  }

  if (!shippingCodes) {
    return <FetchError custom_error={error} error_area="shipping_code" />;
  }

  let selectedShippingCodeIsDisabled = false;
  if (!!selectedShippingCode) {
    selectedShippingCodeIsDisabled = !shippingCodes.find((code) => code.pk_shipping_code === selectedShippingCode)
      ?.editable;
  }

  return (
    <PSelectWrapper
      data-e2e="SelectShippingCode"
      style={{ flexBasis: 'calc(50% - 10px)' }}
      label={t('shipping_code_prompt')}
    >
      <select
        required
        value={selectedShippingCode ?? ''}
        disabled={selectedShippingCodeIsDisabled}
        onChange={(e) => {
          const shippingCode = shippingCodes.find((m) => m.pk_shipping_code === e.target.value);
          if (shippingCode) {
            handleShippingCodeSelect(shippingCode.pk_shipping_code);
          }
        }}
      >
        <option>{t('shipping_code_selection')}</option>
        {shippingCodes
          .filter((shippingCode) => shippingCode.editable || shippingCode.pk_shipping_code === selectedShippingCode)
          .map((shippingCode) => (
            <option
              key={shippingCode.pk_shipping_code}
              value={shippingCode.pk_shipping_code}
              disabled={!shippingCode.editable}
            >
              {`${shippingCode.pk_shipping_code} - ${shippingCode.description}`}
            </option>
          ))}
      </select>
      {selectedShippingCodeIsDisabled && <div style={{ color: 'gray' }}>{t('shipping_code_not_editable')}</div>}
    </PSelectWrapper>
  );
};
