import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { sendFail, sendSuccess } from '../../../../utils/api-helpers';
import { createApiGwHandler } from '../../../../utils/api-gw-handler';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { createTypeORMDataSource } from '../../../../config/typeorm-config';
import { getEnvVarWithAssert } from '../../../../utils/utils';
import { secretCache } from '../../../../utils/secret-cache';

// Initialize database client and table names
const stage = getEnvVarWithAssert('STAGE');

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const getAuditTrailFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const ncoId = event.pathParameters ? event.pathParameters['ncoId'] : undefined;
  logger.setObjectId(ncoId);
  const regex = /^[A-Z0-9]{8,9}$/;
  if (!ncoId || !regex.test(ncoId.trim())) {
    const message = 'Could not get audit trail for ncoid, invalid id';
    logger.log(LogLevel.WARN, message, { data: ncoId });
    return sendFail({ message: message, status: 400, reqHeaders: event.headers }, logger);
  }

  // Query the Audit Trail table
  try {
    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [NewCarOrderAuditTrailModel]);
    const auditTrails = await dataSource.manager.findBy(NewCarOrderAuditTrailModel, { pk_new_car_order_id: ncoId });
    logger.log(LogLevel.DEBUG, `Successfully retrieved audit trails for NCO ${ncoId}`, { data: auditTrails });
    return sendSuccess({ body: auditTrails, reqHeaders: event.headers }, logger);
  } catch (error) {
    logger.log(LogLevel.WARN, `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`, { data: error });
    return sendFail(
      {
        message: `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`,
        status: 500,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-audit-trail', LogLevel.TRACE)(event, context, getAuditTrailFunc);
