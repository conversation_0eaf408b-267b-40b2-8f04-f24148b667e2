import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CorrelationState {
  correlationId: string | null;
}

const initialState: CorrelationState = {
  correlationId: null,
};

const correlationSlice = createSlice({
  name: 'correlation',
  initialState,
  reducers: {
    setCorrelationId: (state, action: PayloadAction<string>) => {
      state.correlationId = action.payload;
    },
    clearCorrelationId: (state) => {
      state.correlationId = null;
    },
  },
});

export const { setCorrelationId, clearCorrelationId } = correlationSlice.actions;
export default correlationSlice.reducer;
