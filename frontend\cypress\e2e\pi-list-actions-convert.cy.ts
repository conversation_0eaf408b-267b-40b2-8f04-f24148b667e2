import { NOTIFICATION_CENTER_TRANSACTION_BASE_URL, USERNAME_WRITE } from '../support/constants';
import { retryableBefore } from '../support/retry';
import { generateMtvs, generatePIs } from '../support/order-lists-test-data';
import { NotificationStatus, OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import { checkTransactionResponse } from '../support/utils';

const piEndpointURL = '**/purchase-intention**';
const ordersEndpointURL = '**/new-car-order**';
const piPrefix = 'CVT';
const piEndpointGetURL = '**/purchase-intention/get';
const convertPurchaseIntentionBtn: string = '[data-e2e="convert_order"]';
const saveKccBtn = '[data-e2e="SaveKcc"]';

//test PIs
const pis = generatePIs(piPrefix, 3);

//add mtvs for every pi so that is and orders are visible in lists
const mtvs = generateMtvs(pis);

const displayPurchaseIntentionsTab: string = '[data-e2e="display_purchase_intentions"]';

describe('Purchase Intention List Tests', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task('preparePiRds', { objs: pis }, { timeout: 10000 });
    cy.task('prepareMtvRds', { objs: mtvs }, { timeout: 10000 });
    cy.visit('/lists/purchase-intentions');
  });

  afterEach(() => {
    cy.task('cleanupPiRds', {
      ids: pis.map((pi) => pi.purchase_intention_id),
    });
    cy.task('cleanupNcoRds', {
      ids: pis.map((pi) => pi.purchase_intention_id),
    });
    cy.task('cleanupMtvRds', { objs: mtvs });
  });

  it('Convert purchase intention to order (success case)', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');

    //Sort asc by pi ic
    cy.intercept('POST', `${piEndpointGetURL}`).as('fetchPis');
    cy.get('.ag-header').find('[col-id="purchase_intention_id"]').click();
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    //Click on convert on first item in list, save kcc and open modal
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="actions"]')
      .find(convertPurchaseIntentionBtn, { timeout: 20000 })
      .should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="actions"]').find(convertPurchaseIntentionBtn).click();
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find('body').should('not.be.undefined');
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find(saveKccBtn).click();
    cy.get('[data-e2e="convert_pi_modal"]', { timeout: 5000 }).should('exist').and('not.be.empty');

    //Check Modal Details and click on confirm
    cy.get('[data-e2e="SelectOrderType"]').should('be.visible');
    cy.get('[data-e2e="od_business_partner_id"]').should('be.visible');
    cy.get('[data-e2e="SelectOrderType"]').should('not.be.disabled');
    cy.get('[data-e2e="SelectShippingCode"]').should('not.be.disabled');
    cy.get('[data-e2e="SelectQuotaMonth"]').should('not.be.disabled');
    cy.get('[data-e2e="DeliveryDate"]').should('not.be.disabled');

    //Confirm, check api response with transaction info and wait until transaction was exported
    cy.intercept('PATCH', `${piEndpointURL}/${pis[0].purchase_intention_id}/convert`).as('convertPi');
    cy.get('[data-e2e="submit_order"]').click();
    cy.wait('@convertPi', { timeout: 10000 })
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //Check success Modal
    cy.get(`[data-e2e="${OneVmsEventKey.CONVERT_PI}_result_modal"]`, { timeout: 15000 })
      .should('exist')
      .and('not.be.empty');
    cy.get(`.header-success-${OneVmsEventKey.CONVERT_PI}`).should('be.visible');

    //Go to purchase intention list and check that converted PI is gone
    cy.get('[data-e2e="go_to_pi_btn"]').should('be.visible').should('not.be.disabled');
    cy.get('[data-e2e="go_to_pi_btn"]').click();

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    const newCarOrderId = pis[0].purchase_intention_id;
    cy.filterPIs(newCarOrderId);
    cy.wait('@fetchPis');
    cy.get('[row-index="0"]').should('not.exist');
  });

  it('Convert purchase intention to order (mocked err case)', () => {
    cy.get(displayPurchaseIntentionsTab, { timeout: 5000 }).should('be.visible');
    //Sort asc by pi ic
    cy.intercept('POST', `${piEndpointGetURL}`).as('fetchPis');
    cy.get('.ag-header').find('[col-id="purchase_intention_id"]').click();
    cy.filterPIs(`E2E${piPrefix}`);
    cy.wait('@fetchPis');

    //Click on convert on first item in list, save kcc and open modal
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]').find(convertPurchaseIntentionBtn, { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]').find(convertPurchaseIntentionBtn).click();
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find('body').should('not.be.undefined');
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find(saveKccBtn).click();
    cy.get('[data-e2e="convert_pi_modal"]', { timeout: 5000 }).should('exist').and('not.be.empty');

    //Check Modal Details and click on confirm
    cy.get('[data-e2e="SelectOrderType"]').should('be.visible');
    cy.get('[data-e2e="od_business_partner_id"]').should('be.visible');
    cy.get('[data-e2e="SelectOrderType"]').should('not.be.disabled');
    cy.get('[data-e2e="SelectShippingCode"]').should('not.be.disabled');
    cy.get('[data-e2e="SelectQuotaMonth"]').should('not.be.disabled');
    cy.get('[data-e2e="DeliveryDate"]').should('not.be.disabled');

    //Confirm and intercept cora api convert call to be able to let it fail
    cy.intercept('PATCH', `${piEndpointURL}/${pis[0].purchase_intention_id}/convert`, (req) => {
      req.body.configuration = undefined;
      console.log(req.body);
      req.continue();
    });
    cy.get('[data-e2e="submit_order"]').click();

    //Check fail Modal
    cy.get(`[data-e2e="${OneVmsEventKey.CONVERT_PI}_result_modal"]`, { timeout: 15000 })
      .should('exist')
      .and('not.be.empty');
    cy.get(`.header-error-${OneVmsEventKey.CONVERT_PI}`).should('be.visible');
    cy.get('p-inline-notification').should('be.visible');

    //Go to purchase intention list and check that converted PI is NOT gone
    cy.get('[data-e2e="close"]').should('be.visible').should('not.be.disabled');
    cy.get('[data-e2e="close"]').click();

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    const newCarOrderId = pis[0].purchase_intention_id;
    cy.filterPIs(newCarOrderId);
    cy.get('[row-index="0"]').should('exist');
  });
});
