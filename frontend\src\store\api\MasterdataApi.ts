import {
  CoraMdOneVmsStatusModel,
  CoraMdOneVmsStatusType,
  CoraMdOrderTypeResponseItem,
  CoraMdPortCodeResponseItem,
  CoraMdShippingCodeResponseItem,
} from '../../../../infrastructure/lib/types/masterdata-types';
import { InboundProcessMappingModel } from '../../../../infrastructure/lib/entities/inbound-mapping-model';
import { baseApi } from './BaseApi';
import { CoraMdDealerImporterApiResponse } from '../types';

const apiPrefix = 'masterdata';
const masterdataApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPortCodesForDealer: builder.query<CoraMdPortCodeResponseItem[], string>({
      query: (dealer) => ({ url: `${apiPrefix}/port-code`, params: { dealer_number: dealer } }),
    }),
    getOrderTypesForDealer: builder.query<CoraMdOrderTypeResponseItem[], string>({
      query: (dealer) => ({ url: `${apiPrefix}/order-type`, params: { dealer_number: dealer } }),
    }),
    getShippingCodesForDealer: builder.query<CoraMdShippingCodeResponseItem[], string>({
      query: (dealer) => ({ url: `${apiPrefix}/shipping-code`, params: { dealer_number: dealer } }),
    }),
    getImporterForDealer: builder.query<CoraMdDealerImporterApiResponse, string>({
      query: (dealer_number) => ({ url: `${apiPrefix}/oc-dealer-importer`, params: { dealer_number: dealer_number } }),
    }),
    getOnevmsStatus: builder.query<CoraMdOneVmsStatusModel[], undefined>({
      query: () => ({ url: `${apiPrefix}/onevms-status` }),
    }),

    getInboundStatusMapping: builder.query<InboundProcessMappingModel[], undefined>({
      query: () => ({ url: `${apiPrefix}/inbound-mapping` }),
    }),
  }),
});

export const {
  useGetPortCodesForDealerQuery,
  useGetOrderTypesForDealerQuery,
  useGetShippingCodesForDealerQuery,
  useGetImporterForDealerQuery,
  useLazyGetImporterForDealerQuery,
  useGetOnevmsStatusQuery,
  useGetInboundStatusMappingQuery,
} = masterdataApi;
