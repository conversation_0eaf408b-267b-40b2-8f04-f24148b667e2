import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createVerify } from 'crypto';
import { KccNCOConfigWindowMessagePayload } from '../../../lib/types/new-car-order-types';
import { JWK, importJWK, exportSPKI, KeyLike } from 'jose';

interface JWKSResponse {
  jwk: JWK[];
}
export class KccConfigSignatureVerifier {
  /**
   * Function which uses the KAS Auth token to fetch the public key
   */
  public static async fetchPublicKey(
    /**
     * KccPublicKeyEndpoint has an authorizer so we need the user token
     */
    kasAuthToken: string,
    stage: string,
    logger: KasLambdaLogger,
  ): Promise<JWK> {
    // fetch JWKS from KCC endpoint according to stage
    const baseUrl =
      stage === 'prod'
        ? 'https://kcc.cora.dpp.porsche.com/.well-known/jwks.json'
        : `https://kcc.cora-${stage}.dpp.porsche.com/.well-known/jwks.json`;

    const response = await fetch(baseUrl, {
      method: 'GET',
      headers: {
        Cookie: kasAuthToken,
        Accept: 'application/json',
      },
    });

    if (!response.ok) {
      const errMessage = `Error while fetching the JWK from KCC: ${response.status} - ${response.statusText}`;
      logger.log(LogLevel.WARN, errMessage, { data: response });
      throw new Error(errMessage);
    } else {
      logger.log(LogLevel.INFO, 'Successfully fetched JWK from KCC');
    }

    // Parse endpoint response and validate
    const jwkResponse = (await response.json()) as JWKSResponse;
    const jwkArray = jwkResponse.jwk;
    if (jwkArray.length === 0) {
      const errMessage = 'No public keys found in JWKS response';
      logger.log(LogLevel.WARN, errMessage, { data: jwkResponse });
      throw new Error(errMessage);
    }

    const jwk = jwkArray[0];

    return jwk;
  }

  /**
   * This function verifies the KCC Config signature of a NewCarOrder against the public key,
   * which is provided by the specific KCC Endpoint.
   * The public key is retrieved by transmitting the KAS Auth token of the user to the endpoint.
   */
  public static async verifySignature(
    kccConfigPayload: KccNCOConfigWindowMessagePayload,
    signature: string,
    cookieHeader: string | undefined,
    stage: string,
    logger: KasLambdaLogger,
  ): Promise<boolean> {
    try {
      // retrieve Auth token
      const kasAuthToken = this.getAuthorizationFromCookie(cookieHeader, stage);
      if (!kasAuthToken) {
        const errMessage = 'Authorization token not found in cookies';
        logger.log(LogLevel.WARN, errMessage);
        throw new Error(errMessage);
      }
      // Retrieve public key
      const publicKey = await this.fetchPublicKey(kasAuthToken, stage, logger);

      // Convert public key in pem format
      const convertedKey = (await importJWK(publicKey)) as KeyLike;
      const publicKeyPem = await exportSPKI(convertedKey);

      // Create verifier
      const verifier = createVerify('RSA-SHA256');
      const dataString = JSON.stringify(kccConfigPayload);

      // Ingest KCC window message and verify signature
      verifier.write(dataString);
      verifier.end();

      return verifier.verify(publicKeyPem, signature, 'base64');
    } catch (error) {
      logger.log(LogLevel.ERROR, 'Error in validation of the KCC Config signature', { data: error });
      return false;
    }
  }

  // retrieves Authorization cookie from browser cookies
  private static getAuthorizationFromCookie(cookieHeader: string | undefined, stage: string): string | undefined {
    if (!cookieHeader) {
      return undefined;
    }

    const authCookiePrefix = `KasAuthorization-${stage.toLowerCase()}`;
    const cookies = cookieHeader.split(';');

    for (const cookie of cookies) {
      const [name, value] = cookie.split('=');
      if (name.trim().startsWith(authCookiePrefix)) {
        return `${name.trim()}=${value.trim()}`;
      }
    }
    return undefined;
  }
}
