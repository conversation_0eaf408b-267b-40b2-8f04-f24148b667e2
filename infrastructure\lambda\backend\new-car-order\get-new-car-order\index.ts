import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand, QueryCommandOutput } from '@aws-sdk/lib-dynamodb';
import { Constants } from '../../../../lib/utils/constants';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { getPpnId, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert, OneVmsStatusConnector } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { secretCache } from '../../../utils/secret-cache';
import { CoraNCOGetApiResponse } from '../../../../lib/types/new-car-order-types';
import { ncoDbToApiObj } from '../../../utils/utils-typeorm';

// Initialize database client and table names
const dbClient = new DynamoDBClient({ region: process.env.AWS_REGION });
const cora_org_relationTableName = getEnvVarWithAssert('TABLE_NAME_COR');
const oneVmsStatusConnector = new OneVmsStatusConnector(dbClient, getEnvVarWithAssert('STATUS_MAPPING_TABLE_NAME'));
const stage = getEnvVarWithAssert('STAGE');

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const getNewCarOrderFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const ncoId = event.pathParameters ? event.pathParameters['ncoId'] : undefined;
  const regex = /^[A-Z0-9]{8,9}$/;
  if (!ncoId || !regex.test(ncoId.trim())) {
    const message = 'Could not get order, invalid id';
    logger.log(LogLevel.ERROR, message, { data: ncoId });
    return sendFail({ message: message, status: 400, reqHeaders: event.headers }, logger);
  }

  // Query the new-car-order table
  let nco: NewCarOrderModel | null = null;
  try {
    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
    ]);
    nco = await dataSource.manager.findOne(NewCarOrderModel, {
      where: { pk_new_car_order_id: ncoId },
      relations: ['configuration', 'configuration.ordered_options', 'configuration.ordered_options.content'],
    });
    logger.log(LogLevel.DEBUG, `Got db response for ncoId ${ncoId}`, { data: nco });
  } catch (error) {
    return sendFail(
      {
        message: `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`,
        status: 500,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  if (!nco) {
    logger.log(LogLevel.ERROR, `Could not find nco with ncoId ${ncoId}`);
    return sendFail(
      { message: 'Could not find order or access was denied', status: 404, reqHeaders: event.headers },
      logger,
    );
  }

  //check nco status, prevent GET on none editable order
  if (
    !(
      await oneVmsStatusConnector.getOneVmsStatus(
        { one_vms_status: nco.order_status_onevms_code, one_vms_error_status: nco.order_status_onevms_error_code },
        logger,
      )
    )?.order_changeable
  ) {
    logger.log(LogLevel.INFO, 'Order is in wrong status, not allowed to GET', { data: nco });
    return sendFail(
      { message: 'Could not find order or access was denied', status: 404, reqHeaders: event.headers },
      logger,
    );
  }

  // Check user's permissions
  const ppnId = getPpnId({ event: event }, logger);
  if (!ppnId) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: event });
    return sendFail({ message: 'An error occurred in the backend', status: 500, reqHeaders: event.headers }, logger);
  }
  const cora_org_relationOutput = await query(
    cora_org_relationTableName,
    Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
    Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk,
    ppnId,
    logger,
  );
  const dealerNumber = nco.dealer_number;
  if (cora_org_relationOutput.Items) {
    const cora_org_relationItems = cora_org_relationOutput.Items as CoraOrgRelModel[];
    const dealerNrs = new Set<string>();
    cora_org_relationItems.forEach(
      (cora_org_relation) => cora_org_relation.dealer_number && dealerNrs.add(cora_org_relation.dealer_number),
    );
    if (dealerNrs.has(dealerNumber)) {
      logger.log(LogLevel.INFO, `${ppnId} is allowed to view the order ${ncoId}`);

      // Add dealer name to the response
      const nco_response: CoraNCOGetApiResponse = {
        ...ncoDbToApiObj(nco),
        dealer_name: cora_org_relationItems.find(
          (cora_org_relation) => cora_org_relation.dealer_number === dealerNumber,
        )?.display_name,
      };

      return sendSuccess({ body: nco_response, reqHeaders: event.headers }, logger);
    } else {
      logger.log(LogLevel.INFO, `${ppnId} isn't allowed to view the order ${ncoId}`);
      return sendFail(
        { message: 'Could not find order or access was denied', status: 404, reqHeaders: event.headers },
        logger,
      );
    }
  } else {
    logger.log(
      LogLevel.ERROR,
      `No match by querying table ${cora_org_relationTableName} with parameters: key=${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk}; value=${ppnId}`,
    );
    return sendFail(
      { message: 'Could not find order or access was denied', status: 404, reqHeaders: event.headers },
      logger,
    );
  }
};

// Helper function to perform a DynamoDB query
async function query(
  tableName: string,
  indexName: string | undefined,
  key: string,
  value: string,
  logger: KasLambdaLogger,
): Promise<QueryCommandOutput> {
  const cmd = new QueryCommand({
    TableName: tableName,
    IndexName: indexName,
    KeyConditionExpression: `${key} = :value`,
    ExpressionAttributeValues: { ':value': value },
  });

  try {
    return await dbClient.send(cmd);
  } catch (error) {
    logger.log(LogLevel.ERROR, `Failed to query table ${tableName} with parameters: key=${key}; value=${value}`);
    throw error;
  }
}

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-new-car-order', LogLevel.TRACE)(event, context, getNewCarOrderFunc);
