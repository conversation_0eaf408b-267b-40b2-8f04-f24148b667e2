import { NotificationStatus, OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import { NOTIFICATION_CENTER_TRANSACTION_BASE_URL, USERNAME_WRITE } from '../support/constants';
import { generateMtvs, generateNewCarOrders } from '../support/order-lists-test-data';
import { checkTransactionResponse } from '../support/utils';

const displayOrdersTab = '[data-e2e="display_orders"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';

const ordersEndpointURL = '**/new-car-order**';

const ncoPrefixThisTest = 'CAN';
const preproductionOrders = generateNewCarOrders(ncoPrefixThisTest, 'PP2000', 3);
const preproductionOrderMtvs = generateMtvs(preproductionOrders);

const cancelOrderModal = `[data-e2e="${OneVmsEventKey.CANCEL}_order_modal"]`;
const cancelReasonSelect = '[data-e2e="SelectCancelReason"]';
const cancelConfirmBtn = '[data-e2e="accept"]';
const cancelOrderBtn = '[data-e2e="cancel_order"]';
const cancelMultipleBtn: string = '[data-e2e="cancel_orders"]';
const closeBtn = '[data-e2e="close"]';

const multiDetailsContainer = `[data-e2e="${OneVmsEventKey.CANCEL}_multi_details"]`;

describe('Order List Actions Cancel', () => {
  beforeEach(() => {
    cy.login(USERNAME_WRITE);
    cy.task('prepareMtvRds', { objs: preproductionOrderMtvs }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: preproductionOrderMtvs });
  });

  it('Cancel single order from list, success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //cancel first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(cancelOrderBtn, { timeout: 3000 }).should('be.visible').click();

    //cancel dialog test
    cy.get(cancelOrderModal).should('exist').and('not.be.empty');
    cy.get(cancelOrderModal).find('.display-details-single-container').should('be.visible');
    cy.get(cancelOrderModal).find(cancelConfirmBtn).shadow().find('[aria-disabled="true"]').should('exist');

    //wait for select to appear
    cy.get(cancelOrderModal).find(cancelReasonSelect, { timeout: 5000 }).should('be.visible');

    //select first cancel reason
    cy.get(cancelOrderModal, { timeout: 2000 }).selectOptionFromDropdown(cancelReasonSelect, 1);

    //confirm and check result (use real Cora API cancel call)
    cy.intercept('PATCH', `${ordersEndpointURL}/cancel`).as('cancelNco');
    cy.get(cancelOrderModal).find(cancelConfirmBtn).click();
    cy.wait('@cancelNco')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //close the dialog and check if table was updated correctly
    cy.get(cancelOrderModal).find(closeBtn).click();
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[1].pk_new_car_order_id);
  });

  it('Cancel multiple orders from list - success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //check table items, mark first 2 items and click multi cancel
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="1"]', { timeout: 10000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="1"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[1].pk_new_car_order_id);
    cy.get(cancelMultipleBtn).should('not.exist');
    cy.wait(1000);

    cy.get('[row-index="0"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.get('[row-index="1"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.get(cancelMultipleBtn, { timeout: 1000 }).should('exist');
    cy.get(cancelMultipleBtn).click({ force: true });

    cy.get(cancelOrderModal).should('exist').and('not.be.empty');
    cy.get(cancelOrderModal).find(multiDetailsContainer).should('be.visible');
    cy.get(cancelOrderModal).find(cancelConfirmBtn).shadow().find('[aria-disabled="true"]').should('exist');
    cy.get(cancelOrderModal).find(multiDetailsContainer).find('.col-container>.field-col').should('have.length', 2);
    cy.get(cancelOrderModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(0)
      .find('p-text')
      .should('have.length', 1);
    cy.get(cancelOrderModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(1)
      .find('p-text')
      .should('have.length', 1);

    //wait for select to appear
    cy.get(cancelOrderModal).find(cancelReasonSelect, { timeout: 5000 }).should('be.visible');

    //select first cancel reason and confirm
    cy.get(cancelOrderModal, { timeout: 2000 }).selectOptionFromDropdown(cancelReasonSelect, 1);

    cy.get(cancelOrderModal).find(cancelConfirmBtn).shadow().find('[aria-disabled="true"]').should('not.exist');

    //confirm and intercept cora api cancel call (let 1. order succeed and 2. fail)
    cy.intercept('PATCH', `${ordersEndpointURL}/cancel`).as('cancelNco');
    cy.get(cancelOrderModal).find(cancelConfirmBtn).click();
    cy.wait('@cancelNco')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //close the dialog (since call was mocked, do not check if table was updated)
    cy.get(cancelOrderModal).find(closeBtn).click();
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[2].pk_new_car_order_id);
  });

  it('Cancel single order from list with invalid request body, error case (mocked cora call)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //cancel first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 20000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(cancelOrderBtn, { timeout: 3000 }).should('be.visible').click();

    cy.get(cancelOrderModal).should('exist').and('not.be.empty');
    cy.get(cancelOrderModal).find('.display-details-single-container').should('be.visible');
    cy.get(cancelOrderModal).find(cancelConfirmBtn).shadow().find('[aria-disabled="true"]').should('exist');

    //wait for select to appear
    cy.get(cancelOrderModal).find(cancelReasonSelect, { timeout: 5000 }).should('be.visible');

    //select first cancel reason
    cy.get(cancelOrderModal, { timeout: 2000 }).selectOptionFromDropdown(cancelReasonSelect, 1);

    //confirm and intercept cora api cancel call to be able to let it fail
    cy.intercept('PATCH', `${ordersEndpointURL}/cancel`, (req) => {
      req.body.nco_ids_with_modified_at = [];
      req.continue();
    }).as('cancelNco');

    cy.get(cancelOrderModal).find(cancelConfirmBtn).click();
    cy.get(cancelOrderModal).find(`.header-error-${OneVmsEventKey.CANCEL}`, { timeout: 10000 }).should('be.visible');

    //close the dialog (since call was mocked, do not check if table was updated)
    cy.get(cancelOrderModal).find(closeBtn).click();
  });

  it('Cancel single order from list, error case (racecondition, mocked request)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //cancel first order
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 20000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(cancelOrderBtn, { timeout: 3000 }).should('be.visible').click();

    cy.get(cancelOrderModal).should('exist').and('not.be.empty');
    cy.get(cancelOrderModal).find('.display-details-single-container').should('be.visible');
    cy.get(cancelOrderModal).find(cancelConfirmBtn).shadow().find('[aria-disabled="true"]').should('exist');

    //wait for select to appear
    cy.get(cancelOrderModal).find(cancelReasonSelect, { timeout: 5000 }).should('be.visible');

    //select first cancel reason and confirm
    cy.get(cancelOrderModal, { timeout: 2000 }).selectOptionFromDropdown(cancelReasonSelect, 1);
    cy.get(cancelOrderModal).find(cancelConfirmBtn).shadow().find('[aria-disabled="true"]').should('not.exist');

    //confirm and intercept cora api cancel call (let 1. order succeed and 2. fail)
    cy.intercept('PATCH', `${ordersEndpointURL}/cancel`, (req) => {
      req.body.nco_ids_with_modified_at[0].modified_at = new Date(0).toISOString();
      req.continue();
    }).as('cancelNco');
    cy.get(cancelOrderModal).find(cancelConfirmBtn).click();
    cy.wait('@cancelNco')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.ERROR),
        );
      });

    //close the dialog (since call was mocked, do not check if table was updated)
    cy.get(cancelOrderModal).find(closeBtn).click();
  });
});
