import {
  <PERSON>fka<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>,
  KasDyna<PERSON>dbT<PERSON>,
  Kas<PERSON><PERSON><PERSON><PERSON>,
  Kas<PERSON>odejsFunction,
  KasSecret,
} from '@kas-resources/constructs';
import { aws_ssm, Duration, aws_ec2 as ec2, aws_lambda as lambda, RemovalPolicy, Stack } from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import { AuthenticationMethod, DynamoEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { Construct } from 'constructs';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaDefaultBundlingExternalModules } from '../utils/constants_cdk';
import { GlobalStack } from './global-stack';

export class BossSyncStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    //import global table key
    const coraGlobalDynamoKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalDynamoKmsKeyArnPName,
    );
    const coraGlobalDynamoKmsKey = KasKmsKey.fromKeyArn(this, 'globalDynamoKmsKey', coraGlobalDynamoKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const kafkaSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    // Kafka Parameters
    const _kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );

    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecretInterface', {
      encryptionKey: kafkaSecretKey,
      secretCompleteArn: _kafkaSecretArn,
    });

    //import boss org  stream arn
    const bossOrgTableStreamArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.bossOrgTableStreamArnPName,
    );

    const bossOrgDdbName = Constants.buildResourceName(props.stage, Constants.DYNAMODB_BOSS_ORG_TABLE_PARAMS.tableName);
    const coraOrgRelDdbName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName,
    );

    const bossOrgTable = KasDynamodbTable.fromTableAttributes(this, 'BossOrgTable', {
      tableName: bossOrgDdbName,
      encryptionKey: coraGlobalDynamoKmsKey,
      tableStreamArn: bossOrgTableStreamArn,
    });

    const coraOrgRelTable = KasDynamodbTable.fromTableAttributes(this, 'CoraOrgRelTable', {
      tableName: coraOrgRelDdbName,
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    // Retrieve all necessary security groups
    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    // Lambda Consumer for orgs
    const bossOrgsSyncLambdaName = `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-BossOrgsSync`;
    const groupId = `FRA_one_vms_cora_orgs_sync_consumer_group_${props.stage}_02`; // MUST be unique across stages
    const bossOrgsSyncLambda = new KafkaConsumerLambda(this, 'boss-orgs-sync-lambda', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      description: 'Cora Lambda to sync orgs from boss kafka to dynamodb',
      customManagedKey: logGroupKey,
      entry: 'lambda/backend/boss-orgs-sync/boss-org/index.ts',
      functionName: bossOrgsSyncLambdaName,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaImportGroup: groupId,
      kafkaImportTopic: props.kafkaParameters.bossOrgTopic,
      kafkaSecret: kafkaSecret,
      extraEnvVars: {
        TOPIC_BOSS_ORG: props.kafkaParameters.bossOrgTopic,
        TABLE_NAME_BOSS_ORG: bossOrgDdbName,
      },
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
      kafkaSecretKmsKeyAlias: Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SECRET_NAME),
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [kafkaSecurityGroup],
      stage: props.stage,
    });
    bossOrgTable.grantReadWriteData(bossOrgsSyncLambda);

    const coraOrgRelLambda = new KasNodejsFunction(this, 'BuildCoraOrgRel', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/boss-orgs-sync/cora-org-rel/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_BOSS_ORG: bossOrgDdbName,
        TABLE_NAME_CORA_ORG_REL: coraOrgRelDdbName,
        GROUP_ID: groupId,
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
        KAFKA_TOPIC: props.kafkaParameters.bossOrgTopic,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Build the flat child-parent org relationship',
      timeout: Duration.minutes(10),
      memorySize: 512,
      customManagedKey: logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-build-cora-org-rel`,
      errHandlingLambda: logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [kafkaSecurityGroup],
      stage: props.stage,
    });
    coraOrgRelTable.grantReadWriteData(coraOrgRelLambda);
    kafkaSecret.grantRead(coraOrgRelLambda);

    coraOrgRelLambda.addEventSource(
      new DynamoEventSource(bossOrgTable, {
        startingPosition: lambda.StartingPosition.TRIM_HORIZON,
      }),
    );
    // We need to scan and read stream from the bossOrgTable
    bossOrgTable.grantReadData(coraOrgRelLambda);

    coraOrgRelLambda.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`],
      }),
    );
    //do not use newCarOrderTable.grantStreamRead() as it created prisma findings
    coraOrgRelLambda.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToStreams',
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:DescribeStream',
          'dynamodb:GetRecords',
          'dynamodb:GetShardIterator',
          'dynamodb:ListStreams',
        ],
        resources: [
          `arn:aws:dynamodb:${Stack.of(this).region}:${Stack.of(this).account}:table/${
            bossOrgTable.tableName
          }/stream/*`,
        ],
      }),
    );
  }
}
