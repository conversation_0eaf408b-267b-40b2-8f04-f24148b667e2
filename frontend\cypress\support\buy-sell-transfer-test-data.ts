import { USER_WRITE_ORG_ID } from './constants';

export const buysell_dealer_from = {
  pk_importer_number: '4500000',
  sk_dealer_number: '3230000001',
  display_name: 'E2E - BuySell From',
  is_relevant_for_order_create: 'true',
  modified_at: '2025-04-09T14:45:28.203Z',
  modified_by: 'boss_sync',
  ppn_business_type: 'PORSCHE_DEALER',
  ppn_facility_type: 'PORSCHE_CENTER',
  ppn_id: 'something-unique-here-buysell-001',
  ppn_importer_org_id: '5fbfe220-392b-11dc-b460-dbce88e795aa',
  ppn_parent_id: '5fbfe220-392b-11dc-b460-dbce88e795aa',
  ppn_status: 'OPERATIVE',
};

export const buysell_dealer_to = {
  pk_importer_number: '4500000',
  sk_dealer_number: '3230000002',
  display_name: 'E2E - BuySell To',
  is_relevant_for_order_create: 'true',
  modified_at: '2025-04-09T14:45:28.203Z',
  modified_by: 'boss_sync',
  ppn_business_type: 'PORSCHE_DEALER',
  ppn_facility_type: 'PORSCHE_CENTER',
  ppn_id: 'something-unique-here-buysell-002',
  ppn_importer_org_id: '5fbfe220-392b-11dc-b460-dbce88e795aa',
  ppn_parent_id: '5fbfe220-392b-11dc-b460-dbce88e795aa',
  ppn_status: 'OPERATIVE',
};

export const buysell_org_rels = [
  {
    pk_ppn_id: buysell_dealer_to.ppn_id,
    parent_ppn_id: USER_WRITE_ORG_ID,
    dealer_number: buysell_dealer_to.sk_dealer_number,
    display_name: buysell_dealer_to.display_name,
    ppn_importer_org_id: buysell_dealer_from.ppn_importer_org_id,
    importer_number: buysell_dealer_to.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  {
    pk_ppn_id: buysell_dealer_from.ppn_id,
    parent_ppn_id: USER_WRITE_ORG_ID,
    dealer_number: buysell_dealer_from.sk_dealer_number,
    display_name: buysell_dealer_from.display_name,
    ppn_importer_org_id: buysell_dealer_from.ppn_importer_org_id,
    importer_number: buysell_dealer_from.pk_importer_number,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

export const buysell_orgRelPks = buysell_org_rels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

export const buysell_dealerPks = [buysell_dealer_from, buysell_dealer_to].map((org) => ({
  pk_importer_number: org.pk_importer_number,
  sk_dealer_number: org.sk_dealer_number,
}));
