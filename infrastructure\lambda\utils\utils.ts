import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { BatchGetCommand, GetCommand, GetCommandInput, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import assert from 'assert';
import { APIGatewayProxyResult, MSKRecord } from 'aws-lambda';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import {
  CoraMdDealer,
  CoraMdImporter,
  CoraMdMappingToOneVmsInvoiceStatus,
  CoraMdMappingToOneVmsStatus,
  CoraMdOneVmsStatus,
  CoraMdOrderType,
} from '../../lib/types/masterdata-types';
import { CoraNCOCopyApiRequest } from '../../lib/types/new-car-order-types';
import { NotificationKafkaEvent } from '../../lib/types/process-steering-types';
import { KafkaAdapter } from './kafka';
import { KafkaObjsWrapper, KafkaObjTyp } from '../backend/export-nco/types';

export const scanAllFromTable = async <T>(
  props: {
    tableName: string;
    dynamoDb: DynamoDBClient;
  },
  logger: KasLambdaLogger,
): Promise<T[]> => {
  logger.log(LogLevel.TRACE, 'scanAllFromTable input', { data: props });
  // Params for DynamoDB ScanCommand
  const params = {
    TableName: props.tableName,
  };
  // Try to retrieve data from DynamoDB
  const data = await props.dynamoDb.send(new ScanCommand(params));
  const res = data.Items as T[];
  logger.log(LogLevel.TRACE, 'scanAllFromTable output', { data: res });
  return res;
};

export const correlationHeader = (record: MSKRecord): string => {
  return record.headers.find((header) => Object.prototype.hasOwnProperty.call(header, 'ce_correlationid'))
    ? String.fromCharCode(
        ...record.headers.find((header) => Object.prototype.hasOwnProperty.call(header, 'ce_correlationid'))![
          'ce_correlationid'
        ],
      )
    : uuidv4();
};

export const getMdDealerImporter = async (
  props: {
    dynamoDb: DynamoDBClient;
    dlrTableName: string;
    impTableName: string;
    importerNumber: string;
    dealerNumber: string;
  },
  logger: KasLambdaLogger,
): Promise<{ dealer?: CoraMdDealer; importer?: CoraMdImporter }> => {
  logger.log(LogLevel.TRACE, 'getDealerImporter input', { data: props });
  const batchGetCmd = new BatchGetCommand({
    RequestItems: {
      [props.dlrTableName]: {
        Keys: [
          {
            pk_importer_number: props.importerNumber,
            sk_dealer_number: props.dealerNumber,
          },
        ],
      },
      [props.impTableName]: {
        Keys: [{ pk_importer_number: props.importerNumber }],
      },
    },
  });
  logger.log(LogLevel.TRACE, 'BatchGetCommand getDealerImporter', { data: batchGetCmd });
  const impDlrResult = await props.dynamoDb.send(batchGetCmd);
  const res = {
    dealer: impDlrResult.Responses?.[props.dlrTableName]?.[0] as CoraMdDealer,
    importer: impDlrResult.Responses?.[props.impTableName]?.[0] as CoraMdImporter,
  };
  logger.log(LogLevel.TRACE, 'getDealerImporter output', { data: res });
  return res;
};

export const getOrderType = async (
  dynamoDb: DynamoDBClient,
  logger: KasLambdaLogger,
  orderTypeTableName: string,
  orderType: string,
): Promise<CoraMdOrderType | null> => {
  const getCmd: GetCommandInput = {
    TableName: orderTypeTableName,
    Key: {
      pk_order_type: orderType,
    },
  };
  try {
    const getOtRes = await dynamoDb.send(new GetCommand(getCmd));
    if (!getOtRes.Item) {
      logger.log(LogLevel.WARN, `Could not find order type with code ${orderType}`);
      return null;
    } else {
      return getOtRes.Item as CoraMdOrderType;
    }
  } catch (error) {
    logger.log(LogLevel.WARN, `Failed to fetch old Order Type`, { data: { error: error, cmd: getCmd } });
    return null;
  }
};

export function splitIntoBatches<T>(array: T[], batchSize?: number): T[][] {
  const size = batchSize ? batchSize : 25;
  const batches: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    batches.push(array.slice(i, i + size));
  }
  return batches;
}

// Moved CreateNewCarOrderId here, because it will be needed in Convert in the future
function generateRandomAlphaNumericString(length: number, maxTries: number = 4): string {
  assert(Number.isInteger(length), 'generateRandomAlphaNumericString length input is not Integer');
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const numericCharacters = '0123456789';
  let result = '';
  let hasNonNumericChar = false;
  let tries = 0;

  while (!hasNonNumericChar && tries < maxTries) {
    result = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters.charAt(randomIndex);
    }

    // Check if the generated string contains at least one non-numeric character (chance to get only numbers is 0.0000256, so it shouldnt happen)
    // The last 5 Characters must NOT be only numeric! KASHEARTBE-854
    for (let i = length - 5; i < result.length; i++) {
      if (!numericCharacters.includes(result.charAt(i))) {
        hasNonNumericChar = true;
        break;
      }
    }

    tries++;
  }

  if (!hasNonNumericChar) {
    throw new Error(`Unable to generate a string with at least one non-numeric character after ${maxTries} tries.`);
  }

  return result;
}

export function generateNewCarOrderId(importerCode: string): string {
  const randomAlphaNumericString = generateRandomAlphaNumericString(6);
  return `${importerCode}${randomAlphaNumericString}`;
}

export function pvmsToIsoDate(expected_dealer_delivery_date: string | undefined): string | null {
  if (expected_dealer_delivery_date) {
    const date = new Date(expected_dealer_delivery_date);
    return date.toISOString().split('T')[0];
  } else {
    return null;
  }
}

export const pvmsToCoraQuota = (pvmsQuota: string | null): string | null => {
  return pvmsQuota ? `${pvmsQuota.slice(0, 4)}-${pvmsQuota.slice(4)}` : null;
};

export const streamToString = async (stream: Readable): Promise<string> => {
  const chunks: Buffer[] = [];
  return new Promise((resolve, reject) => {
    //eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
    stream.on('error', (err) => reject(err));
    // TODO fix and test, for now only removed error
    stream.on('end', () => resolve(Buffer.concat(chunks as unknown as Uint8Array[]).toString('utf8')));
  });
};

export const getStatusObjFromPvmsStatus = (
  props: {
    pvmsStatus: string;
    statusMappingData: CoraMdMappingToOneVmsStatus[];
    pvmsLockStatus?: string | null;
  },
  logger: KasLambdaLogger,
): CoraMdMappingToOneVmsStatus | undefined => {
  logger.log(LogLevel.TRACE, 'getStatusObjFromPvmsStatus input', { data: props });
  const statusMapping = props.statusMappingData.find((status) => {
    if (status.pvms_status === props.pvmsStatus) {
      if ((props.pvmsLockStatus === undefined || props.pvmsLockStatus === '') && !status.lock_reason) {
        return true;
      } else if (status.lock_reason === props.pvmsLockStatus) {
        return true;
      }
    }
    return false;
  });
  if (statusMapping) {
    const res = statusMapping;
    logger.log(LogLevel.TRACE, 'getStatusObjFromPvmsStatus output', { data: res });
    return res;
  }
  const res = undefined;
  logger.log(LogLevel.TRACE, 'getStatusObjFromPvmsStatus output (undefined)', { data: res });
  return res;
};

export const getMappedInvoiceStatus = (
  props: {
    pvmsInvoiceStatus: string;
    statusMappingData: CoraMdMappingToOneVmsInvoiceStatus[];
  },
  logger: KasLambdaLogger,
): CoraMdMappingToOneVmsInvoiceStatus | undefined => {
  logger.log(LogLevel.TRACE, 'getMappedInvoiceStatus input', { data: props });
  const statusMapping = props.statusMappingData.find((status) => {
    if (status.pvms_invoice_status === props.pvmsInvoiceStatus) {
      return true;
    }
    return false;
  });
  if (statusMapping) {
    const res = statusMapping;
    logger.log(LogLevel.TRACE, 'getMappedInvoiceStatus output', { data: res });
    return res;
  }
  const res = undefined;
  logger.log(LogLevel.TRACE, 'getMappedInvoiceStatus output (undefined)', { data: res });
  return res;
};

export class OneVmsStatusConnector {
  private readonly dynamoDbClient: DynamoDBClient;
  private readonly tableName: string;
  private readonly oneVmsStatusMap: Record<string, CoraMdOneVmsStatus | undefined | null>;

  public constructor(dynamoDbClient: DynamoDBClient, tableName: string) {
    this.dynamoDbClient = dynamoDbClient;
    this.tableName = tableName;
    this.oneVmsStatusMap = {};
  }

  public async getOneVmsStatus(
    props: {
      one_vms_status: string | null;
      one_vms_error_status: string | null;
    },
    logger: KasLambdaLogger,
  ): Promise<CoraMdOneVmsStatus | null> {
    logger.log(LogLevel.TRACE, 'getOneVmsStatus input', { data: props });
    // TODO invalidate cache after some time?
    const _tmp = this.oneVmsStatusMap[`${props.one_vms_status}-${props.one_vms_error_status}`];
    if (_tmp === undefined) {
      const res = await this.getOneVmsStatusFromDb(
        {
          one_vms_status: props.one_vms_status,
          one_vms_error_status: props.one_vms_error_status,
        },
        logger,
      );
      this.oneVmsStatusMap[`${props.one_vms_status}-${props.one_vms_error_status}`] = res;
      logger.log(LogLevel.TRACE, 'getOneVmsStatus output', { data: res });
      return res;
    } else {
      logger.log(LogLevel.TRACE, 'getOneVmsStatus output (cached)', { data: _tmp });
      return _tmp;
    }
  }

  public async getOneVmsStatuses(
    keys: { one_vms_status: string | null; one_vms_error_status: string | null }[],
    logger: KasLambdaLogger,
  ): Promise<Record<string, CoraMdOneVmsStatus | null>> {
    logger.log(LogLevel.TRACE, 'getOneVmsStatuses input', { data: keys });

    // Construct keys to query
    const uniqueKeys = new Set(keys.map((k) => `${k.one_vms_status}-${k.one_vms_error_status}`));

    // Check cache first
    const cachedResults: Record<string, CoraMdOneVmsStatus | null> = {};
    const keysToFetch: { one_vms_status: string; one_vms_error_status: string }[] = [];

    for (const key of uniqueKeys) {
      if (this.oneVmsStatusMap[key] === undefined) {
        const [one_vms_status, one_vms_error_status] = key.split('-');
        keysToFetch.push({ one_vms_status, one_vms_error_status });

        continue;
      }

      cachedResults[key] = this.oneVmsStatusMap[key] ?? null;
    }

    // If everything is cached, return early
    if (keysToFetch.length === 0) {
      logger.log(LogLevel.TRACE, 'getOneVmsStatuses output (cached)', { data: cachedResults });
      return cachedResults;
    }

    try {
      const batchGetCmd = new BatchGetCommand({
        RequestItems: {
          [this.tableName]: {
            Keys: keysToFetch.map((k) => ({
              one_vms_status: k.one_vms_status,
              one_vms_error_status: k.one_vms_error_status,
            })),
          },
        },
      });

      logger.log(LogLevel.DEBUG, 'getOneVmsStatuses BatchGetCommand', { data: batchGetCmd });

      const res = await this.dynamoDbClient.send(batchGetCmd);
      const items = res.Responses?.[this.tableName] ?? [];

      for (const item of items) {
        const key = `${item.one_vms_status}-${item.one_vms_error_status}`;
        cachedResults[key] = item as CoraMdOneVmsStatus;
        this.oneVmsStatusMap[key] = item as CoraMdOneVmsStatus;
      }

      logger.log(LogLevel.TRACE, 'getOneVmsStatuses output', { data: cachedResults });
      return cachedResults;
    } catch (error) {
      logger.log(LogLevel.ERROR, `Failed to batch query table ${this.tableName}`, { data: error });
      throw error;
    }
  }

  private async getOneVmsStatusFromDb(
    props: {
      one_vms_status: string | null;
      one_vms_error_status: string | null;
    },
    logger: KasLambdaLogger,
  ): Promise<CoraMdOneVmsStatus | null> {
    logger.log(LogLevel.TRACE, 'getOneVmsStatusFromDb input', { data: props });
    const cmd = new GetCommand({
      TableName: this.tableName,
      Key: {
        one_vms_status: props.one_vms_status ?? 'null',
        one_vms_error_status: props.one_vms_error_status ?? 'null',
      },
    });
    try {
      logger.log(LogLevel.DEBUG, 'getOneVmsStatus GetCommand', { data: cmd });
      const res = await this.dynamoDbClient.send(cmd);
      logger.log(LogLevel.TRACE, 'db response', { data: res });
      const result = (res.Item ?? null) as CoraMdOneVmsStatus | null;
      if (result?.one_vms_status === 'null') {
        result.one_vms_status = null;
      }
      if (result?.one_vms_error_status === 'null') {
        result.one_vms_error_status = null;
      }
      logger.log(LogLevel.TRACE, 'getOneVmsStatus output', { data: result });
      return result;
    } catch (error) {
      logger.log(
        LogLevel.ERROR,
        `Failed to query table ${this.tableName} with parameters: ${props.one_vms_status}, ${props.one_vms_error_status}`,
        { data: error },
      );
      throw error;
    }
  }
}

export function getEnvVarWithAssert(name: string): string {
  assert(process.env[name], `EnvVar ${name} is not set or empty!`);
  return process.env[name]!;
}

export function exponentialBackoff(attempt: number, baseDelay: number = 1000, maxDelay: number = 30000): number {
  const delay = baseDelay * Math.pow(2, attempt - 1);

  const randomNum = Math.random() * 1000;

  return Math.min(delay + randomNum, maxDelay);
}

export const validateCopyRequest = (
  quotas: CoraNCOCopyApiRequest,
  logger: KasLambdaLogger,
): { isValid: boolean; error?: APIGatewayProxyResult } => {
  const quotaMonths = Object.keys(quotas);
  const totalOrders = Object.values(quotas).reduce((sum, quota) => sum + quota, 0);

  // Check if the body is empty or has more than 6 quota months
  if (quotaMonths.length === 0 || quotaMonths.length > 6) {
    const errMessage = 'Error in provided quotas';
    logger.log(LogLevel.WARN, errMessage, { data: quotas });
    return {
      isValid: false,
      error: {
        statusCode: 400,
        body: JSON.stringify({ message: errMessage }),
      },
    };
  }

  // Check if the total orders exceed 100
  if (totalOrders > 100) {
    const errMessage = 'Cannot create more than 100 orders in a single request';
    logger.log(LogLevel.WARN, `${errMessage}. Requested amount: ${totalOrders}`);
    return {
      isValid: false,
      error: {
        statusCode: 400,
        body: JSON.stringify({ message: errMessage }),
      },
    };
  }

  // Validate quota month format
  for (const key of quotaMonths) {
    if (!/^\d{4}-(0[1-9]|1[0-2])$/.test(key)) {
      const errMessage = `Invalid quota month format: ${key}`;
      logger.log(LogLevel.WARN, errMessage, { data: quotas });
      return {
        isValid: false,
        error: {
          statusCode: 400,
          body: JSON.stringify({ message: errMessage }),
        },
      };
    }
  }
  return { isValid: true };
};

export const transformModifiedAt = (modifed_at: undefined | string | object): string => {
  if (modifed_at === undefined) {
    return '';
  }
  if (typeof modifed_at === 'string') {
    return modifed_at;
  }
  return (modifed_at as Date).toISOString();
};

export const getImporterByImporterNumbers = async (
  props: {
    dynamoDb: DynamoDBClient;
    impTableName: string;
    importerKeys: string[];
  },
  logger: KasLambdaLogger,
): Promise<CoraMdImporter[]> => {
  logger.log(LogLevel.TRACE, 'ByImporterNumbers input', { data: props });

  const chunkArray = <T>(array: T[], size: number): T[][] => {
    return Array.from({ length: Math.ceil(array.length / size) }, (_, i) => array.slice(i * size, i * size + size));
  };

  // Split `uniqueImpsPks` into chunks of 100
  const chunks = chunkArray(props.importerKeys, 100);

  const allImporters: CoraMdImporter[] = [];

  for (const chunk of chunks) {
    const cmd = new BatchGetCommand({
      RequestItems: {
        [props.impTableName]: {
          Keys: chunk.map((pk) => ({ pk_importer_number: pk })),
        },
      },
    });

    const dynamoResult = await props.dynamoDb.send(cmd);

    logger.log(LogLevel.TRACE, 'ByImporterNumbers batch result', { data: dynamoResult.Responses });
    const items = dynamoResult.Responses?.[props.impTableName] as CoraMdImporter[];
    allImporters.push(...items);
  }

  logger.log(LogLevel.TRACE, 'ByImporterNumbers all importers', { data: allImporters });
  return allImporters;
};

export const pushNotificationsToKafka = async (
  notificationEvents: NotificationKafkaEvent[],
  notificationTopic: string,
  kafkaAdapter: KafkaAdapter,
  logger: KasLambdaLogger,
): Promise<void> => {
  if (notificationEvents.length === 0) {
    logger.log(LogLevel.INFO, 'No Notification events to push to Kafka');
    return;
  }

  const kafkaObjsWrapperOneVms: KafkaObjsWrapper<NotificationKafkaEvent> = {
    kafkaObjs: notificationEvents.map((event) => ({
      id: event.sub_transaction_id,
      correlationId: event.transaction_id,
      obj: event,
    })),
    kafkaObjTyp: KafkaObjTyp.NOTIFICATION,
    kafkaActionTyp: notificationEvents[0].status,
  };

  try {
    logger.log(LogLevel.INFO, `Pushing to Notification topic (${notificationTopic})`, {
      data: kafkaObjsWrapperOneVms,
    });
    await kafkaAdapter.pushObjsToTopic({
      kWrapper: kafkaObjsWrapperOneVms,
      topic: notificationTopic,
      correlationid: notificationEvents[0].transaction_id,
    });
  } catch (error) {
    logger.log(LogLevel.ERROR, `Could not push Notification events to Kafka`, { data: error });
    throw error;
  }
};
