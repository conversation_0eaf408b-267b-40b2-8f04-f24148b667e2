import { <PERSON><PERSON> } from 'aws-lambda';
import { GeneralError, GeneralErrorProps } from '../../utils/errors';
import { RdsSecret, secretCache } from '../../utils/secret-cache';
import { Ka<PERSON><PERSON><PERSON>bdaLog<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { setupPgClientConnection } from '../../utils/pg-client';
import { getEnvVarWithAssert } from '../../utils/utils';
import { Constants } from '../../../lib/utils/constants';
import { createTypeORMDataSource } from '../../config/typeorm-config';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../lib/entities/new-car-order-model';
import { ModelTypeVisibilityModel } from '../../../lib/entities/model-type-visibility-model';
import { NewCarOrderAuditTrailModel } from '../../../lib/entities/new-car-order-audit-trail-model';
import { OneVmsStatusModel } from '../../../lib/entities/onevms-status-model';
import { CoraPurchaseIntentionModel } from '../../../lib/entities/purchase-intention-model';
import { FailedStatusMappingOrdersModel } from '../../../lib/entities/failed-status-mapping-orders-model';
import { InboundProcessMappingModel } from '../../../lib/entities/inbound-mapping-model';
import { OutboundProcessMappingModel } from '../../../lib/entities/outbound-mapping-model';
import { ModelTypeTextModel } from '../../../lib/entities/model-type-text-model';

const AURORA_ADMIN_SECRET_ARN = getEnvVarWithAssert('AURORA_ADMIN_SECRET_ARN');
const CORA_AURORA_READER_SECRET_ARN = getEnvVarWithAssert('CORA_AURORA_READER_SECRET_ARN');
const CORA_AURORA_WRITER_SECRET_ARN = getEnvVarWithAssert('CORA_AURORA_WRITER_SECRET_ARN');
const CORA_MD_AURORA_READER_SECRET_ARN = getEnvVarWithAssert('CORA_MD_AURORA_READER_SECRET_ARN');
const CORA_MD_AURORA_WRITER_SECRET_ARN = getEnvVarWithAssert('CORA_MD_AURORA_WRITER_SECRET_ARN');
const stage = getEnvVarWithAssert('STAGE');

secretCache.initCache(
  AURORA_ADMIN_SECRET_ARN,
  CORA_AURORA_READER_SECRET_ARN,
  CORA_AURORA_WRITER_SECRET_ARN,
  CORA_MD_AURORA_READER_SECRET_ARN,
  CORA_MD_AURORA_WRITER_SECRET_ARN,
);
const logger = new KasLambdaLogger('setup-global-aurora-db');

export const handler: Handler<Record<string, unknown>, void> = async (event) => {
  const rdsAdminSecret = await secretCache.getSecret<RdsSecret>(AURORA_ADMIN_SECRET_ARN);
  const coraRdsReaderSecret = await secretCache.getSecret<RdsSecret>(CORA_AURORA_READER_SECRET_ARN);
  const coraRdsWriterSecret = await secretCache.getSecret<RdsSecret>(CORA_AURORA_WRITER_SECRET_ARN);
  const coraMdRdsReaderSecret = await secretCache.getSecret<RdsSecret>(CORA_MD_AURORA_READER_SECRET_ARN);
  const coraMdRdsWriterSecret = await secretCache.getSecret<RdsSecret>(CORA_MD_AURORA_WRITER_SECRET_ARN);

  if (event.manualSynchronizeTypeormModels !== 'true') {
    const pgClient = await setupPgClientConnection(rdsAdminSecret, logger);
    if (!pgClient) {
      logger.log(LogLevel.ERROR, 'Connection to database could not be established successfully.');
      throw new GeneralError();
    }

    let initialQuery;

    if (stage.toLocaleLowerCase() === 'prod') {
      // IMPORTANT: Adjust to ALTER USER after first deployment to prod, else this lambda will always fail.
      initialQuery = `
        CREATE USER ${coraRdsReaderSecret.username} WITH encrypted password '${coraRdsReaderSecret.password}';
        CREATE USER ${coraRdsWriterSecret.username} WITH encrypted password '${coraRdsWriterSecret.password}';
        CREATE USER ${coraMdRdsReaderSecret.username} WITH encrypted password '${coraMdRdsReaderSecret.password}';
        CREATE USER ${coraMdRdsWriterSecret.username} WITH encrypted password '${coraMdRdsWriterSecret.password}';
      `;
    } else if (stage.toLocaleLowerCase() === 'int') {
      initialQuery = `
        ALTER USER ${coraRdsReaderSecret.username} WITH PASSWORD '${coraRdsReaderSecret.password}';
        ALTER USER ${coraRdsWriterSecret.username} WITH PASSWORD '${coraRdsWriterSecret.password}';
        ALTER USER ${coraMdRdsReaderSecret.username} WITH PASSWORD '${coraMdRdsReaderSecret.password}';
        ALTER USER ${coraMdRdsWriterSecret.username} WITH PASSWORD '${coraMdRdsWriterSecret.password}';
      `;
    } else {
      initialQuery = `
        ALTER USER ${coraRdsReaderSecret.username} WITH PASSWORD '${coraRdsReaderSecret.password}';
        ALTER USER ${coraRdsWriterSecret.username} WITH PASSWORD '${coraRdsWriterSecret.password}';
        ALTER USER ${coraMdRdsReaderSecret.username} WITH PASSWORD '${coraMdRdsReaderSecret.password}';
        ALTER USER ${coraMdRdsWriterSecret.username} WITH PASSWORD '${coraMdRdsWriterSecret.password}';
      `;
    }

    try {
      const res = await pgClient.query(initialQuery);
      logger.log(LogLevel.INFO, `Successfully created users in DB`, { data: JSON.stringify(res) });
    } catch (error) {
      logger.log(LogLevel.ERROR, `Could not create users in DB`, { data: error });
      const errProps: GeneralErrorProps = {
        message: `Could not create user(s) in DB`,
        causedBy: error,
      };
      throw new GeneralError(errProps);
    }

    // 2. Grant according user permissions for all tables
    try {
      const res = await pgClient.query(`
        CREATE SCHEMA IF NOT EXISTS ${Constants.CORA_MD_AURORA_SCHEMA};
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        GRANT SELECT ON ALL TABLES IN SCHEMA public TO ${coraRdsReaderSecret.username};
        GRANT SELECT ON ALL TABLES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA} TO ${coraRdsReaderSecret.username};
        GRANT SELECT ON ALL TABLES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA} TO ${coraMdRdsReaderSecret.username};
        GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO ${coraRdsWriterSecret.username};
        GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA} TO ${coraMdRdsWriterSecret.username};
        GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO ${coraRdsReaderSecret.username};
        GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA} TO ${coraMdRdsReaderSecret.username};
        GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA public TO ${coraRdsWriterSecret.username};
        GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA} TO ${coraMdRdsWriterSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA public
        GRANT SELECT ON TABLES TO ${coraRdsReaderSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA}
        GRANT SELECT ON TABLES TO ${coraRdsReaderSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA}
        GRANT SELECT ON TABLES TO ${coraMdRdsReaderSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA public
        GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO ${coraRdsWriterSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA}
        GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO ${coraMdRdsWriterSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA public
        GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO ${coraRdsWriterSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA public
        GRANT USAGE, SELECT ON SEQUENCES TO ${coraRdsReaderSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA}
        GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO ${coraMdRdsWriterSecret.username};
        ALTER DEFAULT PRIVILEGES IN SCHEMA ${Constants.CORA_MD_AURORA_SCHEMA}
        GRANT USAGE, SELECT ON SEQUENCES TO ${coraMdRdsReaderSecret.username};
    `);

      logger.log(LogLevel.INFO, `Successfully granted permissions to users`, { data: JSON.stringify(res) });
    } catch (error) {
      logger.log(LogLevel.ERROR, `Could not grant permissions to users`, { data: error });
      const errProps: GeneralErrorProps = {
        message: `Could not grant permissions to user(s)`,
        causedBy: error,
      };
      throw new GeneralError(errProps);
    }

    await pgClient.end();
  } else {
    // used 'dev' to set shouldSynchronize=true
    await createTypeORMDataSource(logger, AURORA_ADMIN_SECRET_ARN, 'dev', [
      NewCarOrderModel,
      OneVmsStatusModel,
      CoraPurchaseIntentionModel,
      FailedStatusMappingOrdersModel,
      ModelTypeVisibilityModel,
      ModelTypeTextModel,
      NewCarOrderAuditTrailModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      InboundProcessMappingModel,
      OutboundProcessMappingModel,
    ]);
  }
};
