import { convert, DateTimeFormatter, ZonedDateTime } from '@js-joda/core';
import { format } from 'date-fns';

export const dS = (d: Date): string => format(d, 'yyyy-MM-dd');
const df = DateTimeFormatter.ofPattern('dd/MM/yyyy:HH:mm:ss Z');

export const parseTime = (date?: string | Date | null): Date | undefined => {
  if (!date) {
    return undefined;
  }
  if (typeof date === 'object') {
    return date;
  }
  if (Number.isInteger(Number(date))) {
    return new Date(parseInt(date));
  }
  if (date.length === 10 || date.indexOf('T') > 0) {
    return new Date(date);
  }
  try {
    const temporal = ZonedDateTime.parse(
      date
        .replace('Jan', '01')
        .replace('Feb', '02')
        .replace('Mar', '03')
        .replace('Apr', '04')
        .replace('May', '05')
        .replace('Jun', '06')
        .replace('Jul', '07')
        .replace('Aug', '08')
        .replace('Sep', '09')
        .replace('Oct', '10')
        .replace('Nov', '11')
        .replace('Dec', '12'),
      df,
    );
    return convert(temporal.toInstant()).toDate();
  } catch (e) {
    return new Date(0);
  }
};

export const getDate = (date?: string | Date): Date => {
  if (date && date !== '' && typeof date === 'string') {
    return new Date(Date.parse(date));
  }
  return new Date('1970-01-01');
};

export const timeDeltaToHHmmss = (delta: number): string => {
  const _ms = delta % 1000;
  delta = (delta - _ms) / 1000;
  const ss = delta % 60;
  delta = (delta - ss) / 60;
  const mm = delta % 60;
  delta = (delta - mm) / 60;
  const HH = delta;
  return `${String(HH).padStart(2, '0')}:${String(mm).padStart(2, '0')}:${String(ss).padStart(2, '0')}`;
};

export const isIsoStringSameDate = (s_d1?: string | null, s_d2?: string | null): boolean => {
  if (!s_d1 || !s_d2) return false;
  const d1 = new Date(s_d1);
  const d2 = new Date(s_d2);
  return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
};
export function getNextSixMonths(): string[] {
  const date = new Date();
  return getNextSixMonthsOfGivenDate(date.toISOString());
}
export const getNextSixMonthsOfGivenDate = (beginningOfThisMonth: string): string[] => {
  return Array.from({ length: 6 }, (_, i) => quotaMonthNMonthFromNow(i, beginningOfThisMonth));
};

export const quotaMonthNMonthFromNow = (n: number, beginning: string): string => {
  const beginningOfThisMonth = new Date(beginning);
  beginningOfThisMonth.setUTCDate(1); // Ensures no end-of-month issues
  beginningOfThisMonth.setUTCHours(8, 0, 0, 0); // Prevent DST issues
  beginningOfThisMonth.setMonth(beginningOfThisMonth.getMonth() + n);
  return beginningOfThisMonth.toISOString().slice(0, 7);
};
