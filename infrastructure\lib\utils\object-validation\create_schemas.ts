import { Config, Schema, createGenerator } from 'ts-json-schema-generator';
import fs from 'fs';
import path from 'path';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import standaloneCode from 'ajv/dist/standalone/index.js';
import assert from 'assert';
import esbuild from 'esbuild';
import { execSync } from 'child_process';

// ESM
// import { fileURLToPath } from 'url';
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);

function typeScriptToJsonSchema(typeFilePath: string, destDir: string): Schema[] {
  const config: Config = {
    path: typeFilePath,
    type: '*',
  };

  const schemas = [];
  console.time('* TS TO JSONSCHEMA');
  const schemaRaw = createGenerator(config).createSchema(config.type);
  console.timeEnd('* TS TO JSONSCHEMA');

  /* Remove all `#/definitions/` so that we can use the Type name as the $id and have matching $refs with the other Types */
  const schema = JSON.parse(JSON.stringify(schemaRaw).replace(/#\/definitions\//gm, '')) as Schema;

  assert(schema.definitions);
  /* Save each Type jsonschema individually, use the Type name as $id */
  for (const [id, definition] of Object.entries(schema.definitions)) {
    const singleTypeDefinition: Schema = {
      $id: id,
      $schema: 'http://json-schema.org/draft-07/schema#',
      ...(definition as Schema),
    };
    schemas.push(singleTypeDefinition);
    fs.writeFileSync(path.join(destDir, `${id}.json`), JSON.stringify(singleTypeDefinition, null, 2));
  }

  return schemas;
}

function compileAjvStandalone(schemas: Schema[], validationFile: string): void {
  console.time('* AJV COMPILE');
  const ajv = new Ajv({ schemas: schemas, code: { source: true, esm: true }, removeAdditional: true });
  addFormats(ajv);
  const moduleCode = standaloneCode(ajv);
  console.timeEnd('* AJV COMPILE');
  fs.writeFileSync(validationFile, moduleCode);
}

function esBuildCommonToEsm(validationFile: string): void {
  console.time('* ES BUILD');
  esbuild.buildSync({
    // minify: true,
    bundle: true,
    target: ['node20'],
    keepNames: true,
    platform: 'node',
    format: 'esm',
    entryPoints: [validationFile],
    outfile: validationFile,
    allowOverwrite: true,
    external: ['pg'],
  });
  console.timeEnd('* ES BUILD');
}

function generateTypings(validationFile: string, validationFileFolder: string, esm?: boolean): void {
  console.time('* TSC DECLARATIONS');
  const tmpFolder = path.join(validationFileFolder, 'tmp');
  // Cannot use in place generation of types with --emitDeclartionOnly, because jest cannot use that .js file
  if (!esm) {
    execSync(`tsc --allowJs --declaration "${validationFile}" --outDir "${tmpFolder}"`);
  } else {
    execSync(
      // ESM:
      `tsc --module esnext --target esnext --moduleResolution node --allowJs --declaration "${validationFile}" --outDir "${tmpFolder}"`,
    );
  }
  execSync(`mv ${tmpFolder}/* ${validationFileFolder}/ && rmdir ${tmpFolder}`);
  console.timeEnd('* TSC DECLARATIONS');
}

/**
 * Makes sure dir exists and deletes files in directory (does not delete folders, no recursion)
 */
function prepareDirectory(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
  }
  const dirContents = fs.readdirSync(dir);
  for (const fileOrDirPath of dirContents) {
    const fullPath = path.join(dir, fileOrDirPath);
    const stat = fs.statSync(fullPath);
    if (stat.isFile()) {
      fs.unlinkSync(fullPath);
    }
  }
}

function buildTypes(extraPath?: string, esm?: boolean): void {
  const outputDir = path.join(__dirname, extraPath ?? '');
  if (extraPath) {
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }
  }
  const paths = {
    types: path.join(__dirname, '../../types/*.ts'),
    typesJsonSchema: path.join(outputDir, 'schemas'),
    validationFile: path.join(outputDir, 'schemas/validations.js'),
  };

  /* Clear the output dir for the AJV validation code, definition and JSON Schema definitions */
  prepareDirectory(paths.typesJsonSchema);

  /* Create the JSON Schema files from the TS Types and save them as individual JSON Schema files */
  const schemas = typeScriptToJsonSchema(paths.types, paths.typesJsonSchema);

  /* Create the AJV validation code in ESM format from the JSON Schema files */
  compileAjvStandalone(schemas, paths.validationFile);

  /* Bundle the AJV validation code file in ESM format */
  esBuildCommonToEsm(paths.validationFile);

  /* Create TypeScript typings for the generated AJV validation code */
  generateTypings(paths.validationFile, paths.typesJsonSchema, esm);
}

buildTypes();
// Frontend needs ESM Types
buildTypes('frontend', true);
