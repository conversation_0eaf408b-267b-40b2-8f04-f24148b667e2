.header-success-update-nco-core {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.header-success-update-nco-core p-icon {
  padding-right: 5px;
}

.header-error-update-nco-core {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.header-error-update-nco-core p-icon {
  padding-right: 5px;
}

.update-order-form-container {
  padding-bottom: 30px;
}

.update-order-footer-btn-container {
  display: flex;
  justify-content: flex-start;
}

.update-order-footer-btn-container p-button {
  padding-right: 10px;
}

.update-order-details-single-container {
  display: flex;
  flex-direction: row;
}

.update-order-details-single-container .field-col {
  display: flex;
  flex-grow: 10;
  flex-direction: column;
}

.update-order-details-single-container .field-row > p-text:nth-child(2) {
  padding-bottom: 7px;
}
