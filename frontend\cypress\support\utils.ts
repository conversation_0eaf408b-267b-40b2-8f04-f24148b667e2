import {
  NotificationTransactionResponseItem,
  SubTransaction,
} from '../../../infrastructure/lib/types/notification-center-types';
import { NotificationStatus } from '../../../infrastructure/lib/types/process-steering-types';

export function checkTransactionResponse(
  response: Cypress.Response<unknown>,
  notificationStatus: NotificationStatus,
): boolean {
  const groupedById = fetchTransactionsGrouped(response);
  if (groupedById === null) return false;

  return Object.values(groupedById).every((subTxArray) =>
    subTxArray.some((item) => item.status === notificationStatus),
  );
}

export function fetchTransactionsGrouped(response: Cypress.Response<unknown>) {
  const body = response.body as NotificationTransactionResponseItem;
  Cypress.log({
    name: 'Transaction response',
    message: JSON.stringify({ code: response.status, body: response.body }),
  });
  if (!body) return null;
  const sub_transactions = body.sub_transactions;
  if (!sub_transactions) return null;

  // Response contains all notifications for a subtransaction
  return sub_transactions.reduce(
    (acc, item) => {
      if (!acc[item.sub_transaction_id]) {
        acc[item.sub_transaction_id] = [];
      }
      acc[item.sub_transaction_id].push(item);
      return acc;
    },
    {} as Record<string, Omit<SubTransaction, 'transaction_id'>[]>,
  );
}

export function checkTransactionResponseHasAnyStatus(
  groupedById: Record<string, Omit<SubTransaction, 'transaction_id'>[]> | null,
  notificationStatus: NotificationStatus,
): boolean {
  if (groupedById === null) return false;
  return Object.values(groupedById).every((subTxArray) =>
    subTxArray.some((item) => item.status === notificationStatus),
  );
}

export function getTransactionResponseObjectId(
  groupedById: Record<string, Omit<SubTransaction, 'transaction_id'>[]> | null,
  notificationStatus: NotificationStatus,
) {
  if (groupedById === null) return null;
  for (const key of Object.keys(groupedById)) {
    const found = groupedById[key].find((item) => item.status === notificationStatus);
    if (found) {
      Cypress.log({
        name: 'Transaction response',
        message: JSON.stringify({ id: found.obj_id, body: found }),
      });
      return found.obj_id;
    }
  }
  return null;
}
