import { Construct } from 'constructs';
import { RestApi, LambdaIntegration, IAuthorizer, IResource } from 'aws-cdk-lib/aws-apigateway';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { RemovalPolicy, aws_iam, Duration, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { KasNodejsFunction, KasKmsKey, KasStage } from '@kas-resources/constructs';
import { Constants } from '../utils/constants';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export interface OrderListEndpointConstructProps {
  api: RestApi;
  authorizer: IAuthorizer;
  stage: KasStage;
  logGroupKey: KasKmsKey;
  newCarOrderListsTable: ITable;
  coraOrgRelTable: ITable;
  coraMdStatusMappingTable: ITable;
  globalDynamoKmsKey: KasKmsKey;
  corsDomain: string;
  parentResource: IResource;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  auroraRdsSecret: ISecret;
  typeormLayer: ILayerVersion;
}

export class OrderListEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: OrderListEndpointConstructProps) {
    super(scope, id);

    const ncoResource = props.parentResource.addResource('new-car-order');

    // POST /lists/new-car-order/get
    const getNewCarOrders = new KasNodejsFunction(this, 'GetNewCarOrderList', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/fetch-orders/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_LISTS: props.newCarOrderListsTable.tableName,
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        APPLICATION_NAME_TO_AUTHORIZE: Constants.APPLICATION_SHORT_NAME,
        TABLE_NAME_STATUS_MAPPING: props.coraMdStatusMappingTable.tableName,
        AURORA_SECRET_ARN: props.auroraRdsSecret.secretArn,
        STAGE: props.stage,
      },
      description: 'Get all editable new car orders',
      customManagedKey: props.logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-get-order-list`,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      timeout: Duration.minutes(10),
      memorySize: 1024,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      layers: [props.typeormLayer],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      stage: props.stage,
    });

    props.coraOrgRelTable.grantReadData(getNewCarOrders);
    getNewCarOrders.addToRolePolicy(
      new aws_iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: aws_iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${props.coraOrgRelTable.tableArn}/index/${Constants.CORA_ORG_PERMISSON_CHECK_PARAMS.pPIDIndex}`],
      }),
    );

    props.newCarOrderListsTable.grantReadData(getNewCarOrders);

    props.coraMdStatusMappingTable.grantReadData(getNewCarOrders);

    props.auroraRdsSecret.grantRead(getNewCarOrders);

    const getNewCarOrdersLambdaIntegration = new LambdaIntegration(getNewCarOrders);
    ncoResource.addMethod('GET', getNewCarOrdersLambdaIntegration, {
      authorizer: props.authorizer,
    });
    const ncoGetResource = ncoResource.addResource('get');
    ncoGetResource.addMethod('POST', getNewCarOrdersLambdaIntegration, {
      authorizer: props.authorizer,
    });
  }
}
