import { Kas<PERSON><PERSON><PERSON><PERSON>, Kas<PERSON><PERSON><PERSON><PERSON><PERSON>unction, KasSqsQueue, KasStage } from '@kas-resources/constructs';
import { Duration, aws_ec2 as ec2, RemovalPolicy } from 'aws-cdk-lib';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';
import { OneVmsEventKey } from '../types/process-steering-types';
import { Constants } from '../utils/constants';
import { ConstantsCdk } from '../utils/constants_cdk';

export interface ProcessSteeringEventApiConstructProps {
  applicationNameToAuthorize: string;
  commonEnvVars?: Record<string, string>;
  dispatcherQueue: KasSqsQueue;
  eventKey: OneVmsEventKey;
  kafkaParameters: {
    brokers: string[];
    ncoNotificationTopic: string;
    kafkaSecret: ISecret;
  };
  kafkaSecurityGroup: ec2.ISecurityGroup;
  logGroupKey: KasKmsKey;
  logSubscriptionLambda: IFunction;
  stage: KasStage;
  vpc: ec2.IVpc;
  additionalSecurityGroups?: ec2.ISecurityGroup[];
}

/**
 * CDK Construct for Process Steering API Lambda handlers.
 *
 * This construct creates and configures an API Lambda function from the `OneVmsEventKey` ENUM with the following features:
 * - Automatically configures environment variables and permissions
 * - Sets up vpc specifications with required security group
 * - Sets up Kafka connectivity with the provided security configuration
 * - Configures compliant lambda environment and runtime specifications
 * - Grants necessary permissions for SQS dispatcher queue export
 *
 */
export class ProcessSteeringEventApiConstruct extends Construct {
  public readonly eventApiHandler: KasNodejsFunction;
  public readonly eventApiKey: OneVmsEventKey;

  public constructor(scope: Construct, id: string, props: ProcessSteeringEventApiConstructProps) {
    super(scope, id);
    this.eventApiKey = props.eventKey;

    const environment = {
      APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
      EVENT_KEY: props.eventKey,
      DISPATCHER_QUEUE_URL: props.dispatcherQueue.queueUrl,
      KAFKA_SECRET_ARN: props.kafkaParameters.kafkaSecret.secretArn,
      KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
      KAFKA_TOPIC_NOTIFICATION: props.kafkaParameters.ncoNotificationTopic,
      ...props.commonEnvVars,
    };

    const apiHandler = new KasNodejsFunction(this, `EventApi-${props.eventKey}`, {
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-event-api-${props.eventKey}`,
      entry: `lambda/backend/process-steering/event-api/${props.eventKey}/index.ts`,
      handler: 'handler',
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [
        props.kafkaSecurityGroup,
        ...(props.additionalSecurityGroups ? props.additionalSecurityGroups : []),
      ],
      description: `Handle ${props.eventKey} api events and send to dispatcher queue.`,
      customManagedKey: props.logGroupKey,
      environment,
      stage: props.stage,
      timeout: Duration.seconds(30),
      memorySize: 128,
      runtime: ConstantsCdk.NODE_JS_VERSION,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: props.logSubscriptionLambda,
    });

    props.dispatcherQueue.grantSendMessages(apiHandler);
    props.kafkaParameters.kafkaSecret.grantRead(apiHandler);
    this.eventApiHandler = apiHandler;
  }
}
